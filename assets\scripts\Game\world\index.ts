/**
 * World System Index
 * 
 * This file exports all components of the World mechanism for easy importing
 * throughout the game codebase.
 */

// Base system components
export { System } from "./base/System";
export { SystemContainer } from "./base/SystemContainer";
export type { SystemUpdateFn, SystemLateUpdateFn } from "./base/SystemContainer";
export { TypeID, RegisterTypeID, TypedBase } from "./base/TypeID";
export type { ITyped } from "./base/TypeID";
export { TypeIDUtils } from "./base/TypeID";

// World core
export { World, WorldState } from "./base/World";

// World initialization data
export {
    WorldInitializeData,
    GameMode,
    DifficultyLevel
} from "./WorldInitializeData";
export type {
    IWorldInitializeData,
    WorldPhysicsConfig,
    WorldRenderConfig,
    PlayerConfig,
    LevelConfig,
    DebugFlags
} from "./WorldInitializeData";

// Bootstrap component
export { Bootstrap } from "./Bootstrap";

// Example systems
export { BulletSystem } from "./bullet/BulletSystem";
export type { BulletData, BulletConfig } from "./bullet/BulletSystem";

export { LevelSystem, LevelEventType } from "./level/LevelSystem";
export type { 
    LevelEvent, 
    Checkpoint, 
    LevelObjective, 
    LevelConfig as LevelSystemConfig 
} from "./level/LevelSystem";

export { PlayerSystem, PlayerState } from "./player/PlayerSystem";
export type { 
    PlayerStats, 
    PlayerInput, 
    PlayerData 
} from "./player/PlayerSystem";

/**
 * Version information for the World system
 */
export const WORLD_SYSTEM_VERSION = "1.0.0";

/**
 * Quick start helper function to create a basic world setup
 * @param levelId The level ID to initialize
 * @param gameMode The game mode (defaults to STORY)
 * @param difficulty The difficulty level (defaults to NORMAL)
 * @returns WorldInitializeData configured with the provided parameters
 */
export function createQuickWorldSetup(
    levelId: string,
    gameMode: GameMode = GameMode.STORY,
    difficulty: DifficultyLevel = DifficultyLevel.NORMAL
): WorldInitializeData {
    return new WorldInitializeData({
        modeId: gameMode,
        levelId: levelId,
        difficulty: difficulty,
        randomSeed: Date.now()
    });
}

/**
 * Helper function to create a world with common systems pre-registered
 * @returns A new World instance with BulletSystem, LevelSystem, and PlayerSystem registered
 */
export function createWorldWithCommonSystems(): World {
    const world = new World();

    // Register common systems
    world.registerSystem(new BulletSystem());
    world.registerSystem(new LevelSystem());
    world.registerSystem(new PlayerSystem());

    return world;
}

/**
 * Example usage documentation
 * 
 * Basic usage:
 * ```typescript
 * import { Bootstrap, createQuickWorldSetup } from "./Game/world";
 * 
 * // Add Bootstrap component to a node in the scene
 * const bootstrap = node.addComponent(Bootstrap);
 * bootstrap.levelId = "level_001";
 * bootstrap.gameMode = GameMode.STORY;
 * bootstrap.autoStart = true;
 * ```
 * 
 * Advanced usage:
 * ```typescript
 * import {
 *     World,
 *     WorldInitializeData,
 *     GameMode,
 *     BulletSystem,
 *     LevelSystem,
 *     PlayerSystem
 * } from "./Game/world";
 *
 * // Create world manually
 * const world = new World();
 *
 * // Register systems
 * world.registerSystem(new BulletSystem());
 * world.registerSystem(new LevelSystem());
 * world.registerSystem(new PlayerSystem());
 *
 * // Initialize world
 * const initData = new WorldInitializeData({
 *     levelId: "custom_level",
 *     modeId: GameMode.CUSTOM
 * });
 *
 * world.initialize(initData).then(success => {
 *     if (success) {
 *         console.log("World initialized successfully");
 *
 *         // Get systems using TypeID
 *         const bulletSystem = world.getSystem(BulletSystem);
 *         const playerSystem = world.getSystem(PlayerSystem);
 *
 *         // Start game loop
 *         const updateLoop = (deltaTime: number) => {
 *             world.update(deltaTime);
 *             world.lateUpdate(deltaTime);
 *         };
 *     }
 * });
 * ```
 * 
 * Custom system creation:
 * ```typescript
 * import { System } from "./Game/world";
 * 
 * class CustomSystem extends System {
 *     getSystemName(): string {
 *         return "CustomSystem";
 *     }
 *     
 *     protected onInit(): void {
 *         console.log("Custom system initialized");
 *     }
 *     
 *     protected onUnInit(): void {
 *         console.log("Custom system cleaned up");
 *     }
 *     
 *     protected onUpdate(deltaTime: number): void {
 *         // Custom update logic
 *     }
 *     
 *     protected onLateUpdate(deltaTime: number): void {
 *         // Custom late update logic
 *     }
 * }
 * 
 * // Register to world
 * world.registerSystem(new CustomSystem());
 * ```
 */
