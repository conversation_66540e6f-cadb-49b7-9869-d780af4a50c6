{"version": 3, "sources": ["cce:/internal/x/prerequisite-imports"], "names": ["requests", "request", "_err"], "mappings": ";;;;;;AACA;AAEA,YAAM,CAAC,YAAY;AACf,cAAMA,QAAQ,GAAG,CAAC,uCAAD,EAA4J,uCAA5J,EAA4T,uCAA5T,EAAge,uCAAhe,EAAioB,uCAAjoB,EAA4xB,uCAA5xB,EAAg7B,uCAAh7B,EAAigC,uCAAjgC,EAAilC,uCAAjlC,EAAupC,uCAAvpC,EAAmuC,wCAAnuC,EAA0yC,wCAA1yC,EAAu3C,wCAAv3C,EAAi8C,wCAAj8C,EAAygD,wCAAzgD,EAAglD,wCAAhlD,EAA0pD,wCAA1pD,EAAguD,wCAAhuD,EAA6yD,wCAA7yD,EAAq3D,wCAAr3D,EAAm8D,wCAAn8D,EAAwhE,wCAAxhE,EAAonE,wCAApnE,EAA0sE,wCAA1sE,EAA+xE,wCAA/xE,EAAq3E,wCAAr3E,EAAk9E,wCAAl9E,EAAwiF,wCAAxiF,EAAooF,wCAApoF,EAAstF,wCAAttF,EAAizF,wCAAjzF,EAA24F,wCAA34F,EAAo+F,wCAAp+F,EAA8jG,wCAA9jG,EAA2pG,wCAA3pG,EAAuvG,wCAAvvG,EAAw1G,wCAAx1G,EAAi7G,wCAAj7G,EAA6gH,wCAA7gH,EAAwmH,wCAAxmH,EAAwsH,wCAAxsH,EAAqyH,wCAAryH,EAAs4H,wCAAt4H,EAAu9H,wCAAv9H,EAAkjI,wCAAljI,EAAqoI,wCAAroI,EAAiuI,wCAAjuI,EAAozI,wCAApzI,EAAs4I,wCAAt4I,EAA29I,wCAA39I,EAAsjJ,wCAAtjJ,EAA4oJ,wCAA5oJ,EAAytJ,wCAAztJ,EAAkzJ,wCAAlzJ,EAA64J,wCAA74J,EAAs9J,wCAAt9J,EAAuhK,wCAAvhK,EAAkmK,wCAAlmK,EAAqqK,wCAArqK,EAAgvK,wCAAhvK,CAAjB;;AACA,aAAK,MAAMC,OAAX,IAAsBD,QAAtB,EAAgC;AAC5B,cAAI;AACA,kBAAMC,OAAO,EAAb;AACH,WAFD,CAEE,OAAOC,IAAP,EAAa,CACX;AACH;AACJ;AACJ,OATK,GAAN", "sourcesContent": ["\n// Auto generated represents the prerequisite imports of project modules.\n\nawait (async () => {\n    const requests = [() => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Background.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Global.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Menu.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Player.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelSerializer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/base/System.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/base/World.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/index.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameInstance.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/IMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/MainUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts\")];\n    for (const request of requests) {\n        try {\n            await request();\n        } catch (_err) {\n            // The error should have been caught by executor.\n        }\n    }\n})();\n    "]}