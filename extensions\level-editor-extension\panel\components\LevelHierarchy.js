/**
 * Level Hierarchy Component
 * Tree view for displaying and managing level structure
 */
class LevelHierarchy {
  constructor(container, eventBus, editorState) {
    this.container = container;
    this.eventBus = eventBus;
    this.editorState = editorState;
    
    this.levelData = null;
    this.expandedItems = new Set();
    this.selectedItem = null;
    
    this.initialize();
  }

  /**
   * Initialize the component
   */
  initialize() {
    this.setupEventHandlers();
    this.render();
  }

  /**
   * Setup event handlers
   */
  setupEventHandlers() {
    // Listen for level data changes
    this.eventBus.onLevelDataChanged(this.onLevelDataChanged, this);
    
    // Listen for selection changes from other components
    this.eventBus.onSelectionChanged(this.onExternalSelectionChanged, this);
    
    // Context menu
    this.container.addEventListener('contextmenu', this.onContextMenu.bind(this));
    
    // Click handling
    this.container.addEventListener('click', this.onClick.bind(this));
    
    // Double-click handling
    this.container.addEventListener('dblclick', this.onDoubleClick.bind(this));
  }

  /**
   * Handle level data changed
   * @param {Object} levelData - New level data
   */
  onLevelDataChanged(levelData) {
    this.levelData = levelData;
    this.render();
  }

  /**
   * Handle external selection changes
   * @param {Array} selection - Selected items
   */
  onExternalSelectionChanged(selection) {
    // Update visual selection without triggering events
    this.updateVisualSelection(selection);
  }

  /**
   * Render the hierarchy
   */
  render() {
    this.container.innerHTML = '';
    
    if (!this.levelData) {
      this.renderEmptyState();
      return;
    }
    
    this.renderLevel();
  }

  /**
   * Render empty state
   */
  renderEmptyState() {
    const emptyDiv = document.createElement('div');
    emptyDiv.className = 'no-level-message';
    emptyDiv.innerHTML = `
      <p>No level loaded.</p>
      <p>Create a new level or load an existing one to get started.</p>
    `;
    this.container.appendChild(emptyDiv);
  }

  /**
   * Render the level
   */
  renderLevel() {
    const levelItem = this.createHierarchyItem({
      id: 'level',
      type: 'level',
      name: this.levelData.metadata.name,
      icon: '🎮',
      data: this.levelData
    });
    
    this.container.appendChild(levelItem.element);
    
    // Add level metadata
    if (this.isExpanded('level')) {
      const metadataItem = this.createHierarchyItem({
        id: 'level-metadata',
        type: 'metadata',
        name: 'Metadata',
        icon: 'ℹ️',
        data: this.levelData.metadata,
        parent: 'level'
      });
      levelItem.childrenContainer.appendChild(metadataItem.element);
      
      // Add camera settings
      const cameraItem = this.createHierarchyItem({
        id: 'level-camera',
        type: 'camera',
        name: 'Camera Settings',
        icon: '📷',
        data: this.levelData.cameraSettings,
        parent: 'level'
      });
      levelItem.childrenContainer.appendChild(cameraItem.element);
      
      // Add global events
      const eventsItem = this.createHierarchyItem({
        id: 'level-events',
        type: 'events',
        name: 'Global Events',
        icon: '⚡',
        data: this.levelData.globalEvents,
        parent: 'level'
      });
      levelItem.childrenContainer.appendChild(eventsItem.element);
      
      // Add sublevels
      const sublevelsItem = this.createHierarchyItem({
        id: 'sublevels',
        type: 'sublevels-group',
        name: `SubLevels (${this.levelData.subLevels.length})`,
        icon: '📁',
        data: this.levelData.subLevels,
        parent: 'level'
      });
      levelItem.childrenContainer.appendChild(sublevelsItem.element);
      
      // Add individual sublevels
      if (this.isExpanded('sublevels')) {
        this.levelData.subLevels.forEach((subLevel, index) => {
          this.renderSubLevel(subLevel, sublevelsItem.childrenContainer);
        });
      }
    }
  }

  /**
   * Render a sublevel
   * @param {Object} subLevel - SubLevel data
   * @param {HTMLElement} container - Container element
   */
  renderSubLevel(subLevel, container) {
    const subLevelItem = this.createHierarchyItem({
      id: subLevel.id,
      type: 'sublevel',
      name: subLevel.name,
      icon: subLevel.isActive ? '🏠' : '🏘️',
      data: subLevel
    });
    
    container.appendChild(subLevelItem.element);
    
    if (this.isExpanded(subLevel.id)) {
      // Add spawners group
      const spawnersItem = this.createHierarchyItem({
        id: `${subLevel.id}-spawners`,
        type: 'spawners-group',
        name: `Spawners (${subLevel.spawners.length})`,
        icon: '📁',
        data: subLevel.spawners,
        parent: subLevel.id
      });
      subLevelItem.childrenContainer.appendChild(spawnersItem.element);
      
      // Add individual spawners
      if (this.isExpanded(`${subLevel.id}-spawners`)) {
        subLevel.spawners.forEach(spawner => {
          const spawnerItem = this.createHierarchyItem({
            id: spawner.id,
            type: 'spawner',
            name: spawner.name,
            icon: spawner.isActive ? '⚡' : '💤',
            data: spawner,
            parent: subLevel.id
          });
          spawnersItem.childrenContainer.appendChild(spawnerItem.element);
        });
      }
      
      // Add paths group
      const pathsItem = this.createHierarchyItem({
        id: `${subLevel.id}-paths`,
        type: 'paths-group',
        name: `Paths (${subLevel.paths.length})`,
        icon: '📁',
        data: subLevel.paths,
        parent: subLevel.id
      });
      subLevelItem.childrenContainer.appendChild(pathsItem.element);
      
      // Add individual paths
      if (this.isExpanded(`${subLevel.id}-paths`)) {
        subLevel.paths.forEach(path => {
          const pathItem = this.createHierarchyItem({
            id: path.id,
            type: 'path',
            name: path.name,
            icon: path.isLoop ? '🔄' : '🛤️',
            data: path,
            parent: subLevel.id
          });
          pathsItem.childrenContainer.appendChild(pathItem.element);
        });
      }
      
      // Add map layers group
      const mapsItem = this.createHierarchyItem({
        id: `${subLevel.id}-maps`,
        type: 'maps-group',
        name: `Map Layers (${subLevel.maps.length})`,
        icon: '📁',
        data: subLevel.maps,
        parent: subLevel.id
      });
      subLevelItem.childrenContainer.appendChild(mapsItem.element);
      
      // Add individual map layers
      if (this.isExpanded(`${subLevel.id}-maps`)) {
        subLevel.maps.forEach(mapLayer => {
          const mapItem = this.createHierarchyItem({
            id: mapLayer.id,
            type: 'map',
            name: mapLayer.editorData?.name || `Layer ${mapLayer.depth}`,
            icon: mapLayer.isVisible ? '🗺️' : '👁️‍🗨️',
            data: mapLayer,
            parent: subLevel.id
          });
          mapsItem.childrenContainer.appendChild(mapItem.element);
        });
      }
    }
  }

  /**
   * Create a hierarchy item
   * @param {Object} itemData - Item data
   * @returns {Object} Item element and children container
   */
  createHierarchyItem(itemData) {
    const itemElement = document.createElement('div');
    itemElement.className = 'hierarchy-item';
    itemElement.dataset.id = itemData.id;
    itemElement.dataset.type = itemData.type;
    
    // Toggle button
    const toggle = document.createElement('div');
    toggle.className = 'hierarchy-toggle';
    if (this.hasChildren(itemData)) {
      toggle.style.visibility = 'visible';
      itemElement.classList.add(this.isExpanded(itemData.id) ? 'expanded' : 'collapsed');
    } else {
      toggle.style.visibility = 'hidden';
    }
    
    // Icon
    const icon = document.createElement('div');
    icon.className = 'hierarchy-icon';
    icon.textContent = itemData.icon;
    
    // Label
    const label = document.createElement('div');
    label.className = 'hierarchy-label';
    label.textContent = itemData.name;
    
    itemElement.appendChild(toggle);
    itemElement.appendChild(icon);
    itemElement.appendChild(label);
    
    // Children container
    const childrenContainer = document.createElement('div');
    childrenContainer.className = 'hierarchy-children';
    if (!this.isExpanded(itemData.id)) {
      childrenContainer.style.display = 'none';
    }
    
    const wrapper = document.createElement('div');
    wrapper.appendChild(itemElement);
    wrapper.appendChild(childrenContainer);
    
    return {
      element: wrapper,
      itemElement: itemElement,
      childrenContainer: childrenContainer
    };
  }

  /**
   * Check if item has children
   * @param {Object} itemData - Item data
   * @returns {boolean} True if has children
   */
  hasChildren(itemData) {
    switch (itemData.type) {
      case 'level':
      case 'sublevel':
      case 'sublevels-group':
      case 'spawners-group':
      case 'paths-group':
      case 'maps-group':
        return true;
      default:
        return false;
    }
  }

  /**
   * Check if item is expanded
   * @param {string} itemId - Item ID
   * @returns {boolean} True if expanded
   */
  isExpanded(itemId) {
    return this.expandedItems.has(itemId);
  }

  /**
   * Toggle item expansion
   * @param {string} itemId - Item ID
   */
  toggleExpansion(itemId) {
    if (this.isExpanded(itemId)) {
      this.expandedItems.delete(itemId);
    } else {
      this.expandedItems.add(itemId);
    }
    this.render();
  }

  /**
   * Handle click events
   * @param {Event} event - Click event
   */
  onClick(event) {
    const item = event.target.closest('.hierarchy-item');
    if (!item) return;
    
    const toggle = event.target.closest('.hierarchy-toggle');
    if (toggle) {
      // Toggle expansion
      this.toggleExpansion(item.dataset.id);
      return;
    }
    
    // Select item
    this.selectItem(item.dataset.id, item.dataset.type);
  }

  /**
   * Handle double-click events
   * @param {Event} event - Double-click event
   */
  onDoubleClick(event) {
    const item = event.target.closest('.hierarchy-item');
    if (!item) return;
    
    // Focus on item in sublevel editor
    this.eventBus.emit('focus-item', {
      id: item.dataset.id,
      type: item.dataset.type
    });
  }

  /**
   * Handle context menu
   * @param {Event} event - Context menu event
   */
  onContextMenu(event) {
    event.preventDefault();
    
    const item = event.target.closest('.hierarchy-item');
    if (!item) return;
    
    this.showContextMenu(event.clientX, event.clientY, item.dataset.id, item.dataset.type);
  }

  /**
   * Show context menu
   * @param {number} x - X position
   * @param {number} y - Y position
   * @param {string} itemId - Item ID
   * @param {string} itemType - Item type
   */
  showContextMenu(x, y, itemId, itemType) {
    this.eventBus.emit('show-context-menu', {
      x, y, itemId, itemType,
      actions: this.getContextMenuActions(itemType)
    });
  }

  /**
   * Get context menu actions for item type
   * @param {string} itemType - Item type
   * @returns {Array} Array of actions
   */
  getContextMenuActions(itemType) {
    const actions = [];
    
    switch (itemType) {
      case 'level':
        actions.push('add-sublevel');
        break;
      case 'sublevel':
        actions.push('add-spawner', 'add-path', 'add-map-layer', '---', 'duplicate', 'delete', '---', 'rename');
        break;
      case 'sublevels-group':
        actions.push('add-sublevel');
        break;
      case 'spawners-group':
        actions.push('add-spawner');
        break;
      case 'paths-group':
        actions.push('add-path');
        break;
      case 'maps-group':
        actions.push('add-map-layer');
        break;
      case 'spawner':
      case 'path':
      case 'map':
        actions.push('duplicate', 'delete', '---', 'rename');
        break;
    }
    
    return actions;
  }

  /**
   * Select an item
   * @param {string} itemId - Item ID
   * @param {string} itemType - Item type
   */
  selectItem(itemId, itemType) {
    this.selectedItem = { id: itemId, type: itemType };
    
    // Find the actual data object
    const data = this.findItemData(itemId, itemType);
    
    // Update visual selection
    this.updateVisualSelection([{ id: itemId, type: itemType, data }]);
    
    // Emit selection change
    this.editorState.setSelection([{ id: itemId, type: itemType, data }]);
  }

  /**
   * Find item data by ID and type
   * @param {string} itemId - Item ID
   * @param {string} itemType - Item type
   * @returns {Object|null} Item data or null
   */
  findItemData(itemId, itemType) {
    if (!this.levelData) return null;
    
    switch (itemType) {
      case 'level':
        return this.levelData;
      case 'metadata':
        return this.levelData.metadata;
      case 'camera':
        return this.levelData.cameraSettings;
      case 'events':
        return this.levelData.globalEvents;
      case 'sublevel':
        return this.levelData.subLevels.find(sl => sl.id === itemId);
      case 'spawner':
        for (const subLevel of this.levelData.subLevels) {
          const spawner = subLevel.spawners.find(s => s.id === itemId);
          if (spawner) return spawner;
        }
        break;
      case 'path':
        for (const subLevel of this.levelData.subLevels) {
          const path = subLevel.paths.find(p => p.id === itemId);
          if (path) return path;
        }
        break;
      case 'map':
        for (const subLevel of this.levelData.subLevels) {
          const map = subLevel.maps.find(m => m.id === itemId);
          if (map) return map;
        }
        break;
    }
    
    return null;
  }

  /**
   * Update visual selection
   * @param {Array} selection - Selected items
   */
  updateVisualSelection(selection) {
    // Clear previous selection
    this.container.querySelectorAll('.hierarchy-item.selected').forEach(item => {
      item.classList.remove('selected');
    });
    
    // Apply new selection
    selection.forEach(item => {
      const element = this.container.querySelector(`[data-id="${item.id}"]`);
      if (element) {
        element.classList.add('selected');
      }
    });
  }

  /**
   * Expand all items
   */
  expandAll() {
    this.expandedItems.clear();
    this.addAllExpandableItems();
    this.render();
  }

  /**
   * Collapse all items
   */
  collapseAll() {
    this.expandedItems.clear();
    this.render();
  }

  /**
   * Add all expandable items to expanded set
   */
  addAllExpandableItems() {
    if (!this.levelData) return;
    
    this.expandedItems.add('level');
    this.expandedItems.add('sublevels');
    
    this.levelData.subLevels.forEach(subLevel => {
      this.expandedItems.add(subLevel.id);
      this.expandedItems.add(`${subLevel.id}-spawners`);
      this.expandedItems.add(`${subLevel.id}-paths`);
      this.expandedItems.add(`${subLevel.id}-maps`);
    });
  }

  /**
   * Destroy the component
   */
  destroy() {
    this.eventBus.off('level-data-changed', this.onLevelDataChanged, this);
    this.eventBus.off('selection-changed', this.onExternalSelectionChanged, this);
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = LevelHierarchy;
} else {
  window.LevelHierarchy = LevelHierarchy;
}
