{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts"], "names": ["_decorator", "Component", "ccclass", "property", "Emitter", "start", "update", "deltaTime"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;;;;;;;;OACf;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;;yBAGjBI,O,WADZF,OAAO,CAAC,SAAD,C,gBAAR,MACaE,OADb,SAC6BH,SAD7B,CACuC;AACnCI,QAAAA,KAAK,GAAG,CAEP;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB,CAEzB;;AAPkC,O", "sourcesContent": ["import { _decorator, Component, Node } from 'cc';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('Emitter')\r\nexport class Emitter extends Component {\r\n    start() {\r\n\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n        \r\n    }\r\n}\r\n\r\r\n"]}