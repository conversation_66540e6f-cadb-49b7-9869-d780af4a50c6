{"name": "level-editor-extension", "version": "1.0.0", "package_version": 2, "description": "Advanced Level Editor for M2Game - Visual editing of LevelData including SubLevels, Spawners, Paths, and Map Layers", "author": "M2Game Team", "main": "main.js", "contributions": {"menu": [{"path": "Panel", "label": "Level Editor", "message": "open-panel"}], "messages": {"open-panel": {"methods": ["openPanel"]}, "new-level": {"methods": ["newLevel"]}, "load-level": {"methods": ["loadLevel"]}, "save-level": {"methods": ["saveLevel"]}, "save-level-as": {"methods": ["saveLevelAs"]}, "import-level": {"methods": ["importLevel"]}, "export-level": {"methods": ["exportLevel"]}}}, "panels": {"level-editor": {"title": "Level Editor", "type": "dockable", "main": "panel/index.js", "size": {"min-width": 400, "min-height": 300, "width": 800, "height": 600}}}, "dependencies": {}, "keywords": ["level-editor", "game-development", "visual-editor", "cocos-creator"], "repository": {"type": "svn", "url": ""}, "license": "MIT"}