# Level Editor Extension Integration Guide

This guide explains how to integrate the Level Editor Extension with the existing LevelEditor.ts component.

## Overview

The Level Editor Extension provides a comprehensive visual editor for level data with the following key features:

- **Visual Panel Interface**: Hierarchy view, property inspector, and canvas editor
- **Component-Specific Editors**: Specialized editors for spawners, paths, and map layers
- **Scene Integration**: Visual gizmos and manipulation tools in the scene view
- **Data Synchronization**: Bidirectional sync with the existing LevelEditor component

## Integration Steps

### 1. Add the Bridge Component

First, add the `LevelEditorBridge` component to your LevelEditor node:

```typescript
// In your LevelEditor.ts file
import { LevelEditorBridge } from '../packages/level-editor-extension/integration/LevelEditorBridge';

@ccclass('LevelEditor')
export class LevelEditor extends Component {
    
    @property(LevelEditorBridge)
    public extensionBridge: LevelEditorBridge | null = null;
    
    start() {
        // Initialize the bridge
        if (this.extensionBridge) {
            this.setupExtensionBridge();
        }
    }
    
    private setupExtensionBridge(): void {
        if (!this.extensionBridge) return;
        
        // Setup callbacks
        this.extensionBridge.onExtensionConnected = this.onExtensionConnected.bind(this);
        this.extensionBridge.onLevelDataReceived = this.onLevelDataFromExtension.bind(this);
        this.extensionBridge.onSelectionReceived = this.onSelectionFromExtension.bind(this);
        this.extensionBridge.onEditModeReceived = this.onEditModeFromExtension.bind(this);
        
        // Connect to extension
        this.extensionBridge.connectToExtension();
    }
}
```

### 2. Implement Bridge Callbacks

Add the callback methods to handle communication with the extension:

```typescript
private onExtensionConnected(bridge: LevelEditorBridge): void {
    console.log('[LevelEditor] Extension connected');
    
    // Send current level data to extension
    if (this.currentLevel) {
        bridge.sendLevelData(this.currentLevel);
    }
    
    // Send current selection
    if (this.selectedSubLevel) {
        bridge.sendSelection({
            type: 'sublevel',
            id: this.selectedSubLevel.id,
            data: this.selectedSubLevel
        });
    }
}

private onLevelDataFromExtension(levelData: ILevelData): void {
    console.log('[LevelEditor] Received level data from extension');
    
    // Update the level data
    this.loadLevel(levelData);
    
    // Mark as modified if needed
    this.markAsModified();
}

private onSelectionFromExtension(selection: SelectionInfo): void {
    console.log('[LevelEditor] Received selection from extension');
    
    // Update selection based on extension
    this.updateSelectionFromExtension(selection);
}

private onEditModeFromExtension(editMode: EditMode): void {
    console.log('[LevelEditor] Received edit mode from extension');
    
    // Update edit mode
    this.currentEditMode = editMode;
}
```

### 3. Sync Data Changes

Ensure that changes in the LevelEditor are sent to the extension:

```typescript
public loadLevel(levelData: ILevelData): void {
    // Your existing load level logic
    this.currentLevel = levelData;
    
    // Sync with extension
    if (this.extensionBridge) {
        this.extensionBridge.sendLevelData(levelData);
    }
}

public selectSubLevel(subLevel: SubLevel): void {
    // Your existing selection logic
    this.selectedSubLevel = subLevel;
    
    // Sync with extension
    if (this.extensionBridge) {
        this.extensionBridge.sendSelection({
            type: 'sublevel',
            id: subLevel.id,
            data: subLevel
        });
    }
}

public setEditMode(editMode: EditMode): void {
    // Your existing edit mode logic
    this.currentEditMode = editMode;
    
    // Sync with extension
    if (this.extensionBridge) {
        this.extensionBridge.sendEditMode(editMode);
    }
}
```

### 4. Handle Property Changes

For fine-grained synchronization, send property changes to the extension:

```typescript
private updateSpawnerProperty(spawner: Spawner, property: string, value: any): void {
    // Update the property
    (spawner as any)[property] = value;
    
    // Sync with extension
    if (this.extensionBridge) {
        this.extensionBridge.sendPropertyChange(`spawner.${property}`, value, spawner);
    }
    
    // Mark as modified
    this.markAsModified();
}
```

### 5. Enable Scene Integration

To enable visual gizmos and manipulation in the scene view:

```typescript
private enableSceneIntegration(): void {
    if (this.extensionBridge) {
        // Update bridge settings to enable scene features
        this.extensionBridge.updateSettings({
            enableSceneGizmos: true,
            enableManipulation: true
        });
    }
}
```

## Extension Panel Usage

### Opening the Extension Panel

1. Go to **Level Editor → Open Level Editor** in the Cocos Creator menu
2. The extension panel will open and automatically attempt to connect to your LevelEditor component
3. Once connected, you'll see the level data synchronized between both systems

### Panel Features

#### Hierarchy View
- **Tree structure** of all sublevels, spawners, paths, and maps
- **Drag & drop** to reorganize elements
- **Context menus** for adding/removing items
- **Search and filter** functionality

#### Property Inspector
- **Dynamic property editing** based on selection
- **Validation** and error checking
- **Custom editors** for complex types (Vector3, Color, etc.)
- **Undo/redo** support

#### Visual Canvas Editor
- **Zoom and pan** for navigation
- **Grid snapping** for precise positioning
- **Visual gizmos** for bounds, spawners, and paths
- **Direct manipulation** with mouse/touch

#### Component-Specific Editors
- **Spawner Editor**: Wave configuration, spawn patterns, area settings
- **Path Editor**: Bezier curve editing, point manipulation, preview
- **Map Layer Editor**: Parallax settings, layer ordering, bounds

### Scene View Integration

When scene integration is enabled, you'll see:

- **Visual gizmos** overlaid on the scene
- **Interactive handles** for resizing and moving
- **Real-time updates** as you manipulate objects
- **Grid snapping** and alignment tools

## Configuration

### Bridge Settings

Configure the bridge behavior through the component properties:

```typescript
// In the inspector or code
this.extensionBridge.autoConnect = true;           // Auto-connect on start
this.extensionBridge.enableBidirectionalSync = true; // Two-way sync
this.extensionBridge.syncInterval = 1000;          // Sync every 1 second
this.extensionBridge.enableSceneGizmos = true;     // Show scene gizmos
this.extensionBridge.enableManipulation = true;    // Enable manipulation
```

### Extension Settings

The extension panel has its own settings accessible through the gear icon:

- **Auto-save**: Automatically save changes
- **Grid settings**: Grid size and snapping
- **Visual settings**: Colors, gizmo sizes, etc.
- **Integration settings**: Sync behavior, validation

## Troubleshooting

### Connection Issues

If the extension doesn't connect:

1. **Check the console** for error messages
2. **Verify the bridge component** is attached and enabled
3. **Ensure the extension is installed** and enabled in Cocos Creator
4. **Try manual connection** using `extensionBridge.connectToExtension()`

### Sync Issues

If data doesn't sync properly:

1. **Check bidirectional sync** is enabled
2. **Verify event handlers** are properly set up
3. **Look for validation errors** in the console
4. **Try force sync** using the extension panel's sync button

### Performance Issues

If the editor feels slow:

1. **Reduce sync frequency** by increasing `syncInterval`
2. **Disable scene gizmos** if not needed
3. **Limit the number of visible elements** in complex levels
4. **Use the extension's LOD system** for large levels

## Best Practices

### Data Management
- **Always validate** level data before applying changes
- **Use the undo/redo system** for user actions
- **Backup important data** before major operations
- **Test with various level sizes** and complexities

### Performance
- **Batch property changes** when possible
- **Debounce frequent updates** (like during dragging)
- **Use efficient data structures** for large datasets
- **Profile regularly** to identify bottlenecks

### User Experience
- **Provide clear feedback** for all operations
- **Handle errors gracefully** with user-friendly messages
- **Maintain consistency** between the extension and main editor
- **Document custom workflows** for your team

## API Reference

### LevelEditorBridge Methods

```typescript
// Connection
connectToExtension(): void
disconnectFromExtension(): void
getConnectionStatus(): { isConnected: boolean; hasExtension: boolean }

// Data Sync
sendLevelData(levelData: ILevelData): void
sendSelection(selection: SelectionInfo): void
sendEditMode(editMode: EditMode): void
sendPropertyChange(propertyPath: string, value: any, target: any): void

// Utilities
focusOnItem(itemType: string, itemId: string): void
updateSettings(settings: Partial<BridgeSettings>): void
```

### Extension Events

The extension emits various events that you can listen for:

```typescript
// Listen for extension events
cc.systemEvent.on('level-editor-manipulation-end', (event) => {
    // Handle manipulation completion
    this.onManipulationEnd(event);
});

cc.systemEvent.on('level-editor-level-changed', (event) => {
    // Handle level data changes
    this.onLevelChanged(event.levelData);
});
```

## Support

For issues, questions, or feature requests:

1. **Check the console** for detailed error messages
2. **Review this integration guide** for common solutions
3. **Examine the example implementations** in the extension
4. **Create an issue** in the project repository with details

The Level Editor Extension is designed to enhance your level editing workflow while maintaining compatibility with existing systems. Follow this guide to get the most out of the integration!
