System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Vec3, System, RegisterTypeID, _dec, _dec2, _class, _crd, ccclass, PlayerState, PlayerSystem;

  function _reportPossibleCrUseOfSystem(extras) {
    _reporterNs.report("System", "../base/System", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRegisterTypeID(extras) {
    _reporterNs.report("RegisterTypeID", "../base/TypeID", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Vec3 = _cc.Vec3;
    }, function (_unresolved_2) {
      System = _unresolved_2.System;
    }, function (_unresolved_3) {
      RegisterTypeID = _unresolved_3.RegisterTypeID;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "4fec59njaJDj5/8ZzLj3jzw", "PlayerSystem", undefined);

      __checkObsolete__(['_decorator', 'Vec3', 'Node']);

      ({
        ccclass
      } = _decorator);
      /**
       * Player state enumeration
       */

      _export("PlayerState", PlayerState = /*#__PURE__*/function (PlayerState) {
        PlayerState["IDLE"] = "idle";
        PlayerState["MOVING"] = "moving";
        PlayerState["ATTACKING"] = "attacking";
        PlayerState["TAKING_DAMAGE"] = "taking_damage";
        PlayerState["DEAD"] = "dead";
        PlayerState["INVULNERABLE"] = "invulnerable";
        return PlayerState;
      }({}));
      /**
       * Player statistics
       */

      /**
       * Player input data
       */

      /**
       * Player data
       */


      /**
       * PlayerSystem - manages player state, movement, combat, and progression
       */
      _export("PlayerSystem", PlayerSystem = (_dec = ccclass("PlayerSystem"), _dec2 = _crd && RegisterTypeID === void 0 ? (_reportPossibleCrUseOfRegisterTypeID({
        error: Error()
      }), RegisterTypeID) : RegisterTypeID, _dec(_class = _dec2(_class = class PlayerSystem extends (_crd && System === void 0 ? (_reportPossibleCrUseOfSystem({
        error: Error()
      }), System) : System) {
        constructor(...args) {
          super(...args);
          this._players = new Map();
          this._currentPlayerId = null;
          this._inputBuffer = [];
          this._maxInputBufferSize = 10;
          this._nextPlayerId = 1;
          // Auto-incrementing player ID
          // Configuration
          this._invulnerabilityTime = 2.0;
          // seconds
          this._maxPlayers = 4;
        }

        // for multiplayer support

        /**
         * Get the system name
         */
        getSystemName() {
          return "PlayerSystem";
        }
        /**
         * Initialize the player system
         */


        onInit() {
          console.log("PlayerSystem: Initializing player system"); // Initialize input buffer

          this._inputBuffer = [];
          console.log(`PlayerSystem: Initialized with max players: ${this._maxPlayers}`);
        }
        /**
         * Cleanup the player system
         */


        onUnInit() {
          console.log("PlayerSystem: Cleaning up player system"); // Cleanup all players

          this._players.forEach(player => {
            this._destroyPlayer(player);
          });

          this._players.clear();

          this._currentPlayerId = null;
          this._inputBuffer.length = 0;
          this._nextPlayerId = 1;
          console.log("PlayerSystem: Cleanup complete");
        }
        /**
         * Update the player system
         */


        onUpdate(deltaTime) {
          // Process input buffer
          this._processInputBuffer(); // Update all players


          this._players.forEach(player => {
            this._updatePlayer(player, deltaTime);
          });
        }
        /**
         * Late update - handle any post-update logic
         */


        onLateUpdate(_deltaTime) {
          // Update visual representations
          this._players.forEach(player => {
            this._updatePlayerVisuals(player);
          });
        }
        /**
         * Create a new player
         * @param startPosition Starting position for the player
         * @param initialStats Initial player statistics
         * @returns The player ID if creation was successful, or null if failed
         */


        createPlayer(startPosition, initialStats = {}) {
          if (this._players.size >= this._maxPlayers) {
            console.warn("PlayerSystem: Cannot create player - max players reached");
            return null;
          } // Generate new player ID


          const playerId = this._nextPlayerId++; // Create default stats

          const defaultStats = {
            maxHealth: 100,
            currentHealth: 100,
            attackPower: 10,
            moveSpeed: 300,
            defense: 5,
            experience: 0,
            level: 1
          }; // Merge with provided stats

          const stats = { ...defaultStats,
            ...initialStats
          };
          stats.currentHealth = stats.maxHealth; // Ensure current health doesn't exceed max
          // Create player data

          const player = {
            id: playerId,
            position: new Vec3(startPosition),
            velocity: new Vec3(),
            rotation: 0,
            state: PlayerState.IDLE,
            stats: stats,
            inventory: [],
            abilities: [],
            statusEffects: new Map(),
            lastDamageTime: 0,
            invulnerabilityDuration: 0,
            node: undefined
          };

          this._players.set(playerId, player); // Set as current player if it's the first one


          if (this._currentPlayerId === null) {
            this._currentPlayerId = playerId;
          }

          console.log(`PlayerSystem: Created player ${playerId}`);
          return playerId;
        }
        /**
         * Remove a player
         * @param playerId The ID of the player to remove
         * @returns true if the player was removed
         */


        removePlayer(playerId) {
          const player = this._players.get(playerId);

          if (!player) {
            return false;
          }

          this._destroyPlayer(player);

          this._players.delete(playerId); // Update current player if necessary


          if (this._currentPlayerId === playerId) {
            this._currentPlayerId = this._players.size > 0 ? this._players.keys().next().value : null;
          }

          console.log(`PlayerSystem: Removed player ${playerId}`);
          return true;
        }
        /**
         * Get a player by ID
         * @param playerId The ID of the player to get
         * @returns The player data or null if not found
         */


        getPlayer(playerId) {
          return this._players.get(playerId) || null;
        }
        /**
         * Get the current active player
         * @returns The current player data or null if no current player
         */


        getCurrentPlayer() {
          return this._currentPlayerId !== null ? this._players.get(this._currentPlayerId) || null : null;
        }
        /**
         * Set the current active player
         * @param playerId The ID of the player to set as current
         * @returns true if the player was set as current
         */


        setCurrentPlayer(playerId) {
          if (!this._players.has(playerId)) {
            return false;
          }

          this._currentPlayerId = playerId;
          console.log(`PlayerSystem: Set current player to ${playerId}`);
          return true;
        }
        /**
         * Apply damage to a player
         * @param playerId The ID of the player to damage
         * @param damage The amount of damage to apply
         * @param _damageSource Optional source of the damage (unused parameter)
         * @returns true if damage was applied
         */


        damagePlayer(playerId, damage, _damageSource) {
          const player = this._players.get(playerId);

          if (!player || player.state === PlayerState.DEAD) {
            return false;
          } // Check invulnerability


          if (player.invulnerabilityDuration > 0) {
            console.log(`PlayerSystem: Player ${playerId} is invulnerable, damage ignored`);
            return false;
          } // Calculate actual damage (apply defense)


          const actualDamage = Math.max(1, damage - player.stats.defense); // Apply damage

          player.stats.currentHealth = Math.max(0, player.stats.currentHealth - actualDamage);
          player.lastDamageTime = Date.now();
          player.invulnerabilityDuration = this._invulnerabilityTime; // Update state

          if (player.stats.currentHealth <= 0) {
            player.state = PlayerState.DEAD;
            console.log(`PlayerSystem: Player ${playerId} died`);
          } else {
            player.state = PlayerState.TAKING_DAMAGE;
          }

          console.log(`PlayerSystem: Player ${playerId} took ${actualDamage} damage (${player.stats.currentHealth}/${player.stats.maxHealth} HP remaining)`);
          return true;
        }
        /**
         * Heal a player
         * @param playerId The ID of the player to heal
         * @param healAmount The amount of health to restore
         * @returns true if healing was applied
         */


        healPlayer(playerId, healAmount) {
          const player = this._players.get(playerId);

          if (!player || player.state === PlayerState.DEAD) {
            return false;
          }

          const oldHealth = player.stats.currentHealth;
          player.stats.currentHealth = Math.min(player.stats.maxHealth, player.stats.currentHealth + healAmount);
          const actualHealing = player.stats.currentHealth - oldHealth;

          if (actualHealing > 0) {
            console.log(`PlayerSystem: Player ${playerId} healed for ${actualHealing} HP`);
            return true;
          }

          return false;
        }
        /**
         * Add input to the input buffer
         * @param input The input data to add
         */


        addInput(input) {
          this._inputBuffer.push(input); // Limit buffer size


          if (this._inputBuffer.length > this._maxInputBufferSize) {
            this._inputBuffer.shift();
          }
        }
        /**
         * Get all players
         * @returns Array of all player data
         */


        getAllPlayers() {
          return Array.from(this._players.values());
        }
        /**
         * Get the number of active players
         * @returns The number of active players
         */


        getPlayerCount() {
          return this._players.size;
        }
        /**
         * Update a single player
         */


        _updatePlayer(player, deltaTime) {
          // Update invulnerability
          if (player.invulnerabilityDuration > 0) {
            player.invulnerabilityDuration -= deltaTime;

            if (player.invulnerabilityDuration <= 0) {
              player.invulnerabilityDuration = 0;

              if (player.state === PlayerState.TAKING_DAMAGE) {
                player.state = PlayerState.IDLE;
              }
            }
          } // Update status effects


          player.statusEffects.forEach((duration, effect) => {
            const newDuration = duration - deltaTime;

            if (newDuration <= 0) {
              player.statusEffects.delete(effect);
              console.log(`PlayerSystem: Status effect ${effect} expired for player ${player.id}`);
            } else {
              player.statusEffects.set(effect, newDuration);
            }
          }); // Update position based on velocity

          player.position.add(Vec3.multiplyScalar(new Vec3(), player.velocity, deltaTime)); // Apply friction to velocity

          player.velocity.multiplyScalar(0.9);
        }
        /**
         * Process the input buffer
         */


        _processInputBuffer() {
          if (this._inputBuffer.length === 0 || !this._currentPlayerId) {
            return;
          }

          const player = this._players.get(this._currentPlayerId);

          if (!player || player.state === PlayerState.DEAD) {
            this._inputBuffer.length = 0; // Clear buffer

            return;
          } // Process the most recent input


          const input = this._inputBuffer.pop();

          if (input) {
            this._applyInputToPlayer(player, input);
          } // Clear remaining inputs (we only process the latest)


          this._inputBuffer.length = 0;
        }
        /**
         * Apply input to a player
         */


        _applyInputToPlayer(player, input) {
          // Apply movement
          if (input.moveDirection.length() > 0) {
            const moveForce = Vec3.multiplyScalar(new Vec3(), input.moveDirection.normalize(), player.stats.moveSpeed);
            player.velocity.add(moveForce);
            player.state = PlayerState.MOVING;
          } else if (player.state === PlayerState.MOVING) {
            player.state = PlayerState.IDLE;
          } // Apply attack


          if (input.isAttacking && player.state !== PlayerState.ATTACKING && player.state !== PlayerState.TAKING_DAMAGE) {
            player.state = PlayerState.ATTACKING; // Attack logic would go here
          }
        }
        /**
         * Update player visual representation
         */


        _updatePlayerVisuals(player) {
          if (player.node && player.node.isValid) {
            player.node.setPosition(player.position);
            player.node.setRotationFromEuler(0, 0, player.rotation);
          }
        }
        /**
         * Destroy a player's visual representation
         */


        _destroyPlayer(player) {
          if (player.node && player.node.isValid) {
            player.node.destroy();
            player.node = undefined;
          }
        }

      }) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=34551532144943d5b236508c5ff8c0f8cd4ba4f3.js.map