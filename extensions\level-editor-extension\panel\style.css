/* Level Editor Extension Styles */

/* Reset and base styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 12px;
    color: #cccccc;
    background-color: #2d2d30;
    overflow: hidden;
}

/* Main container */
.level-editor-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100%;
}

/* Toolbar styles */
.toolbar {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    background-color: #3c3c3c;
    border-bottom: 1px solid #464647;
    min-height: 32px;
}

.toolbar-group {
    display: flex;
    align-items: center;
    gap: 2px;
}

.toolbar-btn {
    min-width: 24px;
    height: 24px;
    padding: 2px;
    border: none;
    background-color: transparent;
    color: #cccccc;
    cursor: pointer;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toolbar-btn:hover {
    background-color: #464647;
}

.toolbar-btn.active {
    background-color: #007acc;
    color: white;
}

.toolbar-separator {
    width: 1px;
    height: 20px;
    background-color: #464647;
    margin: 0 4px;
}

.toolbar-spacer {
    flex: 1;
}

.level-status {
    font-size: 11px;
    color: #999999;
    font-style: italic;
}

/* Main content area */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Panel styles */
.left-panel,
.center-panel,
.right-panel {
    display: flex;
    flex-direction: column;
    border-right: 1px solid #464647;
    background-color: #2d2d30;
}

.left-panel {
    width: 250px;
    min-width: 200px;
    max-width: 400px;
    resize: horizontal;
}

.center-panel {
    flex: 1;
    min-width: 300px;
}

.right-panel {
    width: 280px;
    min-width: 200px;
    max-width: 400px;
    border-right: none;
    resize: horizontal;
}

.panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 8px;
    background-color: #3c3c3c;
    border-bottom: 1px solid #464647;
    min-height: 28px;
}

.panel-title {
    font-size: 11px;
    font-weight: 600;
    color: #cccccc;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.panel-actions {
    display: flex;
    gap: 2px;
}

.icon-btn {
    width: 18px;
    height: 18px;
    padding: 1px;
    border: none;
    background-color: transparent;
    color: #999999;
    cursor: pointer;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-btn:hover {
    background-color: #464647;
    color: #cccccc;
}

/* Level hierarchy styles */
.level-hierarchy {
    flex: 1;
    overflow-y: auto;
    padding: 4px;
}

.hierarchy-item {
    display: flex;
    align-items: center;
    padding: 2px 4px;
    cursor: pointer;
    border-radius: 2px;
    user-select: none;
}

.hierarchy-item:hover {
    background-color: #3c3c3c;
}

.hierarchy-item.selected {
    background-color: #007acc;
    color: white;
}

.hierarchy-item.expanded > .hierarchy-toggle::before {
    content: '▼';
}

.hierarchy-item.collapsed > .hierarchy-toggle::before {
    content: '▶';
}

.hierarchy-toggle {
    width: 12px;
    height: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    margin-right: 4px;
    cursor: pointer;
}

.hierarchy-icon {
    width: 14px;
    height: 14px;
    margin-right: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
}

.hierarchy-label {
    flex: 1;
    font-size: 11px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.hierarchy-children {
    margin-left: 16px;
}

/* SubLevel editor styles */
.sublevel-editor {
    flex: 1;
    position: relative;
    overflow: hidden;
    background-color: #1e1e1e;
}

.sublevel-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: crosshair;
}

.sublevel-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    opacity: 0.3;
}

/* Property inspector styles */
.property-inspector {
    flex: 1;
    overflow-y: auto;
    padding: 4px;
}

.property-group {
    margin-bottom: 8px;
    border: 1px solid #464647;
    border-radius: 2px;
    background-color: #252526;
}

.property-group-header {
    padding: 4px 8px;
    background-color: #3c3c3c;
    border-bottom: 1px solid #464647;
    font-size: 11px;
    font-weight: 600;
    cursor: pointer;
    user-select: none;
}

.property-group-content {
    padding: 4px;
}

.property-row {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    min-height: 20px;
}

.property-label {
    flex: 0 0 80px;
    font-size: 11px;
    color: #cccccc;
    padding-right: 4px;
}

.property-input {
    flex: 1;
    min-height: 18px;
    padding: 2px 4px;
    border: 1px solid #464647;
    background-color: #3c3c3c;
    color: #cccccc;
    font-size: 11px;
    border-radius: 2px;
}

.property-input:focus {
    outline: none;
    border-color: #007acc;
    background-color: #252526;
}

/* Status bar styles */
.status-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 2px 8px;
    background-color: #007acc;
    color: white;
    font-size: 11px;
    min-height: 20px;
}

.status-left,
.status-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-separator {
    color: rgba(255, 255, 255, 0.5);
}

/* Message styles */
.no-level-message,
.no-sublevel-message,
.no-selection-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #999999;
    text-align: center;
    padding: 20px;
}

.no-level-message p,
.no-sublevel-message p,
.no-selection-message p {
    margin-bottom: 8px;
    font-size: 12px;
}

/* Context menu styles */
.context-menu {
    position: fixed;
    background-color: #3c3c3c;
    border: 1px solid #464647;
    border-radius: 2px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    min-width: 120px;
}

.context-menu-item {
    padding: 4px 12px;
    font-size: 11px;
    cursor: pointer;
    color: #cccccc;
}

.context-menu-item:hover {
    background-color: #007acc;
    color: white;
}

.context-menu-separator {
    height: 1px;
    background-color: #464647;
    margin: 2px 0;
}

/* Modal styles */
.modal-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal {
    background-color: #2d2d30;
    border: 1px solid #464647;
    border-radius: 4px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.5);
    min-width: 300px;
    max-width: 80%;
    max-height: 80%;
}

.modal-header {
    padding: 8px 12px;
    background-color: #3c3c3c;
    border-bottom: 1px solid #464647;
    font-size: 12px;
    font-weight: 600;
}

.modal-content {
    padding: 12px;
}

.modal-footer {
    padding: 8px 12px;
    background-color: #3c3c3c;
    border-top: 1px solid #464647;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

/* Icon styles (using text for now, can be replaced with actual icons) */
.icon-new::before { content: '📄'; }
.icon-load::before { content: '📁'; }
.icon-save::before { content: '💾'; }
.icon-select::before { content: '👆'; }
.icon-sublevel::before { content: '🏠'; }
.icon-spawner::before { content: '⚡'; }
.icon-path::before { content: '🛤️'; }
.icon-play::before { content: '▶️'; }
.icon-stop::before { content: '⏹️'; }
.icon-expand::before { content: '⬇️'; }
.icon-collapse::before { content: '⬆️'; }
.icon-zoom-fit::before { content: '🔍'; }
.icon-zoom-reset::before { content: '🎯'; }
.icon-grid::before { content: '⚏'; }
.icon-reset::before { content: '🔄'; }

/* Scrollbar styles */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background-color: #2d2d30;
}

::-webkit-scrollbar-thumb {
    background-color: #464647;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background-color: #5a5a5a;
}

/* Utility classes */
.hidden {
    display: none !important;
}

.disabled {
    opacity: 0.5;
    pointer-events: none;
}

.text-center {
    text-align: center;
}

.text-muted {
    color: #999999;
}

.mb-4 {
    margin-bottom: 4px;
}

.mb-8 {
    margin-bottom: 8px;
}

.p-4 {
    padding: 4px;
}

.p-8 {
    padding: 8px;
}

/* Property Inspector specific styles */
.property-error {
    color: #f44747;
    font-size: 10px;
    margin-top: 2px;
    padding: 2px 4px;
    background-color: rgba(244, 71, 71, 0.1);
    border-radius: 2px;
}

.property-row.has-error .property-input {
    border-color: #f44747;
    background-color: rgba(244, 71, 71, 0.1);
}

/* Vector editors */
.vector3-editor,
.vector2-editor {
    display: flex;
    gap: 4px;
}

.vector-component {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.vector-label {
    font-size: 9px;
    color: #999999;
    margin-bottom: 1px;
    text-align: center;
}

.vector-input {
    width: 100%;
    min-height: 16px;
    padding: 1px 2px;
    border: 1px solid #464647;
    background-color: #3c3c3c;
    color: #cccccc;
    font-size: 10px;
    border-radius: 2px;
    text-align: center;
}

.vector-input:focus {
    outline: none;
    border-color: #007acc;
    background-color: #252526;
}

/* Rect editor */
.rect-editor {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4px;
}

.rect-component {
    display: flex;
    flex-direction: column;
}

.rect-label {
    font-size: 9px;
    color: #999999;
    margin-bottom: 1px;
}

.rect-input {
    width: 100%;
    min-height: 16px;
    padding: 1px 2px;
    border: 1px solid #464647;
    background-color: #3c3c3c;
    color: #cccccc;
    font-size: 10px;
    border-radius: 2px;
}

.rect-input:focus {
    outline: none;
    border-color: #007acc;
    background-color: #252526;
}

/* Color editor */
.color-editor {
    display: flex;
    align-items: center;
    gap: 4px;
}

.color-picker {
    width: 32px;
    height: 18px;
    border: 1px solid #464647;
    border-radius: 2px;
    cursor: pointer;
}

.alpha-wrapper {
    display: flex;
    align-items: center;
    gap: 2px;
}

.alpha-label {
    font-size: 10px;
    color: #999999;
}

.alpha-input {
    width: 40px;
    min-height: 16px;
    padding: 1px 2px;
    border: 1px solid #464647;
    background-color: #3c3c3c;
    color: #cccccc;
    font-size: 10px;
    border-radius: 2px;
}

.alpha-input:focus {
    outline: none;
    border-color: #007acc;
    background-color: #252526;
}

/* File path editor */
.filepath-editor {
    display: flex;
    gap: 2px;
}

.filepath-input {
    flex: 1;
    min-height: 18px;
    padding: 2px 4px;
    border: 1px solid #464647;
    background-color: #3c3c3c;
    color: #cccccc;
    font-size: 11px;
    border-radius: 2px;
}

.filepath-input:focus {
    outline: none;
    border-color: #007acc;
    background-color: #252526;
}

.browse-btn {
    width: 24px;
    height: 18px;
    padding: 0;
    border: 1px solid #464647;
    background-color: #3c3c3c;
    color: #cccccc;
    font-size: 10px;
    border-radius: 2px;
    cursor: pointer;
}

.browse-btn:hover {
    background-color: #464647;
}

/* Enum editor */
.enum-select {
    width: 100%;
    min-height: 18px;
    padding: 1px 4px;
    border: 1px solid #464647;
    background-color: #3c3c3c;
    color: #cccccc;
    font-size: 11px;
    border-radius: 2px;
}

.enum-select:focus {
    outline: none;
    border-color: #007acc;
    background-color: #252526;
}

/* Boolean editor */
.boolean-editor {
    display: flex;
    align-items: center;
}

.boolean-label {
    display: flex;
    align-items: center;
    font-size: 11px;
    color: #cccccc;
    cursor: pointer;
}

.boolean-checkbox {
    margin-right: 4px;
}

/* Array editor */
.array-editor {
    border: 1px solid #464647;
    border-radius: 2px;
    background-color: #252526;
}

.array-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 6px;
    background-color: #3c3c3c;
    border-bottom: 1px solid #464647;
}

.array-title {
    font-size: 10px;
    font-weight: 600;
    color: #cccccc;
}

.array-add-btn {
    width: 16px;
    height: 16px;
    padding: 0;
    border: 1px solid #464647;
    background-color: #007acc;
    color: white;
    font-size: 10px;
    border-radius: 2px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.array-add-btn:hover {
    background-color: #005a9e;
}

.array-items {
    padding: 4px;
}

.array-item {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 2px;
}

.array-index {
    font-size: 9px;
    color: #999999;
    min-width: 20px;
}

.array-item-input {
    flex: 1;
    min-height: 16px;
    padding: 1px 4px;
    border: 1px solid #464647;
    background-color: #3c3c3c;
    color: #cccccc;
    font-size: 10px;
    border-radius: 2px;
}

.array-item-input:focus {
    outline: none;
    border-color: #007acc;
    background-color: #252526;
}

.array-remove-btn {
    width: 16px;
    height: 16px;
    padding: 0;
    border: 1px solid #464647;
    background-color: #f44747;
    color: white;
    font-size: 10px;
    border-radius: 2px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.array-remove-btn:hover {
    background-color: #d73a49;
}

/* Multiple selection message */
.multiple-selection-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #999999;
    text-align: center;
    padding: 20px;
}

.multiple-selection-message p {
    margin-bottom: 8px;
    font-size: 12px;
}

/* Unsupported type message */
.unsupported-type-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #999999;
    text-align: center;
    padding: 20px;
}

.unsupported-type-message p {
    margin-bottom: 8px;
    font-size: 12px;
}

/* Component-Specific Editor Styles */

/* Spawner Editor */
.spawner-editor,
.path-editor,
.map-layer-editor {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #2d2d30;
}

.spawner-header,
.path-header,
.map-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background-color: #3c3c3c;
    border-bottom: 1px solid #464647;
}

.spawner-header h3,
.path-header h3,
.map-header h3 {
    font-size: 14px;
    font-weight: 600;
    color: #cccccc;
    margin: 0;
}

.spawner-actions,
.path-actions,
.map-actions {
    display: flex;
    gap: 4px;
}

.spawner-content,
.path-content,
.map-content {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
}

.spawner-footer,
.path-footer,
.map-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding: 8px 12px;
    background-color: #3c3c3c;
    border-top: 1px solid #464647;
}

/* Property sections */
.property-section {
    margin-bottom: 16px;
    border: 1px solid #464647;
    border-radius: 4px;
    background-color: #252526;
}

.property-section h4 {
    padding: 6px 8px;
    background-color: #3c3c3c;
    border-bottom: 1px solid #464647;
    font-size: 12px;
    font-weight: 600;
    color: #cccccc;
    margin: 0;
}

.property-grid {
    display: grid;
    grid-template-columns: 100px 1fr;
    gap: 8px 12px;
    padding: 8px;
    align-items: center;
}

.property-grid label {
    font-size: 11px;
    color: #cccccc;
    text-align: right;
}

/* Button styles */
.btn-small {
    padding: 2px 8px;
    font-size: 10px;
    border: 1px solid #464647;
    background-color: #3c3c3c;
    color: #cccccc;
    border-radius: 2px;
    cursor: pointer;
}

.btn-small:hover {
    background-color: #464647;
}

.btn-primary {
    padding: 4px 12px;
    font-size: 11px;
    border: 1px solid #007acc;
    background-color: #007acc;
    color: white;
    border-radius: 2px;
    cursor: pointer;
}

.btn-primary:hover {
    background-color: #005a9e;
}

.btn-secondary {
    padding: 4px 12px;
    font-size: 11px;
    border: 1px solid #464647;
    background-color: #3c3c3c;
    color: #cccccc;
    border-radius: 2px;
    cursor: pointer;
}

.btn-secondary:hover {
    background-color: #464647;
}

/* File input */
.file-input {
    display: flex;
    gap: 2px;
}

.file-input input {
    flex: 1;
}

.browse-btn {
    width: 24px;
    height: 20px;
    padding: 0;
    border: 1px solid #464647;
    background-color: #3c3c3c;
    color: #cccccc;
    font-size: 10px;
    border-radius: 2px;
    cursor: pointer;
}

.browse-btn:hover {
    background-color: #464647;
}

/* Vector inputs */
.vector3-input,
.vector2-input {
    display: flex;
    gap: 4px;
}

.vector-component {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.vector-component label {
    font-size: 9px;
    color: #999999;
    margin-bottom: 2px;
    text-align: center;
}

.vector-component input {
    width: 100%;
    min-height: 18px;
    padding: 2px 4px;
    border: 1px solid #464647;
    background-color: #3c3c3c;
    color: #cccccc;
    font-size: 10px;
    border-radius: 2px;
    text-align: center;
}

/* Property inputs */
.property-input,
.property-select {
    min-height: 20px;
    padding: 2px 6px;
    border: 1px solid #464647;
    background-color: #3c3c3c;
    color: #cccccc;
    font-size: 11px;
    border-radius: 2px;
}

.property-input:focus,
.property-select:focus {
    outline: none;
    border-color: #007acc;
    background-color: #252526;
}

.property-checkbox {
    width: 16px;
    height: 16px;
}

.property-slider {
    width: 100%;
    height: 20px;
}

/* Wave timeline */
.wave-timeline {
    border: 1px solid #464647;
    border-radius: 2px;
    background-color: #252526;
}

.timeline-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 8px;
    background-color: #3c3c3c;
    border-bottom: 1px solid #464647;
    font-size: 11px;
    font-weight: 600;
}

.timeline-content {
    padding: 4px;
}

.wave-item {
    margin-bottom: 8px;
    border: 1px solid #464647;
    border-radius: 2px;
    background-color: #2d2d30;
}

.wave-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 6px;
    background-color: #3c3c3c;
    border-bottom: 1px solid #464647;
    font-size: 10px;
}

.wave-properties {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 4px 8px;
    padding: 6px;
    align-items: center;
}

.wave-properties label {
    font-size: 10px;
    color: #cccccc;
}

.wave-properties input {
    min-height: 16px;
    padding: 1px 4px;
    border: 1px solid #464647;
    background-color: #3c3c3c;
    color: #cccccc;
    font-size: 10px;
    border-radius: 2px;
}

.remove-wave {
    width: 16px;
    height: 16px;
    padding: 0;
    border: 1px solid #464647;
    background-color: #f44747;
    color: white;
    font-size: 10px;
    border-radius: 2px;
    cursor: pointer;
}

.remove-wave:hover {
    background-color: #d73a49;
}

/* Preview styles */
.spawner-preview,
.path-preview,
.map-preview {
    border: 1px solid #464647;
    border-radius: 2px;
    background-color: #252526;
    padding: 8px;
}

.spawner-preview canvas,
.path-preview canvas,
.map-preview canvas {
    display: block;
    border: 1px solid #464647;
    background-color: #1e1e1e;
    margin-bottom: 8px;
}

.preview-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.preview-info,
.scroll-info {
    display: flex;
    gap: 8px;
    font-size: 10px;
    color: #999999;
}

.preview-settings {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 4px 8px;
    align-items: center;
}

.preview-settings label {
    font-size: 10px;
    color: #cccccc;
}

/* Points list */
.points-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.points-count {
    font-size: 11px;
    color: #999999;
}

.points-actions {
    display: flex;
    gap: 4px;
}

.points-list {
    max-height: 300px;
    overflow-y: auto;
}

.point-item {
    margin-bottom: 8px;
    border: 1px solid #464647;
    border-radius: 2px;
    background-color: #2d2d30;
}

.point-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 6px;
    background-color: #3c3c3c;
    border-bottom: 1px solid #464647;
    font-size: 10px;
}

.point-actions {
    display: flex;
    gap: 2px;
}

.point-actions button {
    width: 16px;
    height: 16px;
    padding: 0;
    border: 1px solid #464647;
    background-color: #3c3c3c;
    color: #cccccc;
    font-size: 8px;
    border-radius: 2px;
    cursor: pointer;
}

.point-actions button:hover {
    background-color: #464647;
}

.point-actions button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.point-properties {
    padding: 6px;
}

.property-row {
    display: grid;
    grid-template-columns: 60px 1fr;
    gap: 8px;
    margin-bottom: 6px;
    align-items: center;
}

.property-row label {
    font-size: 10px;
    color: #cccccc;
}

.property-row input {
    min-height: 16px;
    padding: 1px 4px;
    border: 1px solid #464647;
    background-color: #3c3c3c;
    color: #cccccc;
    font-size: 10px;
    border-radius: 2px;
}

.tangent-controls {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid #464647;
}

/* Layer order controls */
.layer-order-controls {
    display: flex;
    gap: 4px;
    margin-bottom: 8px;
}

.layer-order-info {
    font-size: 11px;
    color: #999999;
}

/* No content messages */
.no-points {
    text-align: center;
    color: #999999;
    font-size: 11px;
    padding: 20px;
    font-style: italic;
}
