{"version": "1.0.0", "format": "json", "timestamp": "2025-01-26T10:00:00.000Z", "data": {"metadata": {"name": "Complex Test Level", "version": "1.0.0", "duration": 300, "difficulty": 3, "description": "A complex test level with multiple sublevels, curved paths, and various spawner patterns", "author": "Test Generator", "createdAt": "2025-01-26T10:00:00.000Z", "modifiedAt": "2025-01-26T10:00:00.000Z"}, "subLevels": [{"id": "sublevel_start", "name": "Starting Area", "bounds": {"x": 0, "y": 0, "width": 1920, "height": 1500}, "maps": [{"id": "bg_very_far_1", "prefabPath": "prefabs/backgrounds/stars", "layerType": "BG_VeryFar", "depth": -1000, "scrollSpeed": {"x": 0, "y": 0.1}, "repeatMode": "both", "offset": {"x": 0, "y": 0}, "bounds": {"x": 0, "y": 0, "width": 1920, "height": 1500}, "isVisible": true}, {"id": "bg_mid_1", "prefabPath": "prefabs/backgrounds/nebula", "layerType": "BG_Mid", "depth": -600, "scrollSpeed": {"x": 0, "y": 0.3}, "repeatMode": "vertical", "offset": {"x": 0, "y": 0}, "bounds": {"x": 0, "y": 0, "width": 1920, "height": 1500}, "isVisible": true}], "spawners": [{"id": "spawner_formation_1", "name": "Formation Spawner", "position": {"x": 200, "y": 1400, "z": 0}, "prefabPath": "prefabs/enemies/scout", "spawnPattern": {"type": "sequential", "entities": [{"prefabPath": "prefabs/enemies/scout", "weight": 1.0}, {"prefabPath": "prefabs/enemies/fighter", "weight": 0.7}], "count": 3, "interval": 0.5, "delay": 2.0}, "pathId": "path_curved_left", "isActive": true, "waves": [{"id": "wave_formation_1", "name": "Left Formation", "startTime": 10.0, "endTime": 20.0, "spawnerConfigs": [{"spawnerId": "spawner_formation_1", "spawnTime": 10.0, "interval": 0.5}], "isActive": true}]}, {"id": "spawner_formation_2", "name": "Right Formation Spawner", "position": {"x": 1720, "y": 1400, "z": 0}, "prefabPath": "prefabs/enemies/scout", "spawnPattern": {"type": "sequential", "entities": [{"prefabPath": "prefabs/enemies/scout", "weight": 1.0}], "count": 3, "interval": 0.5, "delay": 2.5}, "pathId": "path_curved_right", "isActive": true, "waves": [{"id": "wave_formation_2", "name": "Right Formation", "startTime": 12.0, "endTime": 22.0, "spawnerConfigs": [{"spawnerId": "spawner_formation_2", "spawnTime": 12.0, "interval": 0.5}], "isActive": true}]}], "paths": [{"id": "path_curved_left", "name": "Curved Left Path", "points": [{"position": {"x": 200, "y": 1400, "z": 0}, "controlPoint1": {"x": 200, "y": 1300, "z": 0}, "controlPoint2": {"x": 300, "y": 1200, "z": 0}, "speed": 150, "rotation": 0}, {"position": {"x": 600, "y": 800, "z": 0}, "controlPoint1": {"x": 500, "y": 900, "z": 0}, "controlPoint2": {"x": 700, "y": 700, "z": 0}, "speed": 180, "rotation": -15}, {"position": {"x": 960, "y": 200, "z": 0}, "controlPoint1": {"x": 860, "y": 300, "z": 0}, "controlPoint2": {"x": 1060, "y": 100, "z": 0}, "speed": 200, "rotation": 0}], "isLoop": false, "totalLength": 1400}, {"id": "path_curved_right", "name": "Curved Right Path", "points": [{"position": {"x": 1720, "y": 1400, "z": 0}, "controlPoint1": {"x": 1720, "y": 1300, "z": 0}, "controlPoint2": {"x": 1620, "y": 1200, "z": 0}, "speed": 150, "rotation": 0}, {"position": {"x": 1320, "y": 800, "z": 0}, "controlPoint1": {"x": 1420, "y": 900, "z": 0}, "controlPoint2": {"x": 1220, "y": 700, "z": 0}, "speed": 180, "rotation": 15}, {"position": {"x": 960, "y": 200, "z": 0}, "controlPoint1": {"x": 1060, "y": 300, "z": 0}, "controlPoint2": {"x": 860, "y": 100, "z": 0}, "speed": 200, "rotation": 0}], "isLoop": false, "totalLength": 1400}], "events": {"nodes": {}, "connections": [], "triggers": []}, "entryPoint": {"position": {"x": 960, "y": 100, "z": 0}, "entryType": "smooth", "showCardSelection": true, "entryAnimation": "fade_in", "entryDelay": 2.0}, "exitPoint": {"position": {"x": 960, "y": 1400, "z": 0}, "exitType": "smooth", "showCardSelection": false, "exitDelay": 1.0}, "connections": [{"targetSubLevelId": "sublevel_middle", "connectionType": "transition", "triggerArea": {"x": 860, "y": 1350, "width": 200, "height": 100}, "transitionData": {"duration": 2.0, "fadeType": "fade", "easing": "ease-in-out"}}]}, {"id": "sublevel_middle", "name": "Middle Section", "bounds": {"x": 0, "y": 1500, "width": 1920, "height": 1500}, "maps": [{"id": "bg_asteroid_field", "prefabPath": "prefabs/backgrounds/asteroid_field", "layerType": "BG_Mid", "depth": -600, "scrollSpeed": {"x": 0.1, "y": 0.4}, "repeatMode": "both", "offset": {"x": 0, "y": 0}, "bounds": {"x": 0, "y": 1500, "width": 1920, "height": 1500}, "isVisible": true}], "spawners": [{"id": "spawner_asteroid_miners", "name": "Asteroid Miners", "position": {"x": 960, "y": 2800, "z": 0}, "prefabPath": "prefabs/enemies/miner", "spawnPattern": {"type": "random", "entities": [{"prefabPath": "prefabs/enemies/miner", "weight": 0.6}, {"prefabPath": "prefabs/enemies/heavy_miner", "weight": 0.4}], "count": 6, "interval": 2.5, "delay": 5.0}, "pathId": "path_zigzag", "isActive": true, "waves": [{"id": "wave_miners", "name": "Asteroid Miners Wave", "startTime": 10.0, "endTime": 35.0, "spawnerConfigs": [{"spawnerId": "spawner_asteroid_miners", "spawnTime": 10.0, "interval": 2.5}], "isActive": true}]}], "paths": [{"id": "path_zigzag", "name": "Zigzag Path", "points": [{"position": {"x": 960, "y": 2800, "z": 0}, "controlPoint1": {"x": 960, "y": 2700, "z": 0}, "controlPoint2": {"x": 1200, "y": 2500, "z": 0}, "speed": 160, "rotation": 0}, {"position": {"x": 1400, "y": 2200, "z": 0}, "controlPoint1": {"x": 1300, "y": 2300, "z": 0}, "controlPoint2": {"x": 1200, "y": 2000, "z": 0}, "speed": 140, "rotation": -25}, {"position": {"x": 520, "y": 1800, "z": 0}, "controlPoint1": {"x": 720, "y": 1900, "z": 0}, "controlPoint2": {"x": 320, "y": 1700, "z": 0}, "speed": 180, "rotation": 25}, {"position": {"x": 960, "y": 1600, "z": 0}, "controlPoint1": {"x": 760, "y": 1650, "z": 0}, "controlPoint2": {"x": 1160, "y": 1550, "z": 0}, "speed": 200, "rotation": 0}], "isLoop": false, "totalLength": 1600}], "events": {"nodes": {}, "connections": [], "triggers": []}, "entryPoint": {"position": {"x": 960, "y": 1600, "z": 0}, "entryType": "smooth", "showCardSelection": false, "entryDelay": 0.5}, "exitPoint": {"position": {"x": 960, "y": 2900, "z": 0}, "exitType": "smooth", "showCardSelection": false, "exitDelay": 1.0}, "connections": [{"targetSubLevelId": "sublevel_boss", "connectionType": "seamless", "triggerArea": {"x": 860, "y": 2850, "width": 200, "height": 100}}]}, {"id": "sublevel_boss", "name": "Final Boss Area", "bounds": {"x": 0, "y": 3000, "width": 1920, "height": 1080}, "maps": [{"id": "bg_boss_chamber", "prefabPath": "prefabs/backgrounds/boss_chamber", "layerType": "BG_Close", "depth": -400, "scrollSpeed": {"x": 0, "y": 0}, "repeatMode": "none", "offset": {"x": 0, "y": 0}, "bounds": {"x": 0, "y": 3000, "width": 1920, "height": 1080}, "isVisible": true}], "spawners": [{"id": "spawner_final_boss", "name": "Final Boss", "position": {"x": 960, "y": 3900, "z": 0}, "prefabPath": "prefabs/bosses/final_boss", "spawnPattern": {"type": "static", "entities": [{"prefabPath": "prefabs/bosses/final_boss", "weight": 1.0}], "count": 1, "interval": 0, "delay": 5.0}, "isActive": true, "waves": [{"id": "wave_final_boss", "name": "Final Boss Spawn", "startTime": 8.0, "endTime": 12.0, "spawnerConfigs": [{"spawnerId": "spawner_final_boss", "spawnTime": 8.0}], "isActive": true}]}], "paths": [], "events": {"nodes": {}, "connections": [], "triggers": []}, "entryPoint": {"position": {"x": 960, "y": 3100, "z": 0}, "entryType": "fade", "showCardSelection": true, "entryAnimation": "boss_entrance", "entryDelay": 3.0}, "connections": []}], "globalEvents": {"nodes": {}, "connections": [], "triggers": [{"id": "trigger_level_start", "type": "time", "condition": {"timeReached": 0}, "targetNodeId": "node_start_music", "isActive": true}]}, "cameraSettings": {"viewportSize": {"width": 1920, "height": 1080}, "scrollSpeed": 120, "followTarget": "player", "smoothing": 3.0, "bounds": {"x": 0, "y": 0, "width": 1920, "height": 1500}}}}