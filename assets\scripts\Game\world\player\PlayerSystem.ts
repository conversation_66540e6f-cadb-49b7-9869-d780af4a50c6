import { _decorator, Vec3, Node } from "cc";
import { System } from "../base/System";
const { ccclass } = _decorator;

/**
 * Player state enumeration
 */
export enum PlayerState {
    IDLE = "idle",
    MOVING = "moving",
    ATTACKING = "attacking",
    TAKING_DAMAGE = "taking_damage",
    DEAD = "dead",
    INVULNERABLE = "invulnerable"
}

/**
 * Player statistics
 */
export interface PlayerStats {
    maxHealth: number;
    currentHealth: number;
    attackPower: number;
    moveSpeed: number;
    defense: number;
    experience: number;
    level: number;
}

/**
 * Player input data
 */
export interface PlayerInput {
    moveDirection: Vec3;
    isAttacking: boolean;
    isJumping: boolean;
    mousePosition: Vec3;
    keyStates: { [key: string]: boolean };
}

/**
 * Player data
 */
export interface PlayerData {
    id: string;
    position: Vec3;
    velocity: Vec3;
    rotation: number;
    state: PlayerState;
    stats: PlayerStats;
    inventory: string[];
    abilities: string[];
    statusEffects: Map<string, number>; // effect name -> remaining time
    lastDamageTime: number;
    invulnerabilityDuration: number;
    node?: Node;
}

/**
 * PlayerSystem - manages player state, movement, combat, and progression
 */
@ccclass("PlayerSystem")
export class PlayerSystem extends System {
    
    private _players: Map<string, PlayerData> = new Map();
    private _currentPlayerId: string | null = null;
    private _inputBuffer: PlayerInput[] = [];
    private _maxInputBufferSize: number = 10;
    
    // Configuration
    private _invulnerabilityTime: number = 2.0; // seconds
    private _maxPlayers: number = 4; // for multiplayer support
    
    /**
     * Get the system name
     */
    public getSystemName(): string {
        return "PlayerSystem";
    }
    
    /**
     * Initialize the player system
     */
    protected onInit(): void {
        console.log("PlayerSystem: Initializing player system");
        
        // Initialize input buffer
        this._inputBuffer = [];
        
        console.log(`PlayerSystem: Initialized with max players: ${this._maxPlayers}`);
    }
    
    /**
     * Cleanup the player system
     */
    protected onUnInit(): void {
        console.log("PlayerSystem: Cleaning up player system");
        
        // Cleanup all players
        this._players.forEach(player => {
            this._destroyPlayer(player);
        });
        
        this._players.clear();
        this._currentPlayerId = null;
        this._inputBuffer.length = 0;
        
        console.log("PlayerSystem: Cleanup complete");
    }
    
    /**
     * Update the player system
     */
    protected onUpdate(deltaTime: number): void {
        // Process input buffer
        this._processInputBuffer();
        
        // Update all players
        this._players.forEach(player => {
            this._updatePlayer(player, deltaTime);
        });
    }
    
    /**
     * Late update - handle any post-update logic
     */
    protected onLateUpdate(deltaTime: number): void {
        // Update visual representations
        this._players.forEach(player => {
            this._updatePlayerVisuals(player);
        });
    }
    
    /**
     * Create a new player
     * @param playerId Unique identifier for the player
     * @param startPosition Starting position for the player
     * @param initialStats Initial player statistics
     * @returns true if the player was created successfully
     */
    public createPlayer(playerId: string, startPosition: Vec3, initialStats: Partial<PlayerStats> = {}): boolean {
        if (this._players.has(playerId)) {
            console.warn(`PlayerSystem: Player ${playerId} already exists`);
            return false;
        }
        
        if (this._players.size >= this._maxPlayers) {
            console.warn("PlayerSystem: Cannot create player - max players reached");
            return false;
        }
        
        // Create default stats
        const defaultStats: PlayerStats = {
            maxHealth: 100,
            currentHealth: 100,
            attackPower: 10,
            moveSpeed: 300,
            defense: 5,
            experience: 0,
            level: 1
        };
        
        // Merge with provided stats
        const stats = { ...defaultStats, ...initialStats };
        stats.currentHealth = stats.maxHealth; // Ensure current health doesn't exceed max
        
        // Create player data
        const player: PlayerData = {
            id: playerId,
            position: new Vec3(startPosition),
            velocity: new Vec3(),
            rotation: 0,
            state: PlayerState.IDLE,
            stats: stats,
            inventory: [],
            abilities: [],
            statusEffects: new Map(),
            lastDamageTime: 0,
            invulnerabilityDuration: 0,
            node: undefined
        };
        
        this._players.set(playerId, player);
        
        // Set as current player if it's the first one
        if (!this._currentPlayerId) {
            this._currentPlayerId = playerId;
        }
        
        console.log(`PlayerSystem: Created player ${playerId}`);
        return true;
    }
    
    /**
     * Remove a player
     * @param playerId The ID of the player to remove
     * @returns true if the player was removed
     */
    public removePlayer(playerId: string): boolean {
        const player = this._players.get(playerId);
        if (!player) {
            return false;
        }
        
        this._destroyPlayer(player);
        this._players.delete(playerId);
        
        // Update current player if necessary
        if (this._currentPlayerId === playerId) {
            this._currentPlayerId = this._players.size > 0 ? this._players.keys().next().value : null;
        }
        
        console.log(`PlayerSystem: Removed player ${playerId}`);
        return true;
    }
    
    /**
     * Get a player by ID
     * @param playerId The ID of the player to get
     * @returns The player data or null if not found
     */
    public getPlayer(playerId: string): PlayerData | null {
        return this._players.get(playerId) || null;
    }
    
    /**
     * Get the current active player
     * @returns The current player data or null if no current player
     */
    public getCurrentPlayer(): PlayerData | null {
        return this._currentPlayerId ? this._players.get(this._currentPlayerId) || null : null;
    }
    
    /**
     * Set the current active player
     * @param playerId The ID of the player to set as current
     * @returns true if the player was set as current
     */
    public setCurrentPlayer(playerId: string): boolean {
        if (!this._players.has(playerId)) {
            return false;
        }
        
        this._currentPlayerId = playerId;
        console.log(`PlayerSystem: Set current player to ${playerId}`);
        return true;
    }
    
    /**
     * Apply damage to a player
     * @param playerId The ID of the player to damage
     * @param damage The amount of damage to apply
     * @param damageSource Optional source of the damage
     * @returns true if damage was applied
     */
    public damagePlayer(playerId: string, damage: number, damageSource?: string): boolean {
        const player = this._players.get(playerId);
        if (!player || player.state === PlayerState.DEAD) {
            return false;
        }
        
        // Check invulnerability
        if (player.invulnerabilityDuration > 0) {
            console.log(`PlayerSystem: Player ${playerId} is invulnerable, damage ignored`);
            return false;
        }
        
        // Calculate actual damage (apply defense)
        const actualDamage = Math.max(1, damage - player.stats.defense);
        
        // Apply damage
        player.stats.currentHealth = Math.max(0, player.stats.currentHealth - actualDamage);
        player.lastDamageTime = Date.now();
        player.invulnerabilityDuration = this._invulnerabilityTime;
        
        // Update state
        if (player.stats.currentHealth <= 0) {
            player.state = PlayerState.DEAD;
            console.log(`PlayerSystem: Player ${playerId} died`);
        } else {
            player.state = PlayerState.TAKING_DAMAGE;
        }
        
        console.log(`PlayerSystem: Player ${playerId} took ${actualDamage} damage (${player.stats.currentHealth}/${player.stats.maxHealth} HP remaining)`);
        return true;
    }
    
    /**
     * Heal a player
     * @param playerId The ID of the player to heal
     * @param healAmount The amount of health to restore
     * @returns true if healing was applied
     */
    public healPlayer(playerId: string, healAmount: number): boolean {
        const player = this._players.get(playerId);
        if (!player || player.state === PlayerState.DEAD) {
            return false;
        }
        
        const oldHealth = player.stats.currentHealth;
        player.stats.currentHealth = Math.min(player.stats.maxHealth, player.stats.currentHealth + healAmount);
        
        const actualHealing = player.stats.currentHealth - oldHealth;
        if (actualHealing > 0) {
            console.log(`PlayerSystem: Player ${playerId} healed for ${actualHealing} HP`);
            return true;
        }
        
        return false;
    }
    
    /**
     * Add input to the input buffer
     * @param input The input data to add
     */
    public addInput(input: PlayerInput): void {
        this._inputBuffer.push(input);
        
        // Limit buffer size
        if (this._inputBuffer.length > this._maxInputBufferSize) {
            this._inputBuffer.shift();
        }
    }
    
    /**
     * Get all players
     * @returns Array of all player data
     */
    public getAllPlayers(): PlayerData[] {
        return Array.from(this._players.values());
    }
    
    /**
     * Get the number of active players
     * @returns The number of active players
     */
    public getPlayerCount(): number {
        return this._players.size;
    }
    
    /**
     * Update a single player
     */
    private _updatePlayer(player: PlayerData, deltaTime: number): void {
        // Update invulnerability
        if (player.invulnerabilityDuration > 0) {
            player.invulnerabilityDuration -= deltaTime;
            if (player.invulnerabilityDuration <= 0) {
                player.invulnerabilityDuration = 0;
                if (player.state === PlayerState.TAKING_DAMAGE) {
                    player.state = PlayerState.IDLE;
                }
            }
        }
        
        // Update status effects
        player.statusEffects.forEach((duration, effect) => {
            const newDuration = duration - deltaTime;
            if (newDuration <= 0) {
                player.statusEffects.delete(effect);
                console.log(`PlayerSystem: Status effect ${effect} expired for player ${player.id}`);
            } else {
                player.statusEffects.set(effect, newDuration);
            }
        });
        
        // Update position based on velocity
        player.position.add(Vec3.multiplyScalar(new Vec3(), player.velocity, deltaTime));
        
        // Apply friction to velocity
        player.velocity.multiplyScalar(0.9);
    }
    
    /**
     * Process the input buffer
     */
    private _processInputBuffer(): void {
        if (this._inputBuffer.length === 0 || !this._currentPlayerId) {
            return;
        }
        
        const player = this._players.get(this._currentPlayerId);
        if (!player || player.state === PlayerState.DEAD) {
            this._inputBuffer.length = 0; // Clear buffer
            return;
        }
        
        // Process the most recent input
        const input = this._inputBuffer.pop();
        if (input) {
            this._applyInputToPlayer(player, input);
        }
        
        // Clear remaining inputs (we only process the latest)
        this._inputBuffer.length = 0;
    }
    
    /**
     * Apply input to a player
     */
    private _applyInputToPlayer(player: PlayerData, input: PlayerInput): void {
        // Apply movement
        if (input.moveDirection.length() > 0) {
            const moveForce = Vec3.multiplyScalar(new Vec3(), input.moveDirection.normalize(), player.stats.moveSpeed);
            player.velocity.add(moveForce);
            player.state = PlayerState.MOVING;
        } else if (player.state === PlayerState.MOVING) {
            player.state = PlayerState.IDLE;
        }
        
        // Apply attack
        if (input.isAttacking && player.state !== PlayerState.ATTACKING && player.state !== PlayerState.TAKING_DAMAGE) {
            player.state = PlayerState.ATTACKING;
            // Attack logic would go here
        }
    }
    
    /**
     * Update player visual representation
     */
    private _updatePlayerVisuals(player: PlayerData): void {
        if (player.node && player.node.isValid) {
            player.node.setPosition(player.position);
            player.node.setRotationFromEuler(0, 0, player.rotation);
        }
    }
    
    /**
     * Destroy a player's visual representation
     */
    private _destroyPlayer(player: PlayerData): void {
        if (player.node && player.node.isValid) {
            player.node.destroy();
            player.node = undefined;
        }
    }
}
