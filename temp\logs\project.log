2025-7-26 14:50:39 - log: Load engine in C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine
2025-7-26 14:50:39 - log: Register native engine in C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\native
2025-7-26 14:50:39 - log: Request namespace: device-list
2025-7-26 14:50:44 - info: [PreviewInEditor] preview process is ready
2025-7-26 14:50:44 - log: [Scene] meshopt wasm decoder initialized
2025-7-26 14:50:44 - log: [Scene] [bullet]:bullet wasm lib loaded.
2025-7-26 14:50:44 - log: [Scene] [box2d]:box2d wasm lib loaded.
2025-7-26 14:50:44 - log: [Scene] [PHYSICS]: using builtin.
2025-7-26 14:50:44 - log: [Scene] Cocos Creator v3.8.6
2025-7-26 14:50:44 - log: [Scene] Using custom pipeline: Builtin
2025-7-26 14:50:44 - log: [Scene] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-7-26 14:52:21 - info: [PreviewInEditor] preview process is ready
2025-7-26 14:52:21 - log: [Scene] meshopt wasm decoder initialized
2025-7-26 14:52:21 - log: [Scene] [bullet]:bullet wasm lib loaded.
2025-7-26 14:52:21 - log: [Scene] [box2d]:box2d wasm lib loaded.
2025-7-26 14:52:21 - log: [Scene] [PHYSICS]: using builtin.
2025-7-26 14:52:22 - log: [Scene] Cocos Creator v3.8.6
2025-7-26 14:52:22 - log: [Scene] Using custom pipeline: Builtin
2025-7-26 14:52:22 - log: [Scene] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-7-26 14:53:06 - warn: [Package] level-editor-extension: package_version is not defined
2025-7-26 14:53:06 - error: The plug-in load has encountered some problems.
  Path: E:\M2Game\Client\extensions\level-editor-extension
2025-7-26 14:53:06 - error: Editor.log is not a functionTypeError: Editor.log is not a function
    at Package.load (E:\M2Game\Client\extensions\level-editor-extension\main.js:16:12)
    at Package.execLifeFunc (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\package.ccc:1:1221)
    at Package.enable (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\package.ccc:1:3489)
    at PackageManager.enable (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\index.js:1:1763)
    at Object.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\package\public\browser.ccc:1:2408)
    at Generator.next (<anonymous>)
    at i (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\package\public\browser.ccc:1:131)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-7-26 15:00:04 - warn: [Package] level-editor-extension: package_version is not defined
2025-7-26 15:00:04 - log: [Level Editor] Extension loaded
2025-7-26 15:00:04 - error: The plug-in load has encountered some problems.
  Path: E:\M2Game\Client\extensions\level-editor-extension
2025-7-26 15:00:04 - error: this.setupFileWatchers is not a functionTypeError: this.setupFileWatchers is not a function
    at Package.load (E:\M2Game\Client\extensions\level-editor-extension\main.js:24:10)
    at Package.execLifeFunc (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\package.ccc:1:1221)
    at Package.enable (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\package.ccc:1:3489)
    at PackageManager.enable (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\index.js:1:1763)
    at Object.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\package\public\browser.ccc:1:2408)
    at Generator.next (<anonymous>)
    at i (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\package\public\browser.ccc:1:131)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-7-26 15:00:05 - warn: [Package] level-editor-extension: package_version is not defined
2025-7-26 15:00:05 - log: [Level Editor] Extension loaded
2025-7-26 15:00:05 - error: The plug-in load has encountered some problems.
  Path: E:\M2Game\Client\extensions\level-editor-extension
2025-7-26 15:00:05 - error: this.setupFileWatchers is not a functionTypeError: this.setupFileWatchers is not a function
    at Package.load (E:\M2Game\Client\extensions\level-editor-extension\main.js:24:10)
    at Package.execLifeFunc (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\package.ccc:1:1221)
    at Package.enable (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\package.ccc:1:3489)
    at PackageManager.enable (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\index.js:1:1763)
    at Object.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\package\public\browser.ccc:1:2408)
    at Generator.next (<anonymous>)
    at i (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\package\public\browser.ccc:1:131)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-7-26 15:00:08 - warn: [Package] level-editor-extension: package_version is not defined
2025-7-26 15:00:08 - log: [Level Editor] Extension loaded
2025-7-26 15:00:08 - error: The plug-in load has encountered some problems.
  Path: E:\M2Game\Client\extensions\level-editor-extension
2025-7-26 15:00:08 - error: this.setupFileWatchers is not a functionTypeError: this.setupFileWatchers is not a function
    at Package.load (E:\M2Game\Client\extensions\level-editor-extension\main.js:24:10)
    at Package.execLifeFunc (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\package.ccc:1:1221)
    at Package.enable (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\package.ccc:1:3489)
    at PackageManager.enable (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\index.js:1:1763)
    at Object.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\package\public\browser.ccc:1:2408)
    at Generator.next (<anonymous>)
    at i (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\package\public\browser.ccc:1:131)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-7-26 15:00:20 - info: [PreviewInEditor] preview process is ready
2025-7-26 15:00:20 - log: [Scene] meshopt wasm decoder initialized
2025-7-26 15:00:20 - log: [Scene] [bullet]:bullet wasm lib loaded.
2025-7-26 15:00:20 - log: [Scene] [box2d]:box2d wasm lib loaded.
2025-7-26 15:00:20 - log: [Scene] [PHYSICS]: using builtin.
2025-7-26 15:00:20 - log: [Scene] Cocos Creator v3.8.6
2025-7-26 15:00:20 - log: [Scene] Using custom pipeline: Builtin
2025-7-26 15:00:20 - log: [Scene] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-7-26 15:00:23 - warn: [Package] level-editor-extension: package_version is not defined
2025-7-26 15:00:23 - log: [Level Editor] Extension loaded
2025-7-26 15:00:23 - error: The plug-in load has encountered some problems.
  Path: E:\M2Game\Client\extensions\level-editor-extension
2025-7-26 15:00:23 - error: this.setupFileWatchers is not a functionTypeError: this.setupFileWatchers is not a function
    at Package.load (E:\M2Game\Client\extensions\level-editor-extension\main.js:24:10)
    at Package.execLifeFunc (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\package.ccc:1:1221)
    at Package.enable (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\package.ccc:1:3489)
    at PackageManager.enable (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\index.js:1:1763)
    at Object.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\package\public\browser.ccc:1:2408)
    at Generator.next (<anonymous>)
    at i (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\package\public\browser.ccc:1:131)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-7-26 15:04:11 - info: [PreviewInEditor] preview process is ready
2025-7-26 15:04:11 - log: [Scene] meshopt wasm decoder initialized
2025-7-26 15:04:11 - log: [Scene] [bullet]:bullet wasm lib loaded.
2025-7-26 15:04:11 - log: [Scene] [box2d]:box2d wasm lib loaded.
2025-7-26 15:04:11 - log: [Scene] [PHYSICS]: using builtin.
2025-7-26 15:04:11 - log: [Scene] Cocos Creator v3.8.6
2025-7-26 15:04:11 - log: [Scene] Using custom pipeline: Builtin
2025-7-26 15:04:11 - log: [Scene] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-7-26 15:04:16 - warn: [Package] level-editor-extension: package_version is not defined
2025-7-26 15:04:16 - log: [Level Editor] Extension loaded
2025-7-26 15:04:16 - error: The plug-in load has encountered some problems.
  Path: E:\M2Game\Client\extensions\level-editor-extension
2025-7-26 15:04:16 - error: this.setupFileWatchers is not a functionTypeError: this.setupFileWatchers is not a function
    at Package.load (E:\M2Game\Client\extensions\level-editor-extension\main.js:24:10)
    at Package.execLifeFunc (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\package.ccc:1:1221)
    at Package.enable (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\package.ccc:1:3489)
    at PackageManager.enable (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\index.js:1:1763)
    at Object.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\package\public\browser.ccc:1:2408)
    at Generator.next (<anonymous>)
    at i (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\package\public\browser.ccc:1:131)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-7-26 15:05:27 - log: [Level Editor] Extension loaded
2025-7-26 15:05:27 - error: The plug-in load has encountered some problems.
  Path: E:\M2Game\Client\extensions\level-editor-extension
2025-7-26 15:05:27 - error: this.setupFileWatchers is not a functionTypeError: this.setupFileWatchers is not a function
    at Package.load (E:\M2Game\Client\extensions\level-editor-extension\main.js:24:10)
    at Package.execLifeFunc (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\package.ccc:1:1221)
    at Package.enable (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\package.ccc:1:3489)
    at PackageManager.enable (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\index.js:1:1763)
    at Object.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\package\public\browser.ccc:1:2408)
    at Generator.next (<anonymous>)
    at i (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\package\public\browser.ccc:1:131)
2025-7-26 15:05:48 - log: [Level Editor] Extension loaded
2025-7-26 15:05:48 - error: The plug-in load has encountered some problems.
  Path: E:\M2Game\Client\extensions\level-editor-extension
2025-7-26 15:05:48 - error: this.setupFileWatchers is not a functionTypeError: this.setupFileWatchers is not a function
    at Package.load (E:\M2Game\Client\extensions\level-editor-extension\main.js:24:10)
    at Package.execLifeFunc (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\package.ccc:1:1221)
    at Package.enable (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\package.ccc:1:3489)
    at PackageManager.enable (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\index.js:1:1763)
    at Object.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\package\public\browser.ccc:1:2408)
    at Generator.next (<anonymous>)
    at i (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\package\public\browser.ccc:1:131)
2025-7-26 15:08:28 - info: [PreviewInEditor] preview process is ready
2025-7-26 15:08:28 - log: [Scene] meshopt wasm decoder initialized
2025-7-26 15:08:28 - log: [Scene] [box2d]:box2d wasm lib loaded.
2025-7-26 15:08:28 - log: [Scene] [bullet]:bullet wasm lib loaded.
2025-7-26 15:08:28 - log: [Scene] [PHYSICS]: using builtin.
2025-7-26 15:08:28 - log: [Scene] Cocos Creator v3.8.6
2025-7-26 15:08:28 - log: [Scene] Using custom pipeline: Builtin
2025-7-26 15:08:28 - log: [Scene] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-7-26 15:08:32 - log: [Level Editor] Extension loaded
2025-7-26 15:13:38 - info: [PreviewInEditor] preview process is ready
2025-7-26 15:13:38 - log: [Scene] meshopt wasm decoder initialized
2025-7-26 15:13:38 - log: [Scene] [bullet]:bullet wasm lib loaded.
2025-7-26 15:13:38 - log: [Scene] [box2d]:box2d wasm lib loaded.
2025-7-26 15:13:38 - log: [Scene] [PHYSICS]: using builtin.
2025-7-26 15:13:38 - log: [Scene] Cocos Creator v3.8.6
2025-7-26 15:13:38 - log: [Scene] Using custom pipeline: Builtin
2025-7-26 15:13:38 - log: [Scene] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-7-26 15:14:55 - log: [Level Editor] Extension unloaded
2025-7-26 15:14:55 - error: The plug-in unload has encountered some problems.
  Path: E:\M2Game\Client\extensions\level-editor-extension
2025-7-26 15:14:55 - error: this.cleanup is not a functionTypeError: this.cleanup is not a function
    at Package.unload (E:\M2Game\Client\extensions\level-editor-extension\main.js:34:10)
    at Package.execLifeFunc (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\package.ccc:1:1221)
    at Package.disable (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\package.ccc:1:3807)
    at PackageManager.disable (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\index.js:1:2114)
2025-7-26 15:14:58 - log: [Level Editor] Extension loaded
2025-7-26 15:18:49 - info: [PreviewInEditor] preview process is ready
2025-7-26 15:18:49 - log: [Scene] meshopt wasm decoder initialized
2025-7-26 15:18:49 - log: [Scene] [bullet]:bullet wasm lib loaded.
2025-7-26 15:18:49 - log: [Scene] [box2d]:box2d wasm lib loaded.
2025-7-26 15:18:49 - log: [Scene] [PHYSICS]: using builtin.
2025-7-26 15:18:49 - log: [Scene] Cocos Creator v3.8.6
2025-7-26 15:18:49 - log: [Scene] Using custom pipeline: Builtin
2025-7-26 15:18:49 - log: [Scene] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-7-26 15:19:18 - info: [PreviewInEditor] preview process is ready
2025-7-26 15:19:18 - log: [Scene] meshopt wasm decoder initialized
2025-7-26 15:19:18 - log: [Scene] [box2d]:box2d wasm lib loaded.
2025-7-26 15:19:18 - log: [Scene] [bullet]:bullet wasm lib loaded.
2025-7-26 15:19:18 - log: [Scene] [PHYSICS]: using builtin.
2025-7-26 15:19:18 - log: [Scene] Cocos Creator v3.8.6
2025-7-26 15:19:19 - log: [Scene] Using custom pipeline: Builtin
2025-7-26 15:19:19 - log: [Scene] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-7-26 15:19:29 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-7-26 15:19:29 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-7-26 15:19:29 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-7-26 15:19:29 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-7-26 15:19:29 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-7-26 15:19:29 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-7-26 15:19:29 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-7-26 15:19:30 - warn: [PreviewInEditor] 'cc.loader' is deprecated, please use 'cc.assetManager' instead. Error: [PreviewInEditor] 'cc.loader' is deprecated, please use 'cc.assetManager' instead. 
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at replacePropertyLog (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:151887:11)
    at Object.get (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:151936:19)
    at ResUpdate.start (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9d/9d15e7d22d1a1328ffa5d899e41c0fefa70e2b52.js:103:14)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49048:16)
    at OneOffInvoker.eval [as _invoke] (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:48877:9)
    at OneOffInvoker.invoke (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:48991:16)
    at ComponentScheduler.startPhase (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49222:29)
    at Director.tick (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:19507:35)
    at Game._updateCallback (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:20564:22)
    at updateCallback (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:78359:20)
    at sentryWrapped (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\src\helpers.ts:100:17)
2025-7-26 15:19:30 - log: [PreviewInEditor] ybgg start load main scene
2025-7-26 15:19:30 - log: [PreviewInEditor] load main scene success
2025-7-26 15:19:30 - log: [PreviewInEditor] LubanMgr initialized with tables: 10
2025-7-26 15:19:54 - log: [PreviewInEditor] LubanMgr initialized with tables: 10
2025-7-26 15:19:56 - warn: [PreviewInEditor] IPC message has been lost.Error: [PreviewInEditor] IPC message has been lost.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at IpcRenderer.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\ipc\web\webview.ccc:1:1495)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
2025-7-26 15:19:56 - info: [PreviewInEditor] preview process is ready
2025-7-26 15:20:21 - log: [Level Editor] Extension unloaded
2025-7-26 15:20:21 - error: The plug-in unload has encountered some problems.
  Path: E:\M2Game\Client\extensions\level-editor-extension
2025-7-26 15:20:21 - error: this.cleanup is not a functionTypeError: this.cleanup is not a function
    at Package.unload (E:\M2Game\Client\extensions\level-editor-extension\main.js:34:10)
    at Package.execLifeFunc (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\package.ccc:1:1221)
    at Package.disable (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\package.ccc:1:3807)
    at PackageManager.disable (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\index.js:1:2114)
2025-7-26 15:20:22 - log: [Level Editor] Extension loaded
2025-7-26 15:20:26 - log: [Level Editor] Extension unloaded
2025-7-26 15:20:27 - log: [Level Editor] Extension loaded
2025-7-26 15:20:36 - error: Message does not exist: level-editor-extension - open-panelError: Message does not exist: level-editor-extension - open-panel
    at send (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\message\protected\browser\index.ccc:1:2166)
    at Object.send (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\message\public\browser.ccc:1:284)
    at Object.send (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\message\index.ccc:1:874)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\menu\protected\browser\utils.ccc:1:1659
    at Generator.next (<anonymous>)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\menu\protected\browser\utils.ccc:1:327
    at new Promise (<anonymous>)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\menu\protected\browser\utils.ccc:1:75
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\menu\protected\browser\utils.ccc:1:1548
    at MenuItem.click (node:electron/js2c/browser_init:2:34382)
2025-7-26 15:20:58 - error: Message does not exist: level-editor-extension - open-panelError: Message does not exist: level-editor-extension - open-panel
    at send (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\message\protected\browser\index.ccc:1:2166)
    at Object.send (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\message\public\browser.ccc:1:284)
    at Object.send (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\message\index.ccc:1:874)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\menu\protected\browser\utils.ccc:1:1659
    at Generator.next (<anonymous>)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\menu\protected\browser\utils.ccc:1:327
    at new Promise (<anonymous>)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\menu\protected\browser\utils.ccc:1:75
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\menu\protected\browser\utils.ccc:1:1548
    at MenuItem.click (node:electron/js2c/browser_init:2:34382)
2025-7-26 15:23:12 - info: [PreviewInEditor] preview process is ready
2025-7-26 15:23:12 - log: [Scene] meshopt wasm decoder initialized
2025-7-26 15:23:12 - log: [Scene] [box2d]:box2d wasm lib loaded.
2025-7-26 15:23:12 - log: [Scene] [bullet]:bullet wasm lib loaded.
2025-7-26 15:23:12 - log: [Scene] [PHYSICS]: using builtin.
2025-7-26 15:23:12 - log: [Scene] Cocos Creator v3.8.6
2025-7-26 15:23:12 - log: [Scene] Using custom pipeline: Builtin
2025-7-26 15:23:12 - log: [Scene] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-7-26 15:23:15 - log: [Level Editor] Extension unloaded
2025-7-26 15:23:16 - log: [Level Editor] Extension loaded
2025-7-26 15:23:19 - error: Message does not exist: level-editor-extension - open-panelError: Message does not exist: level-editor-extension - open-panel
    at send (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\message\protected\browser\index.ccc:1:2166)
    at Object.send (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\message\public\browser.ccc:1:284)
    at Object.send (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\message\index.ccc:1:874)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\menu\protected\browser\utils.ccc:1:1659
    at Generator.next (<anonymous>)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\menu\protected\browser\utils.ccc:1:327
    at new Promise (<anonymous>)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\menu\protected\browser\utils.ccc:1:75
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\menu\protected\browser\utils.ccc:1:1548
    at MenuItem.click (node:electron/js2c/browser_init:2:34382)
2025-7-26 15:25:22 - info: [PreviewInEditor] preview process is ready
2025-7-26 15:25:22 - log: [Scene] meshopt wasm decoder initialized
2025-7-26 15:25:22 - log: [Scene] [bullet]:bullet wasm lib loaded.
2025-7-26 15:25:22 - log: [Scene] [box2d]:box2d wasm lib loaded.
2025-7-26 15:25:22 - log: [Scene] [PHYSICS]: using builtin.
2025-7-26 15:25:22 - log: [Scene] Cocos Creator v3.8.6
2025-7-26 15:25:22 - log: [Scene] Using custom pipeline: Builtin
2025-7-26 15:25:22 - log: [Scene] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-7-26 15:25:27 - log: [Level Editor] Extension unloaded
2025-7-26 15:25:27 - log: [Level Editor] Extension loaded
2025-7-26 15:25:33 - error: Message does not exist: level-editor-extension - open-panelError: Message does not exist: level-editor-extension - open-panel
    at send (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\message\protected\browser\index.ccc:1:2166)
    at Object.send (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\message\public\browser.ccc:1:284)
    at Object.send (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\message\index.ccc:1:874)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\menu\protected\browser\utils.ccc:1:1659
    at Generator.next (<anonymous>)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\menu\protected\browser\utils.ccc:1:327
    at new Promise (<anonymous>)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\menu\protected\browser\utils.ccc:1:75
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\menu\protected\browser\utils.ccc:1:1548
    at MenuItem.click (node:electron/js2c/browser_init:2:34382)
2025-7-26 15:39:10 - error: (node:399572) MaxListenersExceededWarning: Possible EventEmitter memory leak detected. 11 render-view-deleted listeners added to [WebContents]. MaxListeners is 10. Use emitter.setMaxListeners() to increase limit
(Use `CocosCreator --trace-warnings ...` to show where the warning was created)
2025-7-26 15:39:12 - info: [PreviewInEditor] preview process is ready
2025-7-26 15:39:12 - log: [Scene] meshopt wasm decoder initialized
2025-7-26 15:39:12 - log: [Scene] [box2d]:box2d wasm lib loaded.
2025-7-26 15:39:12 - log: [Scene] [bullet]:bullet wasm lib loaded.
2025-7-26 15:39:12 - log: [Scene] [PHYSICS]: using builtin.
2025-7-26 15:39:12 - log: [Scene] Cocos Creator v3.8.6
2025-7-26 15:39:12 - log: [Scene] Using custom pipeline: Builtin
2025-7-26 15:39:12 - log: [Scene] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-7-26 15:39:13 - log: [Level Editor] Extension unloaded
2025-7-26 15:39:14 - log: [Level Editor] Extension loaded
2025-7-26 15:39:21 - error: [Window] Panel[level-editor-extension.level-editor] cannot be loaded - Editor.Panel.extend is not a functionTypeError: Panel[level-editor-extension.level-editor] cannot be loaded - Editor.Panel.extend is not a function
    at Object.<anonymous> (E:\M2Game\Client\extensions\level-editor-extension\panel\index.js:6:14)
    at Object.<anonymous> (E:\M2Game\Client\extensions\level-editor-extension\panel\index.js:180:3)
    at Module._compile (node:internal/modules/cjs/loader:1373:14)
    at Object.extFunction (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\require.js:1:2233)
    at Module.load (node:internal/modules/cjs/loader:1215:32)
    at Module._load (node:internal/modules/cjs/loader:1031:12)
    at c._load (node:electron/js2c/node_init:2:17025)
    at s._load (node:electron/js2c/renderer_init:2:30909)
    at Module.require (node:internal/modules/cjs/loader:1240:19)
    at r (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\creator\dist\require.js:1:2858)
    at loadPanel (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\panel\lib\element.ccc:1:5651)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\panel\lib\element.ccc:1:4434
    at sentryWrapped (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\browser\cjs\helpers.js:93:17)
2025-7-26 15:42:23 - info: [PreviewInEditor] preview process is ready
2025-7-26 15:42:23 - log: [Scene] meshopt wasm decoder initialized
2025-7-26 15:42:23 - log: [Scene] [box2d]:box2d wasm lib loaded.
2025-7-26 15:42:23 - log: [Scene] [bullet]:bullet wasm lib loaded.
2025-7-26 15:42:23 - log: [Scene] [PHYSICS]: using builtin.
2025-7-26 15:42:23 - log: [Scene] Cocos Creator v3.8.6
2025-7-26 15:42:23 - log: [Scene] Using custom pipeline: Builtin
2025-7-26 15:42:23 - log: [Scene] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-7-26 15:42:25 - log: [Level Editor] Extension unloaded
2025-7-26 15:42:25 - log: [Level Editor] Extension loaded
2025-7-26 15:42:29 - error: [Window] this.initializePanel is not a functionTypeError: this.initializePanel is not a function
    at Object.<anonymous> (E:\M2Game\Client\extensions\level-editor-extension\panel\index.js:25:8)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\panel\lib\element.ccc:1:4682
    at new Promise (<anonymous>)
    at PanelFrame.emit (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\panel\lib\element.ccc:1:4637)
    at loadPanel (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\panel\lib\element.ccc:1:7242)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\panel\lib\element.ccc:1:4434
    at sentryWrapped (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\browser\cjs\helpers.js:93:17)
2025-7-26 15:42:29 - error: [Window] this.initializePanel is not a functionTypeError: this.initializePanel is not a function
    at Object.<anonymous> (E:\M2Game\Client\extensions\level-editor-extension\panel\index.js:25:8)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\panel\lib\element.ccc:1:4682
    at new Promise (<anonymous>)
    at PanelFrame.emit (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\panel\lib\element.ccc:1:4637)
    at loadPanel (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\panel\lib\element.ccc:1:7242)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\panel\lib\element.ccc:1:4434
    at sentryWrapped (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\browser\cjs\helpers.js:93:17)
2025-7-26 15:46:06 - log: [Window] [Level Editor Panel] Panel closed
2025-7-26 15:46:07 - log: [Level Editor] Extension unloaded
2025-7-26 15:46:08 - log: [Level Editor] Extension loaded
2025-7-26 15:46:14 - log: [Window] [Level Editor Panel] Panel ready
2025-7-26 15:46:14 - error: [Window] Cannot read properties of undefined (reading 'addEventListener')TypeError: Cannot read properties of undefined (reading 'addEventListener')
    at Object.<anonymous> (E:\M2Game\Client\extensions\level-editor-extension\panel\index.js:32:15)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\panel\lib\element.ccc:1:4682
    at new Promise (<anonymous>)
    at PanelFrame.emit (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\panel\lib\element.ccc:1:4637)
    at loadPanel (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\panel\lib\element.ccc:1:7242)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\panel\lib\element.ccc:1:4434
    at sentryWrapped (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\browser\cjs\helpers.js:93:17)
2025-7-26 15:46:14 - error: [Window] Cannot read properties of undefined (reading 'addEventListener')TypeError: Cannot read properties of undefined (reading 'addEventListener')
    at Object.<anonymous> (E:\M2Game\Client\extensions\level-editor-extension\panel\index.js:32:15)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\panel\lib\element.ccc:1:4682
    at new Promise (<anonymous>)
    at PanelFrame.emit (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\panel\lib\element.ccc:1:4637)
    at loadPanel (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\panel\lib\element.ccc:1:7242)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\panel\lib\element.ccc:1:4434
    at sentryWrapped (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\browser\cjs\helpers.js:93:17)
2025-7-26 15:47:23 - log: [Window] [Level Editor Panel] Panel closed
2025-7-26 15:47:30 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-7-26 15:47:30 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-7-26 15:47:30 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-7-26 15:47:30 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-7-26 15:47:30 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-7-26 15:47:30 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-7-26 15:47:30 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-7-26 15:47:31 - warn: [PreviewInEditor] 'cc.loader' is deprecated, please use 'cc.assetManager' instead. Error: [PreviewInEditor] 'cc.loader' is deprecated, please use 'cc.assetManager' instead. 
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at replacePropertyLog (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:151887:11)
    at Object.get (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:151936:19)
    at ResUpdate.start (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/9d/9d15e7d22d1a1328ffa5d899e41c0fefa70e2b52.js:103:14)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49048:16)
    at OneOffInvoker.eval [as _invoke] (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:48877:9)
    at OneOffInvoker.invoke (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:48991:16)
    at ComponentScheduler.startPhase (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49222:29)
    at Director.tick (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:19507:35)
    at Game._updateCallback (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:20564:22)
    at updateCallback (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:78359:20)
    at sentryWrapped (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\src\helpers.ts:100:17)
2025-7-26 15:47:31 - log: [PreviewInEditor] ybgg start load main scene
2025-7-26 15:47:31 - log: [PreviewInEditor] load main scene success
2025-7-26 15:47:31 - log: [PreviewInEditor] LubanMgr initialized with tables: 10
2025-7-26 15:48:48 - warn: [PreviewInEditor] IPC message has been lost.Error: [PreviewInEditor] IPC message has been lost.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at IpcRenderer.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\ipc\web\webview.ccc:1:1495)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
2025-7-26 15:48:48 - info: [PreviewInEditor] preview process is ready
2025-7-26 15:51:59 - info: [PreviewInEditor] preview process is ready
2025-7-26 15:51:59 - log: [Scene] meshopt wasm decoder initialized
2025-7-26 15:51:59 - log: [Scene] [box2d]:box2d wasm lib loaded.
2025-7-26 15:51:59 - log: [Scene] [bullet]:bullet wasm lib loaded.
2025-7-26 15:51:59 - log: [Scene] [PHYSICS]: using builtin.
2025-7-26 15:51:59 - log: [Scene] Cocos Creator v3.8.6
2025-7-26 15:51:59 - log: [Scene] Using custom pipeline: Builtin
2025-7-26 15:51:59 - log: [Scene] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-7-26 15:52:03 - log: [Level Editor] Extension unloaded
2025-7-26 15:52:03 - log: [Level Editor] Extension loaded
2025-7-26 15:52:07 - log: [Window] [Level Editor Panel] Panel ready
2025-7-26 15:52:07 - error: [Window] [Level Editor Panel] Could not find panel frame elementError: [Window] [Level Editor Panel] Could not find panel frame element
    at Object.<anonymous> (E:\M2Game\Client\extensions\level-editor-extension\panel\index.js:52:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\panel\lib\element.ccc:1:4682
    at new Promise (<anonymous>)
    at PanelFrame.emit (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\panel\lib\element.ccc:1:4637)
    at loadPanel (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\panel\lib\element.ccc:1:7242)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\panel\lib\element.ccc:1:4434
    at sentryWrapped (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\browser\cjs\helpers.js:93:17)
2025-7-26 15:52:07 - log: [Window] [Level Editor Panel] Available methods: [
  'hidden',
  'clientHeight',
  'clientWidth',
  'level-data-changed',
  'level-saved',
  'selection-changed'
]
2025-7-26 15:53:14 - log: [Window] [Level Editor Panel] Panel closed
2025-7-26 15:53:19 - info: [PreviewInEditor] preview process is ready
2025-7-26 15:53:19 - log: [Scene] meshopt wasm decoder initialized
2025-7-26 15:53:19 - log: [Scene] [box2d]:box2d wasm lib loaded.
2025-7-26 15:53:19 - log: [Scene] [bullet]:bullet wasm lib loaded.
2025-7-26 15:53:19 - log: [Scene] [PHYSICS]: using builtin.
2025-7-26 15:53:19 - log: [Scene] Cocos Creator v3.8.6
2025-7-26 15:53:19 - log: [Scene] Using custom pipeline: Builtin
2025-7-26 15:53:19 - log: [Scene] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-7-26 15:53:21 - log: [Level Editor] Extension unloaded
2025-7-26 15:53:21 - log: [Level Editor] Extension loaded
2025-7-26 15:53:24 - log: [Window] [Level Editor Panel] Panel ready
2025-7-26 15:53:24 - log: [Window] [Level Editor Panel] Panel context: {
  hidden: [Getter],
  clientHeight: [Getter],
  clientWidth: [Getter],
  'level-data-changed': [Function: level-data-changed],
  'level-saved': [Function: level-saved],
  'selection-changed': [Function: selection-changed]
}
2025-7-26 15:53:24 - log: [Window] [Level Editor Panel] Panel properties: [
  'hidden',
  'clientHeight',
  'clientWidth',
  'level-data-changed',
  'level-saved',
  'selection-changed'
]
2025-7-26 15:53:24 - log: [Window] [Level Editor Panel] Trying document.querySelector...
2025-7-26 15:53:24 - error: [Window] [Level Editor Panel] Could not find panel frame element, creating programmaticallyError: [Window] [Level Editor Panel] Could not find panel frame element, creating programmatically
    at E:\M2Game\Client\extensions\level-editor-extension\panel\index.js:71:15
    at sentryWrapped (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\browser\cjs\helpers.js:93:17)
2025-7-26 15:53:24 - log: [Window] [Level Editor Panel] Created iframe programmatically
2025-7-26 15:53:24 - log: [Window] [Level Editor Panel] Programmatically created iframe loaded
2025-7-26 15:53:24 - warn: [Window] [Level Editor Panel] LevelEditorPanel not found in iframeError: [Window] [Level Editor Panel] LevelEditorPanel not found in iframe
    at onFrameLoaded (E:\M2Game\Client\extensions\level-editor-extension\panel\index.js:115:15)
    at HTMLIFrameElement.<anonymous> (E:\M2Game\Client\extensions\level-editor-extension\panel\index.js:85:11)
    at HTMLIFrameElement.sentryWrapped (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\browser\cjs\helpers.js:93:17)
