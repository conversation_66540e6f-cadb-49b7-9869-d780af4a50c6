<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Level Editor</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="level-editor-container">
        <!-- Toolbar -->
        <div id="toolbar" class="toolbar">
            <div class="toolbar-group">
                <ui-button id="btn-new-level" class="toolbar-btn" title="New Level">
                    <i class="icon-new"></i>
                </ui-button>
                <ui-button id="btn-load-level" class="toolbar-btn" title="Load Level">
                    <i class="icon-load"></i>
                </ui-button>
                <ui-button id="btn-save-level" class="toolbar-btn" title="Save Level">
                    <i class="icon-save"></i>
                </ui-button>
            </div>
            
            <div class="toolbar-separator"></div>
            
            <div class="toolbar-group">
                <ui-button id="btn-select-mode" class="toolbar-btn mode-btn active" title="Select Mode">
                    <i class="icon-select"></i>
                </ui-button>
                <ui-button id="btn-add-sublevel" class="toolbar-btn mode-btn" title="Add SubLevel">
                    <i class="icon-sublevel"></i>
                </ui-button>
                <ui-button id="btn-add-spawner" class="toolbar-btn mode-btn" title="Add Spawner">
                    <i class="icon-spawner"></i>
                </ui-button>
                <ui-button id="btn-add-path" class="toolbar-btn mode-btn" title="Add Path">
                    <i class="icon-path"></i>
                </ui-button>
            </div>
            
            <div class="toolbar-separator"></div>
            
            <div class="toolbar-group">
                <ui-button id="btn-preview" class="toolbar-btn" title="Preview Level">
                    <i class="icon-play"></i>
                </ui-button>
                <ui-button id="btn-stop-preview" class="toolbar-btn" title="Stop Preview" style="display: none;">
                    <i class="icon-stop"></i>
                </ui-button>
            </div>
            
            <div class="toolbar-spacer"></div>
            
            <div class="toolbar-group">
                <span id="level-status" class="level-status">No level loaded</span>
            </div>
        </div>
        
        <!-- Main content area -->
        <div class="main-content">
            <!-- Left panel: Level Hierarchy -->
            <div class="left-panel">
                <div class="panel-header">
                    <span class="panel-title">Level Hierarchy</span>
                    <div class="panel-actions">
                        <ui-button id="btn-expand-all" class="icon-btn" title="Expand All">
                            <i class="icon-expand"></i>
                        </ui-button>
                        <ui-button id="btn-collapse-all" class="icon-btn" title="Collapse All">
                            <i class="icon-collapse"></i>
                        </ui-button>
                    </div>
                </div>
                <div id="level-hierarchy" class="level-hierarchy">
                    <div class="no-level-message">
                        <p>No level loaded.</p>
                        <p>Create a new level or load an existing one to get started.</p>
                    </div>
                </div>
            </div>
            
            <!-- Center panel: SubLevel Editor -->
            <div class="center-panel">
                <div class="panel-header">
                    <span class="panel-title">SubLevel Editor</span>
                    <div class="panel-actions">
                        <ui-button id="btn-zoom-fit" class="icon-btn" title="Zoom to Fit">
                            <i class="icon-zoom-fit"></i>
                        </ui-button>
                        <ui-button id="btn-zoom-reset" class="icon-btn" title="Reset Zoom">
                            <i class="icon-zoom-reset"></i>
                        </ui-button>
                        <ui-button id="btn-grid-toggle" class="icon-btn" title="Toggle Grid">
                            <i class="icon-grid"></i>
                        </ui-button>
                    </div>
                </div>
                <div id="sublevel-editor" class="sublevel-editor">
                    <div class="no-sublevel-message">
                        <p>No SubLevel selected.</p>
                        <p>Select a SubLevel from the hierarchy to edit it.</p>
                    </div>
                </div>
            </div>
            
            <!-- Right panel: Property Inspector -->
            <div class="right-panel">
                <div class="panel-header">
                    <span class="panel-title">Properties</span>
                    <div class="panel-actions">
                        <ui-button id="btn-reset-properties" class="icon-btn" title="Reset Properties">
                            <i class="icon-reset"></i>
                        </ui-button>
                    </div>
                </div>
                <div id="property-inspector" class="property-inspector">
                    <div class="no-selection-message">
                        <p>No item selected.</p>
                        <p>Select an item to view and edit its properties.</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Status bar -->
        <div class="status-bar">
            <div class="status-left">
                <span id="selection-info">Ready</span>
            </div>
            <div class="status-right">
                <span id="zoom-level">100%</span>
                <span class="status-separator">|</span>
                <span id="grid-size">Grid: 50px</span>
                <span class="status-separator">|</span>
                <span id="snap-mode">Snap: Off</span>
            </div>
        </div>
    </div>
    
    <!-- Context menu template -->
    <div id="context-menu" class="context-menu" style="display: none;">
        <div class="context-menu-item" data-action="add-sublevel">Add SubLevel</div>
        <div class="context-menu-item" data-action="add-spawner">Add Spawner</div>
        <div class="context-menu-item" data-action="add-path">Add Path</div>
        <div class="context-menu-item" data-action="add-map-layer">Add Map Layer</div>
        <div class="context-menu-separator"></div>
        <div class="context-menu-item" data-action="duplicate">Duplicate</div>
        <div class="context-menu-item" data-action="delete">Delete</div>
        <div class="context-menu-separator"></div>
        <div class="context-menu-item" data-action="rename">Rename</div>
    </div>
    
    <!-- Modal dialogs container -->
    <div id="modal-container" class="modal-container" style="display: none;"></div>
    
    <!-- Scripts -->
    <script src="components/EditorEventBus.js"></script>
    <script src="components/EditorState.js"></script>
    <script src="components/LevelHierarchy.js"></script>
    <script src="components/PropertyInspector.js"></script>
    <script src="components/SubLevelEditor.js"></script>
    <script src="components/ToolbarActions.js"></script>

    <!-- Component-specific editors -->
    <!-- <script src="components/SpawnerEditor.js"></script>
    <script src="components/PathEditor.js"></script>
    <script src="components/MapLayerEditor.js"></script> -->

    <!-- Integration components -->
    <!-- <script src="../integration/DataBridge.js"></script>
    <script src="../integration/IntegrationManager.js"></script> -->

    <!-- Main panel -->
    <script src="components/LevelEditorPanel.js"></script>
    <script src="index.js"></script>

    <!-- Debug script -->
    <script>
      console.log('[Level Editor HTML] Scripts loaded');
      console.log('[Level Editor HTML] LevelEditorPanel available:', typeof LevelEditorPanel);
      console.log('[Level Editor HTML] EditorEventBus available:', typeof EditorEventBus);
      console.log('[Level Editor HTML] EditorState available:', typeof EditorState);

      // Make sure LevelEditorPanel is available globally
      if (typeof LevelEditorPanel !== 'undefined') {
        window.LevelEditorPanel = LevelEditorPanel;
        console.log('[Level Editor HTML] LevelEditorPanel set on window');
      }
    </script>
</body>
</html>
