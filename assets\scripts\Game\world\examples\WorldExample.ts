import { _decorator, Component, Node, Vec3, KeyCode, input, Input } from "cc";
import { 
    World, 
    WorldInitializeData, 
    GameMode, 
    DifficultyLevel,
    BulletSystem,
    LevelSystem,
    PlayerSystem,
    PlayerInput,
    LevelConfig as LevelSystemConfig,
    LevelEventType
} from "../index";
const { ccclass, property } = _decorator;

/**
 * WorldExample - demonstrates how to use the World system
 * This is a complete example showing world creation, system registration,
 * and basic gameplay loop implementation
 */
@ccclass("WorldExample")
export class WorldExample extends Component {
    
    @property({ displayName: "Enable Example", tooltip: "Enable this example (disable for production)" })
    public enableExample: boolean = false;
    
    private _world: World | null = null;
    private _isInitialized: boolean = false;
    
    // System references for easy access
    private _bulletSystem: BulletSystem | null = null;
    private _levelSystem: LevelSystem | null = null;
    private _playerSystem: PlayerSystem | null = null;
    
    // Input tracking
    private _currentInput: PlayerInput = {
        moveDirection: new Vec3(),
        isAttacking: false,
        isJumping: false,
        mousePosition: new Vec3(),
        keyStates: {}
    };
    
    /**
     * Component lifecycle - called when component is loaded
     */
    onLoad(): void {
        if (!this.enableExample) {
            console.log("WorldExample: Example disabled, skipping initialization");
            return;
        }
        
        console.log("WorldExample: Initializing world example");
        this._setupWorld();
    }
    
    /**
     * Component lifecycle - called when component starts
     */
    start(): void {
        if (!this.enableExample || !this._world) {
            return;
        }
        
        this._startExample();
    }
    
    /**
     * Component lifecycle - called every frame
     */
    update(deltaTime: number): void {
        if (!this.enableExample || !this._world || !this._isInitialized) {
            return;
        }
        
        // Update input
        this._updateInput();
        
        // Update world
        this._world.update(deltaTime);
    }
    
    /**
     * Component lifecycle - called after all updates
     */
    lateUpdate(): void {
        if (!this.enableExample || !this._world || !this._isInitialized) {
            return;
        }
        
        // Late update world
        this._world.lateUpdate(0);
    }
    
    /**
     * Component lifecycle - called when component is destroyed
     */
    onDestroy(): void {
        if (this._world) {
            this._world.destroy();
            this._world = null;
        }
        
        this._isInitialized = false;
    }
    
    /**
     * Set up the world and register systems
     */
    private _setupWorld(): void {
        // Create world instance
        this._world = new World();
        
        // Set up event callbacks
        this._world.setOnStateChanged((oldState, newState) => {
            console.log(`WorldExample: World state changed from ${oldState} to ${newState}`);
        });
        
        this._world.setOnError((error) => {
            console.error("WorldExample: World error:", error);
        });
        
        // Register systems
        this._bulletSystem = new BulletSystem();
        this._levelSystem = new LevelSystem();
        this._playerSystem = new PlayerSystem();
        
        this._world.registerSystem(this._bulletSystem);
        this._world.registerSystem(this._levelSystem);
        this._world.registerSystem(this._playerSystem);
        
        console.log("WorldExample: World setup complete");
    }
    
    /**
     * Start the example
     */
    private async _startExample(): Promise<void> {
        if (!this._world) {
            console.error("WorldExample: Cannot start - no world instance");
            return;
        }
        
        // Create initialization data
        const initData = new WorldInitializeData();
        initData.modeId = GameMode.TUTORIAL;
        initData.levelId = "example_level";
        initData.difficulty = DifficultyLevel.EASY;
        initData.randomSeed = 12345; // Fixed seed for consistent example
        
        // Enable debug features for the example
        initData.debugFlags.enableDebugDraw = true;
        initData.debugFlags.showPerformanceStats = true;
        initData.debugFlags.logSystemUpdates = true;
        
        // Initialize world
        const success = await this._world.initialize(initData);
        if (!success) {
            console.error("WorldExample: Failed to initialize world");
            return;
        }
        
        this._isInitialized = true;
        
        // Set up example gameplay
        this._setupExampleGameplay();
        
        console.log("WorldExample: Example started successfully");
    }
    
    /**
     * Set up example gameplay elements
     */
    private _setupExampleGameplay(): void {
        if (!this._playerSystem || !this._levelSystem) {
            return;
        }
        
        // Create a player
        this._playerSystem.createPlayer("example_player", new Vec3(0, 0, 0), {
            maxHealth: 100,
            attackPower: 15,
            moveSpeed: 200,
            defense: 5
        });
        
        // Create example level configuration
        const levelConfig: LevelSystemConfig = {
            levelId: "example_level",
            name: "Example Level",
            description: "A simple example level to demonstrate the World system",
            timeLimit: 300, // 5 minutes
            objectives: [
                {
                    id: "survive",
                    description: "Survive for 60 seconds",
                    type: "time",
                    targetValue: 60,
                    currentValue: 0,
                    isCompleted: false
                },
                {
                    id: "collect_items",
                    description: "Collect 10 items",
                    type: "collection",
                    targetValue: 10,
                    currentValue: 0,
                    isCompleted: false
                }
            ],
            checkpoints: [
                {
                    id: "start",
                    position: new Vec3(0, 0, 0),
                    isReached: true
                },
                {
                    id: "middle",
                    position: new Vec3(100, 0, 0),
                    isReached: false
                }
            ],
            spawnPoints: [
                new Vec3(0, 0, 0),
                new Vec3(50, 50, 0),
                new Vec3(-50, -50, 0)
            ],
            boundaries: {
                minX: -200,
                maxX: 200,
                minY: -200,
                maxY: 200
            }
        };
        
        // Load the level
        this._levelSystem.loadLevel(levelConfig);
        
        // Set up level event listeners
        this._levelSystem.addEventListener(LevelEventType.LEVEL_COMPLETE, (event) => {
            console.log("WorldExample: Level completed!", event.data);
        });
        
        this._levelSystem.addEventListener(LevelEventType.CHECKPOINT_REACHED, (event) => {
            console.log("WorldExample: Checkpoint reached:", event.data.checkpointId);
        });
        
        // Set up input handling
        input.on(Input.EventType.KEY_DOWN, this._onKeyDown, this);
        input.on(Input.EventType.KEY_UP, this._onKeyUp, this);
        
        console.log("WorldExample: Example gameplay setup complete");
    }
    
    /**
     * Update input state
     */
    private _updateInput(): void {
        if (!this._playerSystem) {
            return;
        }
        
        // Reset movement direction
        this._currentInput.moveDirection.set(0, 0, 0);
        
        // Check movement keys
        if (this._currentInput.keyStates[KeyCode.KEY_W] || this._currentInput.keyStates[KeyCode.ARROW_UP]) {
            this._currentInput.moveDirection.y += 1;
        }
        if (this._currentInput.keyStates[KeyCode.KEY_S] || this._currentInput.keyStates[KeyCode.ARROW_DOWN]) {
            this._currentInput.moveDirection.y -= 1;
        }
        if (this._currentInput.keyStates[KeyCode.KEY_A] || this._currentInput.keyStates[KeyCode.ARROW_LEFT]) {
            this._currentInput.moveDirection.x -= 1;
        }
        if (this._currentInput.keyStates[KeyCode.KEY_D] || this._currentInput.keyStates[KeyCode.ARROW_RIGHT]) {
            this._currentInput.moveDirection.x += 1;
        }
        
        // Normalize movement direction
        if (this._currentInput.moveDirection.length() > 0) {
            this._currentInput.moveDirection.normalize();
        }
        
        // Send input to player system
        this._playerSystem.addInput(this._currentInput);
    }
    
    /**
     * Handle key down events
     */
    private _onKeyDown(event: any): void {
        this._currentInput.keyStates[event.keyCode] = true;
        
        // Handle special keys
        switch (event.keyCode) {
            case KeyCode.SPACE:
                this._currentInput.isAttacking = true;
                this._handleAttack();
                break;
            case KeyCode.KEY_R:
                this._restartExample();
                break;
            case KeyCode.KEY_P:
                this._togglePause();
                break;
        }
    }
    
    /**
     * Handle key up events
     */
    private _onKeyUp(event: any): void {
        this._currentInput.keyStates[event.keyCode] = false;
        
        if (event.keyCode === KeyCode.SPACE) {
            this._currentInput.isAttacking = false;
        }
    }
    
    /**
     * Handle attack action
     */
    private _handleAttack(): void {
        if (!this._bulletSystem || !this._playerSystem) {
            return;
        }
        
        const player = this._playerSystem.getCurrentPlayer();
        if (!player) {
            return;
        }
        
        // Create a bullet
        const bulletConfig = {
            speed: 500,
            damage: 10,
            lifetime: 3.0,
            bulletType: "player_bullet",
            size: 5
        };
        
        const direction = new Vec3(1, 0, 0); // Shoot to the right
        this._bulletSystem.createBullet(player.position, direction, bulletConfig, player.id);
        
        console.log("WorldExample: Player fired a bullet");
    }
    
    /**
     * Restart the example
     */
    private _restartExample(): void {
        console.log("WorldExample: Restarting example...");
        
        if (this._world) {
            this._world.stop();
            this._isInitialized = false;
            this._startExample();
        }
    }
    
    /**
     * Toggle pause state
     */
    private _togglePause(): void {
        if (!this._world) {
            return;
        }
        
        const currentState = this._world.getState();
        if (currentState === "running") {
            this._world.pause();
            console.log("WorldExample: Game paused");
        } else if (currentState === "paused") {
            this._world.start();
            console.log("WorldExample: Game resumed");
        }
    }
}
