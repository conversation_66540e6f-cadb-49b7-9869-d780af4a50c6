# Test Level JSON Files

This directory contains JSON test files that match your current level runtime design. These files are designed to test different aspects of your level system without needing the level editor.

## Test Files Overview

### 1. `minimal-level.json`
**Purpose**: Basic functionality testing
- Single sublevel with minimal configuration
- One simple spawner with sequential pattern
- One straight path
- Basic background layer
- No events or complex features
- **Use for**: Initial runtime system validation, basic loading/parsing tests

### 2. `basic-level.json`
**Purpose**: Standard level features testing
- Single sublevel with entry/exit points
- Multiple background layers with different scroll speeds
- Basic spawner with wave configuration
- Simple straight-down path
- Sublevel connections
- **Use for**: Testing standard level flow, background rendering, basic spawning

### 3. `complex-level.json`
**Purpose**: Advanced features and multi-sublevel testing
- Three connected sublevels (start → middle → boss)
- Multiple spawners with different patterns (sequential, random)
- Curved bezier paths with multiple control points
- Various background layers and effects
- Sublevel transitions (seamless and fade)
- Entry/exit points with animations
- **Use for**: Testing sublevel connections, complex paths, transition systems

### 4. `boss-level.json`
**Purpose**: Boss battle and complex spawning patterns
- Single arena-style sublevel
- Boss spawner with static pattern
- Multiple minion spawners with different timing
- Complex looping boss movement path
- Event triggers for boss phases
- Camera following boss target
- **Use for**: Testing boss mechanics, complex spawning coordination, event triggers

### 5. `event-test-level.json`
**Purpose**: Event system testing
- Focus on event triggers and timing
- Time-based triggers (10s, 30s intervals)
- Condition-based triggers (player damage)
- Event-activated spawners
- Global and sublevel events
- **Use for**: Testing event system, trigger conditions, event-driven gameplay

## Key Features Tested

### Data Structure Validation
- All files follow the `ILevelData` interface structure
- Proper JSON serialization format with version/timestamp
- Correct nesting of sublevels, spawners, paths, events
- Valid enum values for all type fields

### Runtime Components
- **LevelManager**: Level loading and sublevel management
- **SpawnerSystem**: Different spawn patterns and wave configurations
- **PathManager**: Bezier curve paths with control points
- **MapSystem**: Multiple background layers with parallax scrolling
- **CameraManager**: Different camera modes and following targets
- **EventSystem**: Time and condition-based triggers

### Spawner Patterns
- `sequential`: Spawns entities in order
- `random`: Randomly selects from entity list based on weights
- `static`: Single spawn (for bosses)

### Path Types
- Straight paths (minimal, basic)
- Curved bezier paths with control points (complex, boss)
- Looping paths (boss movement)

### Background Layers
- Multiple layer types: `BG_VeryFar`, `BG_Far`, `BG_Mid`, `BG_Close`, `FG_VeryClose`
- Different scroll speeds for parallax effect
- Repeat modes: `vertical`, `horizontal`, `both`, `none`

### Sublevel Features
- Entry/exit points with different types (`smooth`, `fade`, `instant`)
- Connections between sublevels (`seamless`, `transition`, `teleport`)
- Bounds and trigger areas
- Load priorities

## Usage Instructions

1. **Start with minimal-level.json** to verify basic loading and parsing
2. **Test basic-level.json** for standard gameplay features
3. **Use complex-level.json** to test sublevel transitions and advanced paths
4. **Test boss-level.json** for complex spawning and boss mechanics
5. **Use event-test-level.json** to validate event system functionality

## Testing Your Runtime

```typescript
// Example usage in your runtime system
import { JsonLevelSerializer } from './core/LevelSerializer';
import { LevelManager } from './runtime/LevelManager';

const serializer = new JsonLevelSerializer();
const levelManager = new LevelManager();

// Load and test a level
const levelJson = await loadFile('test-levels/minimal-level.json');
const levelData = serializer.deserialize(levelJson);
await levelManager.loadLevel(levelData);
```

## Validation Checklist

- [ ] Level metadata loads correctly
- [ ] Sublevels are created with proper bounds
- [ ] Background layers render with correct parallax
- [ ] Spawners activate at correct times
- [ ] Entities follow paths correctly
- [ ] Sublevel transitions work
- [ ] Camera follows targets properly
- [ ] Events trigger at correct conditions
- [ ] Wave timing works as expected
- [ ] Entry/exit points function correctly

These test files should help you validate that your runtime design is solid before returning to the level editor implementation.
