/**
 * Scene Manipulation System
 * Handles user interactions with gizmos in the scene view
 */

const { Vec3, Vec2, Camera, Node } = require('cc');

class SceneManipulation {
  constructor(sceneGizmos) {
    this.sceneGizmos = sceneGizmos;
    this.isEnabled = true;
    this.isDragging = false;
    this.dragTarget = null;
    this.dragType = null;
    this.dragStartPos = new Vec3();
    this.dragStartData = null;
    
    // Manipulation settings
    this.settings = {
      snapToGrid: false,
      gridSize: 50,
      snapThreshold: 10,
      enableRotation: true,
      enableScaling: false
    };
    
    this.initialize();
  }

  /**
   * Initialize the manipulation system
   */
  initialize() {
    this.setupEventHandlers();
  }

  /**
   * Setup event handlers
   */
  setupEventHandlers() {
    // Mouse events
    cc.systemEvent.on(cc.SystemEvent.EventType.MOUSE_DOWN, this.onMouseDown, this);
    cc.systemEvent.on(cc.SystemEvent.EventType.MOUSE_MOVE, this.onMouseMove, this);
    cc.systemEvent.on(cc.SystemEvent.EventType.MOUSE_UP, this.onMouseUp, this);
    
    // Touch events for mobile
    cc.systemEvent.on(cc.SystemEvent.EventType.TOUCH_START, this.onTouchStart, this);
    cc.systemEvent.on(cc.SystemEvent.EventType.TOUCH_MOVE, this.onTouchMove, this);
    cc.systemEvent.on(cc.SystemEvent.EventType.TOUCH_END, this.onTouchEnd, this);
    
    // Keyboard events
    cc.systemEvent.on(cc.SystemEvent.EventType.KEY_DOWN, this.onKeyDown, this);
    cc.systemEvent.on(cc.SystemEvent.EventType.KEY_UP, this.onKeyUp, this);
  }

  /**
   * Enable/disable manipulation
   * @param {boolean} enabled - Whether manipulation is enabled
   */
  setEnabled(enabled) {
    this.isEnabled = enabled;
  }

  /**
   * Update manipulation settings
   * @param {Object} settings - New settings
   */
  updateSettings(settings) {
    Object.assign(this.settings, settings);
  }

  /**
   * Handle mouse down event
   * @param {Event} event - Mouse event
   */
  onMouseDown(event) {
    if (!this.isEnabled) return;
    
    const worldPos = this.screenToWorld(event.getLocationX(), event.getLocationY());
    const hitResult = this.performRaycast(worldPos);
    
    if (hitResult) {
      this.startManipulation(hitResult, worldPos);
    }
  }

  /**
   * Handle mouse move event
   * @param {Event} event - Mouse event
   */
  onMouseMove(event) {
    if (!this.isEnabled) return;
    
    if (this.isDragging) {
      const worldPos = this.screenToWorld(event.getLocationX(), event.getLocationY());
      this.updateManipulation(worldPos);
    } else {
      // Update hover state
      const worldPos = this.screenToWorld(event.getLocationX(), event.getLocationY());
      this.updateHover(worldPos);
    }
  }

  /**
   * Handle mouse up event
   * @param {Event} event - Mouse event
   */
  onMouseUp(event) {
    if (!this.isEnabled) return;
    
    if (this.isDragging) {
      this.endManipulation();
    }
  }

  /**
   * Handle touch start event
   * @param {Event} event - Touch event
   */
  onTouchStart(event) {
    const touch = event.getTouch();
    const worldPos = this.screenToWorld(touch.getLocationX(), touch.getLocationY());
    const hitResult = this.performRaycast(worldPos);
    
    if (hitResult) {
      this.startManipulation(hitResult, worldPos);
    }
  }

  /**
   * Handle touch move event
   * @param {Event} event - Touch event
   */
  onTouchMove(event) {
    if (!this.isEnabled || !this.isDragging) return;
    
    const touch = event.getTouch();
    const worldPos = this.screenToWorld(touch.getLocationX(), touch.getLocationY());
    this.updateManipulation(worldPos);
  }

  /**
   * Handle touch end event
   * @param {Event} event - Touch event
   */
  onTouchEnd(event) {
    if (!this.isEnabled) return;
    
    if (this.isDragging) {
      this.endManipulation();
    }
  }

  /**
   * Handle key down event
   * @param {Event} event - Key event
   */
  onKeyDown(event) {
    if (!this.isEnabled) return;
    
    switch (event.keyCode) {
      case cc.macro.KEY.g: // Grid snap toggle
        if (event.ctrlKey || event.metaKey) {
          this.settings.snapToGrid = !this.settings.snapToGrid;
          this.notifySettingsChanged();
        }
        break;
      case cc.macro.KEY.delete:
        this.deleteSelected();
        break;
      case cc.macro.KEY.escape:
        if (this.isDragging) {
          this.cancelManipulation();
        }
        break;
    }
  }

  /**
   * Handle key up event
   * @param {Event} event - Key event
   */
  onKeyUp(event) {
    // Handle key up events if needed
  }

  /**
   * Convert screen coordinates to world coordinates
   * @param {number} screenX - Screen X coordinate
   * @param {number} screenY - Screen Y coordinate
   * @returns {Vec3} World coordinates
   */
  screenToWorld(screenX, screenY) {
    const camera = cc.Camera.main;
    if (!camera) return new Vec3();
    
    const screenPos = new Vec3(screenX, screenY, 0);
    const worldPos = new Vec3();
    camera.screenToWorld(worldPos, screenPos);
    
    return worldPos;
  }

  /**
   * Perform raycast to find gizmo under cursor
   * @param {Vec3} worldPos - World position
   * @returns {Object|null} Hit result or null
   */
  performRaycast(worldPos) {
    if (!this.sceneGizmos.gizmoLayer) return null;
    
    // Simple 2D hit testing
    const hitResult = this.findGizmoAtPosition(worldPos);
    return hitResult;
  }

  /**
   * Find gizmo at position
   * @param {Vec3} worldPos - World position
   * @returns {Object|null} Gizmo hit result or null
   */
  findGizmoAtPosition(worldPos) {
    // Iterate through gizmo nodes and check bounds
    for (const [id, subLevelNode] of this.sceneGizmos.gizmoNodes) {
      const hitResult = this.checkNodeHit(subLevelNode, worldPos);
      if (hitResult) {
        return hitResult;
      }
    }
    
    return null;
  }

  /**
   * Check if node is hit by position
   * @param {Node} node - Node to check
   * @param {Vec3} worldPos - World position
   * @returns {Object|null} Hit result or null
   */
  checkNodeHit(node, worldPos) {
    // Check if node has user data (is a gizmo)
    if (node.userData) {
      const bounds = this.getNodeBounds(node);
      if (this.pointInBounds(worldPos, bounds)) {
        return {
          node: node,
          type: node.userData.type,
          data: node.userData.data,
          worldPos: worldPos
        };
      }
    }
    
    // Check children
    for (const child of node.children) {
      const hitResult = this.checkNodeHit(child, worldPos);
      if (hitResult) {
        return hitResult;
      }
    }
    
    return null;
  }

  /**
   * Get node bounds
   * @param {Node} node - Node
   * @returns {Object} Bounds {x, y, width, height}
   */
  getNodeBounds(node) {
    const worldPos = node.getWorldPosition();
    const contentSize = node.getContentSize();
    
    return {
      x: worldPos.x - contentSize.width / 2,
      y: worldPos.y - contentSize.height / 2,
      width: contentSize.width,
      height: contentSize.height
    };
  }

  /**
   * Check if point is in bounds
   * @param {Vec3} point - Point to check
   * @param {Object} bounds - Bounds
   * @returns {boolean} True if point is in bounds
   */
  pointInBounds(point, bounds) {
    return point.x >= bounds.x && 
           point.x <= bounds.x + bounds.width &&
           point.y >= bounds.y && 
           point.y <= bounds.y + bounds.height;
  }

  /**
   * Start manipulation
   * @param {Object} hitResult - Hit result
   * @param {Vec3} worldPos - World position
   */
  startManipulation(hitResult, worldPos) {
    this.isDragging = true;
    this.dragTarget = hitResult;
    this.dragStartPos.set(worldPos);
    
    // Store initial data for undo
    this.dragStartData = this.cloneData(hitResult.data);
    
    // Determine manipulation type
    this.dragType = this.getDragType(hitResult);
    
    // Notify start of manipulation
    this.notifyManipulationStart(hitResult);
  }

  /**
   * Update manipulation
   * @param {Vec3} worldPos - Current world position
   */
  updateManipulation(worldPos) {
    if (!this.isDragging || !this.dragTarget) return;
    
    const delta = new Vec3();
    Vec3.subtract(delta, worldPos, this.dragStartPos);
    
    // Apply grid snapping if enabled
    if (this.settings.snapToGrid) {
      delta.x = Math.round(delta.x / this.settings.gridSize) * this.settings.gridSize;
      delta.y = Math.round(delta.y / this.settings.gridSize) * this.settings.gridSize;
    }
    
    // Apply manipulation based on type
    switch (this.dragType) {
      case 'move':
        this.applyMove(delta);
        break;
      case 'resize':
        this.applyResize(delta);
        break;
      case 'rotate':
        this.applyRotate(worldPos);
        break;
    }
    
    // Update gizmos
    this.sceneGizmos.updateAllGizmos();
    
    // Notify manipulation update
    this.notifyManipulationUpdate();
  }

  /**
   * End manipulation
   */
  endManipulation() {
    if (!this.isDragging) return;
    
    // Notify end of manipulation
    this.notifyManipulationEnd();
    
    // Reset state
    this.isDragging = false;
    this.dragTarget = null;
    this.dragType = null;
    this.dragStartData = null;
  }

  /**
   * Cancel manipulation
   */
  cancelManipulation() {
    if (!this.isDragging) return;
    
    // Restore original data
    if (this.dragStartData && this.dragTarget) {
      Object.assign(this.dragTarget.data, this.dragStartData);
    }
    
    // Update gizmos
    this.sceneGizmos.updateAllGizmos();
    
    // End manipulation
    this.endManipulation();
  }

  /**
   * Get drag type based on hit result
   * @param {Object} hitResult - Hit result
   * @returns {string} Drag type
   */
  getDragType(hitResult) {
    switch (hitResult.type) {
      case 'bounds-handle':
        return 'resize';
      case 'spawner':
      case 'path-point':
      case 'entry-point':
      case 'exit-point':
        return 'move';
      default:
        return 'move';
    }
  }

  /**
   * Apply move transformation
   * @param {Vec3} delta - Movement delta
   */
  applyMove(delta) {
    const target = this.dragTarget;
    
    switch (target.type) {
      case 'spawner':
        target.data.position.x = this.dragStartData.position.x + delta.x;
        target.data.position.y = this.dragStartData.position.y + delta.y;
        target.data.position.z = this.dragStartData.position.z + delta.z;
        break;
      case 'path-point':
        target.data.position.x = this.dragStartData.position.x + delta.x;
        target.data.position.y = this.dragStartData.position.y + delta.y;
        target.data.position.z = this.dragStartData.position.z + delta.z;
        break;
      case 'entry-point':
      case 'exit-point':
        target.data.position.x = this.dragStartData.position.x + delta.x;
        target.data.position.y = this.dragStartData.position.y + delta.y;
        target.data.position.z = this.dragStartData.position.z + delta.z;
        break;
    }
  }

  /**
   * Apply resize transformation
   * @param {Vec3} delta - Resize delta
   */
  applyResize(delta) {
    const target = this.dragTarget;
    
    if (target.type === 'bounds-handle') {
      const bounds = target.data.bounds;
      const corner = target.node.userData.corner;
      
      switch (corner) {
        case 'top-left':
          bounds.width = this.dragStartData.bounds.width - delta.x;
          bounds.height = this.dragStartData.bounds.height - delta.y;
          bounds.x = this.dragStartData.bounds.x + delta.x;
          bounds.y = this.dragStartData.bounds.y + delta.y;
          break;
        case 'top-right':
          bounds.width = this.dragStartData.bounds.width + delta.x;
          bounds.height = this.dragStartData.bounds.height - delta.y;
          bounds.y = this.dragStartData.bounds.y + delta.y;
          break;
        case 'bottom-left':
          bounds.width = this.dragStartData.bounds.width - delta.x;
          bounds.height = this.dragStartData.bounds.height + delta.y;
          bounds.x = this.dragStartData.bounds.x + delta.x;
          break;
        case 'bottom-right':
          bounds.width = this.dragStartData.bounds.width + delta.x;
          bounds.height = this.dragStartData.bounds.height + delta.y;
          break;
      }
      
      // Ensure minimum size
      bounds.width = Math.max(50, bounds.width);
      bounds.height = Math.max(50, bounds.height);
    }
  }

  /**
   * Apply rotate transformation
   * @param {Vec3} worldPos - Current world position
   */
  applyRotate(worldPos) {
    // Implementation for rotation
    // This would calculate rotation based on mouse position relative to object center
  }

  /**
   * Update hover state
   * @param {Vec3} worldPos - World position
   */
  updateHover(worldPos) {
    const hitResult = this.performRaycast(worldPos);
    
    // Update cursor based on hover target
    if (hitResult) {
      this.updateCursor(hitResult.type);
    } else {
      this.updateCursor('default');
    }
  }

  /**
   * Update cursor
   * @param {string} type - Cursor type
   */
  updateCursor(type) {
    // In a full implementation, this would update the cursor style
    // For now, we'll just log the cursor type
    // console.log('Cursor:', type);
  }

  /**
   * Delete selected items
   */
  deleteSelected() {
    // This would delete currently selected items
    // Implementation depends on selection system
  }

  /**
   * Clone data for undo
   * @param {Object} data - Data to clone
   * @returns {Object} Cloned data
   */
  cloneData(data) {
    return JSON.parse(JSON.stringify(data));
  }

  /**
   * Notify manipulation start
   * @param {Object} hitResult - Hit result
   */
  notifyManipulationStart(hitResult) {
    // Emit event for manipulation start
    cc.systemEvent.emit('level-editor-manipulation-start', {
      type: hitResult.type,
      data: hitResult.data
    });
  }

  /**
   * Notify manipulation update
   */
  notifyManipulationUpdate() {
    // Emit event for manipulation update
    cc.systemEvent.emit('level-editor-manipulation-update', {
      type: this.dragTarget.type,
      data: this.dragTarget.data
    });
  }

  /**
   * Notify manipulation end
   */
  notifyManipulationEnd() {
    // Emit event for manipulation end
    cc.systemEvent.emit('level-editor-manipulation-end', {
      type: this.dragTarget.type,
      data: this.dragTarget.data,
      originalData: this.dragStartData
    });
  }

  /**
   * Notify settings changed
   */
  notifySettingsChanged() {
    // Emit event for settings change
    cc.systemEvent.emit('level-editor-settings-changed', this.settings);
  }

  /**
   * Destroy the manipulation system
   */
  destroy() {
    // Remove event listeners
    cc.systemEvent.off(cc.SystemEvent.EventType.MOUSE_DOWN, this.onMouseDown, this);
    cc.systemEvent.off(cc.SystemEvent.EventType.MOUSE_MOVE, this.onMouseMove, this);
    cc.systemEvent.off(cc.SystemEvent.EventType.MOUSE_UP, this.onMouseUp, this);
    cc.systemEvent.off(cc.SystemEvent.EventType.TOUCH_START, this.onTouchStart, this);
    cc.systemEvent.off(cc.SystemEvent.EventType.TOUCH_MOVE, this.onTouchMove, this);
    cc.systemEvent.off(cc.SystemEvent.EventType.TOUCH_END, this.onTouchEnd, this);
    cc.systemEvent.off(cc.SystemEvent.EventType.KEY_DOWN, this.onKeyDown, this);
    cc.systemEvent.off(cc.SystemEvent.EventType.KEY_UP, this.onKeyUp, this);
    
    // Reset state
    this.isDragging = false;
    this.dragTarget = null;
    this.dragType = null;
    this.dragStartData = null;
  }
}

module.exports = SceneManipulation;
