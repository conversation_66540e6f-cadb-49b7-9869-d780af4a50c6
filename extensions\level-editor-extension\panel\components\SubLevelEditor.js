/**
 * SubLevel Editor Component
 * Visual editing for sublevel bounds, connections, and layout
 */
class SubLevelEditor {
  constructor(container, eventBus, editorState) {
    this.container = container;
    this.eventBus = eventBus;
    this.editorState = editorState;
    
    this.canvas = null;
    this.ctx = null;
    this.currentSubLevel = null;
    this.levelData = null;
    
    // View state
    this.viewState = {
      zoom: 1.0,
      panX: 0,
      panY: 0,
      gridSize: 50,
      showGrid: true,
      showConnections: true,
      showGizmos: true
    };
    
    // Interaction state
    this.isDragging = false;
    this.dragStart = { x: 0, y: 0 };
    this.dragTarget = null;
    this.dragType = null; // 'pan', 'bounds', 'connection', 'spawner', 'path'
    
    // Selection state
    this.selectedItems = [];
    this.hoveredItem = null;
    
    // Gizmos and handles
    this.gizmos = [];
    this.handles = [];
    
    this.initialize();
  }

  /**
   * Initialize the component
   */
  initialize() {
    this.createCanvas();
    this.setupEventHandlers();
    this.render();
  }

  /**
   * Create the canvas element
   */
  createCanvas() {
    // Clear container
    this.container.innerHTML = '';
    
    // Create canvas
    this.canvas = document.createElement('canvas');
    this.canvas.className = 'sublevel-canvas';
    this.ctx = this.canvas.getContext('2d');
    
    // Set initial size
    this.resizeCanvas();
    
    this.container.appendChild(this.canvas);
    
    // Handle resize
    window.addEventListener('resize', this.resizeCanvas.bind(this));
  }

  /**
   * Resize canvas to fit container
   */
  resizeCanvas() {
    const rect = this.container.getBoundingClientRect();
    this.canvas.width = rect.width;
    this.canvas.height = rect.height;
    this.render();
  }

  /**
   * Setup event handlers
   */
  setupEventHandlers() {
    // Listen for level data changes
    this.eventBus.onLevelDataChanged(this.onLevelDataChanged, this);
    
    // Listen for selection changes
    this.eventBus.onSelectionChanged(this.onSelectionChanged, this);
    
    // Listen for view settings changes
    this.eventBus.on('view-settings-changed', this.onViewSettingsChanged, this);
    
    // Mouse events
    this.canvas.addEventListener('mousedown', this.onMouseDown.bind(this));
    this.canvas.addEventListener('mousemove', this.onMouseMove.bind(this));
    this.canvas.addEventListener('mouseup', this.onMouseUp.bind(this));
    this.canvas.addEventListener('wheel', this.onWheel.bind(this));
    this.canvas.addEventListener('contextmenu', this.onContextMenu.bind(this));
    
    // Keyboard events
    document.addEventListener('keydown', this.onKeyDown.bind(this));
    document.addEventListener('keyup', this.onKeyUp.bind(this));
  }

  /**
   * Handle level data changed
   * @param {Object} levelData - New level data
   */
  onLevelDataChanged(levelData) {
    this.levelData = levelData;
    
    // If no sublevel is selected, select the first one
    if (levelData && levelData.subLevels.length > 0 && !this.currentSubLevel) {
      this.setSubLevel(levelData.subLevels[0]);
    }
    
    this.render();
  }

  /**
   * Handle selection changed
   * @param {Array} selection - Selected items
   */
  onSelectionChanged(selection) {
    this.selectedItems = selection;
    
    // If a sublevel is selected, switch to it
    const subLevelSelection = selection.find(item => item.type === 'sublevel');
    if (subLevelSelection && subLevelSelection.data !== this.currentSubLevel) {
      this.setSubLevel(subLevelSelection.data);
    }
    
    this.updateGizmos();
    this.render();
  }

  /**
   * Handle view settings changed
   * @param {Object} viewSettings - New view settings
   */
  onViewSettingsChanged(viewSettings) {
    this.viewState.showGrid = viewSettings.gridEnabled;
    this.viewState.gridSize = viewSettings.gridSize;
    this.viewState.zoom = viewSettings.zoom;
    this.viewState.panX = viewSettings.panX;
    this.viewState.panY = viewSettings.panY;
    this.render();
  }

  /**
   * Set the current sublevel
   * @param {Object} subLevel - SubLevel data
   */
  setSubLevel(subLevel) {
    this.currentSubLevel = subLevel;
    this.updateGizmos();
    this.fitToSubLevel();
    this.render();
  }

  /**
   * Fit view to current sublevel
   */
  fitToSubLevel() {
    if (!this.currentSubLevel) return;
    
    const bounds = this.currentSubLevel.bounds;
    const canvasWidth = this.canvas.width;
    const canvasHeight = this.canvas.height;
    
    // Calculate zoom to fit
    const zoomX = canvasWidth / (bounds.width + 200); // Add padding
    const zoomY = canvasHeight / (bounds.height + 200);
    const zoom = Math.min(zoomX, zoomY, 2.0); // Max zoom of 2.0
    
    // Calculate pan to center
    const panX = (canvasWidth / 2) - (bounds.x + bounds.width / 2) * zoom;
    const panY = (canvasHeight / 2) - (bounds.y + bounds.height / 2) * zoom;
    
    this.viewState.zoom = zoom;
    this.viewState.panX = panX;
    this.viewState.panY = panY;
    
    // Update editor state
    this.editorState.updateViewSettings({
      zoom: zoom,
      panX: panX,
      panY: panY
    });
  }

  /**
   * Update gizmos based on current selection and sublevel
   */
  updateGizmos() {
    this.gizmos = [];
    this.handles = [];
    
    if (!this.currentSubLevel) return;
    
    // Add sublevel bounds gizmo
    this.addSubLevelBoundsGizmo();
    
    // Add spawner gizmos
    this.currentSubLevel.spawners.forEach(spawner => {
      this.addSpawnerGizmo(spawner);
    });
    
    // Add path gizmos
    this.currentSubLevel.paths.forEach(path => {
      this.addPathGizmo(path);
    });
    
    // Add connection gizmos
    this.currentSubLevel.connections.forEach(connection => {
      this.addConnectionGizmo(connection);
    });
    
    // Add entry/exit point gizmos
    if (this.currentSubLevel.entryPoint) {
      this.addEntryPointGizmo(this.currentSubLevel.entryPoint);
    }
    
    if (this.currentSubLevel.exitPoint) {
      this.addExitPointGizmo(this.currentSubLevel.exitPoint);
    }
  }

  /**
   * Add sublevel bounds gizmo
   */
  addSubLevelBoundsGizmo() {
    const bounds = this.currentSubLevel.bounds;
    
    // Main bounds rectangle
    this.gizmos.push({
      type: 'sublevel-bounds',
      bounds: bounds,
      color: '#007acc',
      strokeWidth: 2,
      fillAlpha: 0.1
    });
    
    // Corner handles for resizing
    const handleSize = 8;
    const positions = [
      { x: bounds.x, y: bounds.y, cursor: 'nw-resize', corner: 'top-left' },
      { x: bounds.x + bounds.width, y: bounds.y, cursor: 'ne-resize', corner: 'top-right' },
      { x: bounds.x, y: bounds.y + bounds.height, cursor: 'sw-resize', corner: 'bottom-left' },
      { x: bounds.x + bounds.width, y: bounds.y + bounds.height, cursor: 'se-resize', corner: 'bottom-right' }
    ];
    
    positions.forEach(pos => {
      this.handles.push({
        type: 'bounds-handle',
        x: pos.x,
        y: pos.y,
        width: handleSize,
        height: handleSize,
        cursor: pos.cursor,
        corner: pos.corner,
        target: this.currentSubLevel
      });
    });
    
    // Edge handles for moving edges
    const edgeHandleSize = 6;
    this.handles.push(
      // Top edge
      {
        type: 'bounds-edge',
        x: bounds.x + bounds.width / 2,
        y: bounds.y,
        width: edgeHandleSize,
        height: edgeHandleSize,
        cursor: 'n-resize',
        edge: 'top',
        target: this.currentSubLevel
      },
      // Right edge
      {
        type: 'bounds-edge',
        x: bounds.x + bounds.width,
        y: bounds.y + bounds.height / 2,
        width: edgeHandleSize,
        height: edgeHandleSize,
        cursor: 'e-resize',
        edge: 'right',
        target: this.currentSubLevel
      },
      // Bottom edge
      {
        type: 'bounds-edge',
        x: bounds.x + bounds.width / 2,
        y: bounds.y + bounds.height,
        width: edgeHandleSize,
        height: edgeHandleSize,
        cursor: 's-resize',
        edge: 'bottom',
        target: this.currentSubLevel
      },
      // Left edge
      {
        type: 'bounds-edge',
        x: bounds.x,
        y: bounds.y + bounds.height / 2,
        width: edgeHandleSize,
        height: edgeHandleSize,
        cursor: 'w-resize',
        edge: 'left',
        target: this.currentSubLevel
      }
    );
  }

  /**
   * Add spawner gizmo
   * @param {Object} spawner - Spawner data
   */
  addSpawnerGizmo(spawner) {
    const isSelected = this.selectedItems.some(item => item.data === spawner);
    
    this.gizmos.push({
      type: 'spawner',
      x: spawner.position.x,
      y: spawner.position.y,
      radius: 12,
      color: spawner.isActive ? '#ff6b35' : '#666666',
      strokeWidth: isSelected ? 3 : 2,
      strokeColor: isSelected ? '#ffffff' : '#333333',
      data: spawner
    });
    
    // Add move handle if selected
    if (isSelected) {
      this.handles.push({
        type: 'spawner-move',
        x: spawner.position.x,
        y: spawner.position.y,
        width: 24,
        height: 24,
        cursor: 'move',
        target: spawner
      });
    }
  }

  /**
   * Add path gizmo
   * @param {Object} path - Path data
   */
  addPathGizmo(path) {
    const isSelected = this.selectedItems.some(item => item.data === path);
    
    // Draw path curve
    this.gizmos.push({
      type: 'path',
      points: path.points,
      color: path.editorData?.color || '#00ff88',
      strokeWidth: isSelected ? 3 : 2,
      isLoop: path.isLoop,
      data: path
    });
    
    // Add point handles if selected
    if (isSelected) {
      path.points.forEach((point, index) => {
        // Main point handle
        this.handles.push({
          type: 'path-point',
          x: point.position.x,
          y: point.position.y,
          width: 8,
          height: 8,
          cursor: 'move',
          target: path,
          pointIndex: index
        });
        
        // Control point handles
        this.handles.push({
          type: 'path-control',
          x: point.position.x + point.controlPoint1.x,
          y: point.position.y + point.controlPoint1.y,
          width: 6,
          height: 6,
          cursor: 'move',
          target: path,
          pointIndex: index,
          controlIndex: 1
        });
        
        this.handles.push({
          type: 'path-control',
          x: point.position.x + point.controlPoint2.x,
          y: point.position.y + point.controlPoint2.y,
          width: 6,
          height: 6,
          cursor: 'move',
          target: path,
          pointIndex: index,
          controlIndex: 2
        });
      });
    }
  }

  /**
   * Add connection gizmo
   * @param {Object} connection - Connection data
   */
  addConnectionGizmo(connection) {
    const triggerArea = connection.triggerArea;
    
    this.gizmos.push({
      type: 'connection',
      bounds: triggerArea,
      color: '#ff9500',
      strokeWidth: 2,
      fillAlpha: 0.2,
      data: connection
    });
  }

  /**
   * Add entry point gizmo
   * @param {Object} entryPoint - Entry point data
   */
  addEntryPointGizmo(entryPoint) {
    this.gizmos.push({
      type: 'entry-point',
      x: entryPoint.position.x,
      y: entryPoint.position.y,
      radius: 10,
      color: '#00ff00',
      strokeWidth: 2,
      data: entryPoint
    });
  }

  /**
   * Add exit point gizmo
   * @param {Object} exitPoint - Exit point data
   */
  addExitPointGizmo(exitPoint) {
    this.gizmos.push({
      type: 'exit-point',
      x: exitPoint.position.x,
      y: exitPoint.position.y,
      radius: 10,
      color: '#ff0000',
      strokeWidth: 2,
      data: exitPoint
    });
  }

  /**
   * Render the sublevel editor
   */
  render() {
    if (!this.ctx) return;

    // Clear canvas
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

    // Save context
    this.ctx.save();

    // Apply view transformation
    this.ctx.translate(this.viewState.panX, this.viewState.panY);
    this.ctx.scale(this.viewState.zoom, this.viewState.zoom);

    // Draw grid
    if (this.viewState.showGrid) {
      this.drawGrid();
    }

    // Draw gizmos
    this.drawGizmos();

    // Draw handles
    this.drawHandles();

    // Restore context
    this.ctx.restore();

    // Draw UI overlay (not affected by zoom/pan)
    this.drawOverlay();
  }

  /**
   * Draw grid
   */
  drawGrid() {
    const gridSize = this.viewState.gridSize;
    const zoom = this.viewState.zoom;
    const panX = this.viewState.panX;
    const panY = this.viewState.panY;

    // Calculate visible area
    const startX = Math.floor((-panX / zoom) / gridSize) * gridSize;
    const startY = Math.floor((-panY / zoom) / gridSize) * gridSize;
    const endX = startX + (this.canvas.width / zoom) + gridSize;
    const endY = startY + (this.canvas.height / zoom) + gridSize;

    this.ctx.strokeStyle = '#404040';
    this.ctx.lineWidth = 1 / zoom;
    this.ctx.globalAlpha = 0.3;

    this.ctx.beginPath();

    // Vertical lines
    for (let x = startX; x <= endX; x += gridSize) {
      this.ctx.moveTo(x, startY);
      this.ctx.lineTo(x, endY);
    }

    // Horizontal lines
    for (let y = startY; y <= endY; y += gridSize) {
      this.ctx.moveTo(startX, y);
      this.ctx.lineTo(endX, y);
    }

    this.ctx.stroke();
    this.ctx.globalAlpha = 1.0;
  }

  /**
   * Draw all gizmos
   */
  drawGizmos() {
    this.gizmos.forEach(gizmo => {
      this.drawGizmo(gizmo);
    });
  }

  /**
   * Draw a single gizmo
   * @param {Object} gizmo - Gizmo data
   */
  drawGizmo(gizmo) {
    this.ctx.save();

    switch (gizmo.type) {
      case 'sublevel-bounds':
      case 'connection':
        this.drawRectGizmo(gizmo);
        break;
      case 'spawner':
      case 'entry-point':
      case 'exit-point':
        this.drawCircleGizmo(gizmo);
        break;
      case 'path':
        this.drawPathGizmo(gizmo);
        break;
    }

    this.ctx.restore();
  }

  /**
   * Draw rectangle gizmo
   * @param {Object} gizmo - Rectangle gizmo data
   */
  drawRectGizmo(gizmo) {
    const bounds = gizmo.bounds;

    // Fill
    if (gizmo.fillAlpha > 0) {
      this.ctx.fillStyle = gizmo.color;
      this.ctx.globalAlpha = gizmo.fillAlpha;
      this.ctx.fillRect(bounds.x, bounds.y, bounds.width, bounds.height);
      this.ctx.globalAlpha = 1.0;
    }

    // Stroke
    this.ctx.strokeStyle = gizmo.color;
    this.ctx.lineWidth = gizmo.strokeWidth / this.viewState.zoom;
    this.ctx.strokeRect(bounds.x, bounds.y, bounds.width, bounds.height);
  }

  /**
   * Draw circle gizmo
   * @param {Object} gizmo - Circle gizmo data
   */
  drawCircleGizmo(gizmo) {
    this.ctx.beginPath();
    this.ctx.arc(gizmo.x, gizmo.y, gizmo.radius, 0, Math.PI * 2);

    // Fill
    this.ctx.fillStyle = gizmo.color;
    this.ctx.fill();

    // Stroke
    if (gizmo.strokeColor) {
      this.ctx.strokeStyle = gizmo.strokeColor;
      this.ctx.lineWidth = gizmo.strokeWidth / this.viewState.zoom;
      this.ctx.stroke();
    }
  }

  /**
   * Draw path gizmo
   * @param {Object} gizmo - Path gizmo data
   */
  drawPathGizmo(gizmo) {
    if (gizmo.points.length < 2) return;

    this.ctx.strokeStyle = gizmo.color;
    this.ctx.lineWidth = gizmo.strokeWidth / this.viewState.zoom;
    this.ctx.lineCap = 'round';
    this.ctx.lineJoin = 'round';

    this.ctx.beginPath();

    // Move to first point
    const firstPoint = gizmo.points[0];
    this.ctx.moveTo(firstPoint.position.x, firstPoint.position.y);

    // Draw bezier curves between points
    for (let i = 1; i < gizmo.points.length; i++) {
      const prevPoint = gizmo.points[i - 1];
      const currPoint = gizmo.points[i];

      const cp1x = prevPoint.position.x + prevPoint.controlPoint2.x;
      const cp1y = prevPoint.position.y + prevPoint.controlPoint2.y;
      const cp2x = currPoint.position.x + currPoint.controlPoint1.x;
      const cp2y = currPoint.position.y + currPoint.controlPoint1.y;

      this.ctx.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, currPoint.position.x, currPoint.position.y);
    }

    // Close path if loop
    if (gizmo.isLoop && gizmo.points.length > 2) {
      const lastPoint = gizmo.points[gizmo.points.length - 1];
      const firstPoint = gizmo.points[0];

      const cp1x = lastPoint.position.x + lastPoint.controlPoint2.x;
      const cp1y = lastPoint.position.y + lastPoint.controlPoint2.y;
      const cp2x = firstPoint.position.x + firstPoint.controlPoint1.x;
      const cp2y = firstPoint.position.y + firstPoint.controlPoint1.y;

      this.ctx.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, firstPoint.position.x, firstPoint.position.y);
    }

    this.ctx.stroke();
  }

  /**
   * Draw all handles
   */
  drawHandles() {
    this.handles.forEach(handle => {
      this.drawHandle(handle);
    });
  }

  /**
   * Draw a single handle
   * @param {Object} handle - Handle data
   */
  drawHandle(handle) {
    const size = Math.max(handle.width, handle.height) / this.viewState.zoom;
    const halfSize = size / 2;

    this.ctx.save();

    // Handle background
    this.ctx.fillStyle = '#ffffff';
    this.ctx.strokeStyle = '#333333';
    this.ctx.lineWidth = 1 / this.viewState.zoom;

    this.ctx.fillRect(handle.x - halfSize, handle.y - halfSize, size, size);
    this.ctx.strokeRect(handle.x - halfSize, handle.y - halfSize, size, size);

    // Handle type indicator
    this.ctx.fillStyle = this.getHandleColor(handle.type);
    const indicatorSize = size * 0.6;
    const indicatorHalf = indicatorSize / 2;
    this.ctx.fillRect(handle.x - indicatorHalf, handle.y - indicatorHalf, indicatorSize, indicatorSize);

    this.ctx.restore();
  }

  /**
   * Get handle color based on type
   * @param {string} type - Handle type
   * @returns {string} Color
   */
  getHandleColor(type) {
    switch (type) {
      case 'bounds-handle':
      case 'bounds-edge':
        return '#007acc';
      case 'spawner-move':
        return '#ff6b35';
      case 'path-point':
        return '#00ff88';
      case 'path-control':
        return '#88ff00';
      default:
        return '#666666';
    }
  }

  /**
   * Draw UI overlay
   */
  drawOverlay() {
    // Draw zoom level
    this.ctx.fillStyle = '#cccccc';
    this.ctx.font = '12px Arial';
    this.ctx.fillText(`Zoom: ${Math.round(this.viewState.zoom * 100)}%`, 10, 20);

    // Draw current sublevel name
    if (this.currentSubLevel) {
      this.ctx.fillText(`SubLevel: ${this.currentSubLevel.name}`, 10, 40);
    }

    // Draw edit mode
    const editMode = this.editorState.getEditMode();
    this.ctx.fillText(`Mode: ${editMode}`, 10, 60);
  }

  /**
   * Convert screen coordinates to world coordinates
   * @param {number} screenX - Screen X coordinate
   * @param {number} screenY - Screen Y coordinate
   * @returns {Object} World coordinates {x, y}
   */
  screenToWorld(screenX, screenY) {
    return {
      x: (screenX - this.viewState.panX) / this.viewState.zoom,
      y: (screenY - this.viewState.panY) / this.viewState.zoom
    };
  }

  /**
   * Convert world coordinates to screen coordinates
   * @param {number} worldX - World X coordinate
   * @param {number} worldY - World Y coordinate
   * @returns {Object} Screen coordinates {x, y}
   */
  worldToScreen(worldX, worldY) {
    return {
      x: worldX * this.viewState.zoom + this.viewState.panX,
      y: worldY * this.viewState.zoom + this.viewState.panY
    };
  }

  /**
   * Find handle at screen position
   * @param {number} screenX - Screen X coordinate
   * @param {number} screenY - Screen Y coordinate
   * @returns {Object|null} Handle or null
   */
  findHandleAtPosition(screenX, screenY) {
    const worldPos = this.screenToWorld(screenX, screenY);

    for (const handle of this.handles) {
      const halfWidth = handle.width / 2;
      const halfHeight = handle.height / 2;

      if (worldPos.x >= handle.x - halfWidth &&
          worldPos.x <= handle.x + halfWidth &&
          worldPos.y >= handle.y - halfHeight &&
          worldPos.y <= handle.y + halfHeight) {
        return handle;
      }
    }

    return null;
  }

  /**
   * Find gizmo at screen position
   * @param {number} screenX - Screen X coordinate
   * @param {number} screenY - Screen Y coordinate
   * @returns {Object|null} Gizmo or null
   */
  findGizmoAtPosition(screenX, screenY) {
    const worldPos = this.screenToWorld(screenX, screenY);

    for (const gizmo of this.gizmos) {
      if (this.isPointInGizmo(worldPos, gizmo)) {
        return gizmo;
      }
    }

    return null;
  }

  /**
   * Check if point is inside gizmo
   * @param {Object} point - World point {x, y}
   * @param {Object} gizmo - Gizmo data
   * @returns {boolean} True if point is inside gizmo
   */
  isPointInGizmo(point, gizmo) {
    switch (gizmo.type) {
      case 'sublevel-bounds':
      case 'connection':
        const bounds = gizmo.bounds;
        return point.x >= bounds.x && point.x <= bounds.x + bounds.width &&
               point.y >= bounds.y && point.y <= bounds.y + bounds.height;

      case 'spawner':
      case 'entry-point':
      case 'exit-point':
        const dx = point.x - gizmo.x;
        const dy = point.y - gizmo.y;
        return Math.sqrt(dx * dx + dy * dy) <= gizmo.radius;

      case 'path':
        // Simplified path hit testing - check distance to points
        for (const pathPoint of gizmo.points) {
          const dx = point.x - pathPoint.position.x;
          const dy = point.y - pathPoint.position.y;
          if (Math.sqrt(dx * dx + dy * dy) <= 10) {
            return true;
          }
        }
        return false;

      default:
        return false;
    }
  }

  /**
   * Handle mouse down event
   * @param {MouseEvent} event - Mouse event
   */
  onMouseDown(event) {
    event.preventDefault();

    const rect = this.canvas.getBoundingClientRect();
    const screenX = event.clientX - rect.left;
    const screenY = event.clientY - rect.top;

    // Check for handle interaction first
    const handle = this.findHandleAtPosition(screenX, screenY);
    if (handle) {
      this.startHandleDrag(handle, screenX, screenY);
      return;
    }

    // Check for gizmo interaction
    const gizmo = this.findGizmoAtPosition(screenX, screenY);
    if (gizmo) {
      this.selectGizmo(gizmo);
      this.startGizmoDrag(gizmo, screenX, screenY);
      return;
    }

    // Start pan operation
    this.startPan(screenX, screenY);
  }

  /**
   * Handle mouse move event
   * @param {MouseEvent} event - Mouse event
   */
  onMouseMove(event) {
    const rect = this.canvas.getBoundingClientRect();
    const screenX = event.clientX - rect.left;
    const screenY = event.clientY - rect.top;

    if (this.isDragging) {
      this.updateDrag(screenX, screenY);
    } else {
      this.updateHover(screenX, screenY);
    }
  }

  /**
   * Handle mouse up event
   * @param {MouseEvent} event - Mouse event
   */
  onMouseUp(event) {
    if (this.isDragging) {
      this.endDrag();
    }
  }

  /**
   * Handle wheel event (zoom)
   * @param {WheelEvent} event - Wheel event
   */
  onWheel(event) {
    event.preventDefault();

    const rect = this.canvas.getBoundingClientRect();
    const screenX = event.clientX - rect.left;
    const screenY = event.clientY - rect.top;

    // Zoom factor
    const zoomFactor = event.deltaY > 0 ? 0.9 : 1.1;
    const newZoom = Math.max(0.1, Math.min(5.0, this.viewState.zoom * zoomFactor));

    // Zoom towards mouse position
    const worldPos = this.screenToWorld(screenX, screenY);

    this.viewState.zoom = newZoom;

    // Adjust pan to keep mouse position fixed
    const newScreenPos = this.worldToScreen(worldPos.x, worldPos.y);
    this.viewState.panX += screenX - newScreenPos.x;
    this.viewState.panY += screenY - newScreenPos.y;

    // Update editor state
    this.editorState.updateViewSettings({
      zoom: this.viewState.zoom,
      panX: this.viewState.panX,
      panY: this.viewState.panY
    });

    this.render();
  }

  /**
   * Handle context menu event
   * @param {MouseEvent} event - Mouse event
   */
  onContextMenu(event) {
    event.preventDefault();

    const rect = this.canvas.getBoundingClientRect();
    const screenX = event.clientX - rect.left;
    const screenY = event.clientY - rect.top;

    // Find what's under the cursor
    const gizmo = this.findGizmoAtPosition(screenX, screenY);

    this.eventBus.emit('show-context-menu', {
      x: event.clientX,
      y: event.clientY,
      target: gizmo,
      worldPos: this.screenToWorld(screenX, screenY),
      actions: this.getContextMenuActions(gizmo)
    });
  }

  /**
   * Get context menu actions for target
   * @param {Object|null} target - Target gizmo
   * @returns {Array} Array of actions
   */
  getContextMenuActions(target) {
    const actions = [];

    if (!target) {
      // Empty space
      actions.push('add-spawner', 'add-path', 'add-connection');
    } else {
      switch (target.type) {
        case 'spawner':
          actions.push('edit-spawner', 'duplicate-spawner', 'delete-spawner');
          break;
        case 'path':
          actions.push('edit-path', 'add-path-point', 'duplicate-path', 'delete-path');
          break;
        case 'connection':
          actions.push('edit-connection', 'delete-connection');
          break;
        case 'sublevel-bounds':
          actions.push('fit-to-content', 'reset-bounds');
          break;
      }
    }

    return actions;
  }

  /**
   * Start handle drag operation
   * @param {Object} handle - Handle to drag
   * @param {number} screenX - Screen X coordinate
   * @param {number} screenY - Screen Y coordinate
   */
  startHandleDrag(handle, screenX, screenY) {
    this.isDragging = true;
    this.dragStart = { x: screenX, y: screenY };
    this.dragTarget = handle;
    this.dragType = 'handle';

    this.canvas.style.cursor = handle.cursor;
  }

  /**
   * Start gizmo drag operation
   * @param {Object} gizmo - Gizmo to drag
   * @param {number} screenX - Screen X coordinate
   * @param {number} screenY - Screen Y coordinate
   */
  startGizmoDrag(gizmo, screenX, screenY) {
    this.isDragging = true;
    this.dragStart = { x: screenX, y: screenY };
    this.dragTarget = gizmo;
    this.dragType = 'gizmo';

    this.canvas.style.cursor = 'move';
  }

  /**
   * Start pan operation
   * @param {number} screenX - Screen X coordinate
   * @param {number} screenY - Screen Y coordinate
   */
  startPan(screenX, screenY) {
    this.isDragging = true;
    this.dragStart = { x: screenX, y: screenY };
    this.dragTarget = null;
    this.dragType = 'pan';

    this.canvas.style.cursor = 'grab';
  }

  /**
   * Update drag operation
   * @param {number} screenX - Current screen X coordinate
   * @param {number} screenY - Current screen Y coordinate
   */
  updateDrag(screenX, screenY) {
    const deltaX = screenX - this.dragStart.x;
    const deltaY = screenY - this.dragStart.y;

    switch (this.dragType) {
      case 'handle':
        this.updateHandleDrag(deltaX, deltaY);
        break;
      case 'gizmo':
        this.updateGizmoDrag(deltaX, deltaY);
        break;
      case 'pan':
        this.updatePan(deltaX, deltaY);
        break;
    }

    this.render();
  }

  /**
   * Update handle drag
   * @param {number} deltaX - X delta
   * @param {number} deltaY - Y delta
   */
  updateHandleDrag(deltaX, deltaY) {
    const handle = this.dragTarget;
    const worldDelta = {
      x: deltaX / this.viewState.zoom,
      y: deltaY / this.viewState.zoom
    };

    switch (handle.type) {
      case 'bounds-handle':
        this.updateBoundsHandle(handle, worldDelta);
        break;
      case 'bounds-edge':
        this.updateBoundsEdge(handle, worldDelta);
        break;
      case 'spawner-move':
        this.updateSpawnerPosition(handle, worldDelta);
        break;
      case 'path-point':
        this.updatePathPoint(handle, worldDelta);
        break;
      case 'path-control':
        this.updatePathControl(handle, worldDelta);
        break;
    }

    this.updateGizmos();
  }

  /**
   * Update bounds handle
   * @param {Object} handle - Handle data
   * @param {Object} worldDelta - World space delta
   */
  updateBoundsHandle(handle, worldDelta) {
    const bounds = handle.target.bounds;

    switch (handle.corner) {
      case 'top-left':
        bounds.width += bounds.x - (bounds.x + worldDelta.x);
        bounds.height += bounds.y - (bounds.y + worldDelta.y);
        bounds.x += worldDelta.x;
        bounds.y += worldDelta.y;
        break;
      case 'top-right':
        bounds.width += worldDelta.x;
        bounds.height += bounds.y - (bounds.y + worldDelta.y);
        bounds.y += worldDelta.y;
        break;
      case 'bottom-left':
        bounds.width += bounds.x - (bounds.x + worldDelta.x);
        bounds.height += worldDelta.y;
        bounds.x += worldDelta.x;
        break;
      case 'bottom-right':
        bounds.width += worldDelta.x;
        bounds.height += worldDelta.y;
        break;
    }

    // Ensure minimum size
    bounds.width = Math.max(50, bounds.width);
    bounds.height = Math.max(50, bounds.height);

    this.editorState.markModified();
  }

  /**
   * Update bounds edge
   * @param {Object} handle - Handle data
   * @param {Object} worldDelta - World space delta
   */
  updateBoundsEdge(handle, worldDelta) {
    const bounds = handle.target.bounds;

    switch (handle.edge) {
      case 'top':
        bounds.height += bounds.y - (bounds.y + worldDelta.y);
        bounds.y += worldDelta.y;
        break;
      case 'right':
        bounds.width += worldDelta.x;
        break;
      case 'bottom':
        bounds.height += worldDelta.y;
        break;
      case 'left':
        bounds.width += bounds.x - (bounds.x + worldDelta.x);
        bounds.x += worldDelta.x;
        break;
    }

    // Ensure minimum size
    bounds.width = Math.max(50, bounds.width);
    bounds.height = Math.max(50, bounds.height);

    this.editorState.markModified();
  }

  /**
   * Update spawner position
   * @param {Object} handle - Handle data
   * @param {Object} worldDelta - World space delta
   */
  updateSpawnerPosition(handle, worldDelta) {
    handle.target.position.x += worldDelta.x;
    handle.target.position.y += worldDelta.y;

    this.editorState.markModified();
  }

  /**
   * Update path point
   * @param {Object} handle - Handle data
   * @param {Object} worldDelta - World space delta
   */
  updatePathPoint(handle, worldDelta) {
    const point = handle.target.points[handle.pointIndex];
    point.position.x += worldDelta.x;
    point.position.y += worldDelta.y;

    this.editorState.markModified();
  }

  /**
   * Update path control point
   * @param {Object} handle - Handle data
   * @param {Object} worldDelta - World space delta
   */
  updatePathControl(handle, worldDelta) {
    const point = handle.target.points[handle.pointIndex];
    const controlPoint = handle.controlIndex === 1 ? point.controlPoint1 : point.controlPoint2;

    controlPoint.x += worldDelta.x;
    controlPoint.y += worldDelta.y;

    this.editorState.markModified();
  }

  /**
   * Update gizmo drag
   * @param {number} deltaX - X delta
   * @param {number} deltaY - Y delta
   */
  updateGizmoDrag(deltaX, deltaY) {
    // For now, gizmo dragging is handled by handles
    // This could be extended for direct gizmo manipulation
  }

  /**
   * Update pan
   * @param {number} deltaX - X delta
   * @param {number} deltaY - Y delta
   */
  updatePan(deltaX, deltaY) {
    this.viewState.panX = this.viewState.panX + deltaX;
    this.viewState.panY = this.viewState.panY + deltaY;

    // Update drag start for continuous panning
    this.dragStart.x += deltaX;
    this.dragStart.y += deltaY;
  }

  /**
   * End drag operation
   */
  endDrag() {
    this.isDragging = false;
    this.dragTarget = null;
    this.dragType = null;
    this.canvas.style.cursor = 'default';

    // Update editor state with final view settings
    this.editorState.updateViewSettings({
      zoom: this.viewState.zoom,
      panX: this.viewState.panX,
      panY: this.viewState.panY
    });
  }

  /**
   * Update hover state
   * @param {number} screenX - Screen X coordinate
   * @param {number} screenY - Screen Y coordinate
   */
  updateHover(screenX, screenY) {
    // Check for handle hover
    const handle = this.findHandleAtPosition(screenX, screenY);
    if (handle) {
      this.canvas.style.cursor = handle.cursor;
      return;
    }

    // Check for gizmo hover
    const gizmo = this.findGizmoAtPosition(screenX, screenY);
    if (gizmo) {
      this.canvas.style.cursor = 'pointer';
      this.hoveredItem = gizmo;
    } else {
      this.canvas.style.cursor = 'default';
      this.hoveredItem = null;
    }
  }

  /**
   * Select gizmo
   * @param {Object} gizmo - Gizmo to select
   */
  selectGizmo(gizmo) {
    // Find the corresponding data object and emit selection
    let selectionData = null;

    switch (gizmo.type) {
      case 'spawner':
        selectionData = { id: gizmo.data.id, type: 'spawner', data: gizmo.data };
        break;
      case 'path':
        selectionData = { id: gizmo.data.id, type: 'path', data: gizmo.data };
        break;
      case 'connection':
        selectionData = { id: gizmo.data.id || 'connection', type: 'connection', data: gizmo.data };
        break;
      case 'entry-point':
        selectionData = { id: 'entry-point', type: 'entry-point', data: gizmo.data };
        break;
      case 'exit-point':
        selectionData = { id: 'exit-point', type: 'exit-point', data: gizmo.data };
        break;
    }

    if (selectionData) {
      this.editorState.setSelection([selectionData]);
    }
  }

  /**
   * Handle keyboard events
   * @param {KeyboardEvent} event - Keyboard event
   */
  onKeyDown(event) {
    switch (event.key) {
      case 'Delete':
        this.deleteSelected();
        break;
      case 'Escape':
        this.editorState.clearSelection();
        break;
      case 'f':
      case 'F':
        if (event.ctrlKey || event.metaKey) {
          event.preventDefault();
          this.fitToSubLevel();
        }
        break;
    }
  }

  /**
   * Handle key up events
   * @param {KeyboardEvent} event - Keyboard event
   */
  onKeyUp(event) {
    // Handle key up events if needed
  }

  /**
   * Delete selected items
   */
  deleteSelected() {
    const selection = this.editorState.getSelection();

    selection.forEach(item => {
      switch (item.type) {
        case 'spawner':
          this.deleteSpawner(item.data);
          break;
        case 'path':
          this.deletePath(item.data);
          break;
        case 'connection':
          this.deleteConnection(item.data);
          break;
      }
    });

    this.editorState.clearSelection();
    this.updateGizmos();
    this.render();
  }

  /**
   * Delete spawner
   * @param {Object} spawner - Spawner to delete
   */
  deleteSpawner(spawner) {
    if (!this.currentSubLevel) return;

    const index = this.currentSubLevel.spawners.indexOf(spawner);
    if (index !== -1) {
      this.currentSubLevel.spawners.splice(index, 1);
      this.editorState.markModified();
    }
  }

  /**
   * Delete path
   * @param {Object} path - Path to delete
   */
  deletePath(path) {
    if (!this.currentSubLevel) return;

    const index = this.currentSubLevel.paths.indexOf(path);
    if (index !== -1) {
      this.currentSubLevel.paths.splice(index, 1);
      this.editorState.markModified();
    }
  }

  /**
   * Delete connection
   * @param {Object} connection - Connection to delete
   */
  deleteConnection(connection) {
    if (!this.currentSubLevel) return;

    const index = this.currentSubLevel.connections.indexOf(connection);
    if (index !== -1) {
      this.currentSubLevel.connections.splice(index, 1);
      this.editorState.markModified();
    }
  }

  /**
   * Destroy the component
   */
  destroy() {
    // Remove event listeners
    this.eventBus.off('level-data-changed', this.onLevelDataChanged, this);
    this.eventBus.off('selection-changed', this.onSelectionChanged, this);
    this.eventBus.off('view-settings-changed', this.onViewSettingsChanged, this);

    window.removeEventListener('resize', this.resizeCanvas.bind(this));
    document.removeEventListener('keydown', this.onKeyDown.bind(this));
    document.removeEventListener('keyup', this.onKeyUp.bind(this));

    // Clear references
    this.canvas = null;
    this.ctx = null;
    this.currentSubLevel = null;
    this.levelData = null;
    this.gizmos = [];
    this.handles = [];
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = SubLevelEditor;
} else {
  window.SubLevelEditor = SubLevelEditor;
}
