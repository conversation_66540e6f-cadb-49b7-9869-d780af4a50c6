import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('Emitter')
export class Emitter extends Component {

    @property({type: Node})
    bulletPrefab: Node = null;

    // 发射角度
    @property
    angle: number = 0;

    // 发射半径
    @property
    radius: number = 0;

    // 发射条数
    @property
    count: number = 1;

    // 子弹初速度
    @property
    speed: number = 0;

    // 频率(间隔多少秒发射一次)
    @property
    frequency: number = 1;

    update(deltaTime: number) {
        
    }

    // draw gizmos
    onDrawGizmos() {
        // draw gizmos for the emitter base on angle, radius, and count, the angle should be divided by count
        // and draw an arc with the radius and angle, draw an arrow for the direction of the bullet
        const angleStep = this.angle / this.count;
        const angle = angleStep / 2;
        const radius = this.radius;
        const count = this.count;
        
        for (let i = 0; i < count; i++) {
            const currentAngle = angle + i * angleStep;
            const x = radius * Math.cos(currentAngle);
            const y = radius * Math.sin(currentAngle);
            // draw an arrow at (x, y) with the direction of the bullet
            // assuming you have a method to draw an arrow
            this.drawArrow(x, y, this.speed, currentAngle);
        }

        
    }
}


