System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, TypedBase, _dec, _class, _crd, ccclass, System;

  function _reportPossibleCrUseOfTypedBase(extras) {
    _reporterNs.report("TypedBase", "./TypeID", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      TypedBase = _unresolved_2.TypedBase;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "684633sA8hEg6hYiL4Y2kZ1", "System", undefined);

      __checkObsolete__(['_decorator']);

      ({
        ccclass
      } = _decorator);
      /**
       * Base System class for all game systems
       * Follows the same pattern as IMgr but specifically designed for game world systems
       */

      _export("System", System = (_dec = ccclass("System"), _dec(_class = class System extends (_crd && TypedBase === void 0 ? (_reportPossibleCrUseOfTypedBase({
        error: Error()
      }), TypedBase) : TypedBase) {
        constructor(...args) {
          super(...args);
          this._isInitialized = false;
          this._isEnabled = true;
        }

        /**
         * Initialize the system
         * Called once when the system is registered to the world
         */
        init() {
          if (this._isInitialized) {
            console.warn(`System ${this.getTypeName()} is already initialized`);
            return;
          }

          this.onInit();
          this._isInitialized = true;
        }
        /**
         * Cleanup the system
         * Called when the system is removed or world is destroyed
         */


        unInit() {
          if (!this._isInitialized) {
            return;
          }

          this.onUnInit();
          this._isInitialized = false;
        }
        /**
         * Update the system
         * Called every frame when the system is enabled
         * @param deltaTime Time elapsed since last frame in seconds
         */


        update(deltaTime) {
          if (!this._isInitialized || !this._isEnabled) {
            return;
          }

          this.onUpdate(deltaTime);
        }
        /**
         * Late update the system
         * Called after all systems have been updated
         * @param deltaTime Time elapsed since last frame in seconds
         */


        lateUpdate(deltaTime) {
          if (!this._isInitialized || !this._isEnabled) {
            return;
          }

          this.onLateUpdate(deltaTime);
        }
        /**
         * Enable or disable the system
         * Disabled systems will not receive update calls
         */


        setEnabled(enabled) {
          this._isEnabled = enabled;
        }
        /**
         * Check if the system is enabled
         */


        isEnabled() {
          return this._isEnabled;
        }
        /**
         * Check if the system is initialized
         */


        isInitialized() {
          return this._isInitialized;
        }
        /**
         * Override this method to implement system initialization logic
         */

        /**
         * Override this method to implement system cleanup logic
         */

        /**
         * Override this method to implement system update logic
         * @param deltaTime Time elapsed since last frame in seconds
         */

        /**
         * Override this method to implement system late update logic
         * @param deltaTime Time elapsed since last frame in seconds
         */


      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=18115108ff656ee90e2863a8933b4707545ec1a5.js.map