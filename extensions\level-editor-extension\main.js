'use strict';

const Path = require('path');
const Fs = require('fs');

// Extension state variables
let currentLevelPath = null;
let currentLevelData = null;
let isModified = false;

/**
 * Level Editor Extension Main Process
 * Handles IPC communication between the panel and Cocos Creator editor
 */
module.exports = {
  
  /**
   * Extension load lifecycle
   */
  load() {
    console.log('[Level Editor] Extension loaded');
    
    // Initialize extension state
    currentLevelPath = null;
    currentLevelData = null;
    isModified = false;
    
    // Setup file watchers if needed
    setupFileWatchers();
  },

  /**
   * Extension unload lifecycle
   */
  unload() {
    console.log('[Level Editor] Extension unloaded');
    
    // Cleanup resources
    cleanup();
  },

  /**
   * Message handlers
   */
  methods: {

    /**
     * Open the Level Editor panel
     */
    openPanel() {
      Editor.Panel.open('level-editor-extension.level-editor');
    },

    /**
     * Create a new level
     */
    newLevel() {
      console.log('[Level Editor] Creating new level');
      
      // Load default level template
      const templatePath = Path.join(__dirname, 'templates', 'level-template.json');
      
      try {
        const templateData = Fs.readFileSync(templatePath, 'utf8');
        const levelData = JSON.parse(templateData);
        
        // Generate unique IDs and timestamps
        levelData.metadata.createdAt = new Date().toISOString();
        levelData.metadata.modifiedAt = new Date().toISOString();
        
        // Reset state
        currentLevelPath = null;
        currentLevelData = levelData;
        isModified = true;
        
        // Notify panel
        broadcastToPanel('level-data-changed', levelData);
        
        console.log('[Level Editor] New level created');

      } catch (error) {
        console.error('[Level Editor] Failed to create new level:', error);
      }
    },

    /**
     * Load level from file
     */
    loadLevel() {
      console.log('[Level Editor] Loading level from file');
      
      // Open file dialog
      const filePath = Editor.Dialog.openFile({
        title: 'Load Level File',
        defaultPath: Editor.Project.path,
        filters: [
          { name: 'Level Files', extensions: ['json', 'level'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      });
      
      if (filePath && filePath.length > 0) {
        this.loadLevelFromPath(filePath[0]);
      }
    },

    /**
     * Save current level
     */
    saveLevel() {
      if (currentLevelPath) {
        saveLevelToPath(currentLevelPath);
      } else {
        // No current path, trigger save as
        module.exports.methods.saveLevelAs();
      }
    },

    /**
     * Save level with new path
     */
    saveLevelAs() {
      console.log('[Level Editor] Save level as...');
      
      const filePath = Editor.Dialog.saveFile({
        title: 'Save Level File',
        defaultPath: Editor.Project.path,
        filters: [
          { name: 'Level Files', extensions: ['json'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      });
      
      if (filePath) {
        saveLevelToPath(filePath);
      }
    },

    /**
     * Import level from external format
     */
    importLevel() {
      console.log('[Level Editor] Import level');
      // TODO: Implement level import functionality
      console.log('[Level Editor] Import functionality coming soon');
    },

    /**
     * Export level to external format
     */
    exportLevel() {
      console.log('[Level Editor] Export level');
      // TODO: Implement level export functionality
      console.log('[Level Editor] Export functionality coming soon');
    },

    /**
     * Sync level data from panel
     */
    'sync-level-data'(levelData) {
      currentLevelData = levelData;
      isModified = true;

      // Update modification timestamp
      if (levelData && levelData.metadata) {
        levelData.metadata.modifiedAt = new Date().toISOString();
      }

      console.log('[Level Editor] Level data synchronized');
    },

    /**
     * Get current level data
     */
    'get-level-data'() {
      return currentLevelData;
    },

    /**
     * Check if level has unsaved changes
     */
    'is-modified'() {
      return isModified;
    }
  }
};

/**
 * Load level from file path
 */
function loadLevelFromPath(filePath) {
    try {
      const fileData = Fs.readFileSync(filePath, 'utf8');
      const levelData = JSON.parse(fileData);

      currentLevelPath = filePath;
      currentLevelData = levelData;
      isModified = false;

      // Notify panel
      broadcastToPanel('level-data-changed', levelData);

      console.log(`[Level Editor] Level loaded: ${Path.basename(filePath)}`);

    } catch (error) {
      console.error('[Level Editor] Failed to load level:', error);
    }
};

/**
 * Save level to file path
 */
function saveLevelToPath(filePath) {
  try {
    if (!currentLevelData) {
      console.warn('[Level Editor] No level data to save');
      return;
    }

    // Update metadata
    currentLevelData.metadata.modifiedAt = new Date().toISOString();

    // Serialize and save
    const fileData = JSON.stringify(currentLevelData, null, 2);
    Fs.writeFileSync(filePath, fileData, 'utf8');

    currentLevelPath = filePath;
    isModified = false;

    // Notify panel
    broadcastToPanel('level-saved', { path: filePath, data: currentLevelData });

    console.log(`[Level Editor] Level saved: ${Path.basename(filePath)}`);

  } catch (error) {
    console.error('[Level Editor] Failed to save level:', error);
  }
}

/**
 * Setup file watchers
 */
function setupFileWatchers() {
  // File watching functionality can be added here if needed
  console.log('[Level Editor] File watchers setup (placeholder)');
}

/**
 * Cleanup resources
 */
function cleanup() {
  currentLevelPath = null;
  currentLevelData = null;
  isModified = false;
}

/**
 * Broadcast message to panel
 */
function broadcastToPanel(message, data) {
  Editor.Message.send('level-editor-extension.level-editor', message, data);
}
