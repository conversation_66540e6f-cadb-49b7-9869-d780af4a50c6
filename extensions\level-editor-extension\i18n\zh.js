module.exports = {
  // Panel titles
  'level-editor': '关卡编辑器',
  'level-hierarchy': '关卡层级',
  'property-inspector': '属性检查器',
  'sublevel-editor': '子关卡编辑器',
  'toolbar': '工具栏',
  
  // Menu items
  'new-level': '新建关卡',
  'load-level': '加载关卡',
  'save-level': '保存关卡',
  'save-level-as': '另存为...',
  'import-level': '导入关卡',
  'export-level': '导出关卡',
  
  // Toolbar actions
  'select-mode': '选择模式',
  'add-sublevel': '添加子关卡',
  'add-spawner': '添加生成器',
  'add-path': '添加路径',
  'add-map-layer': '添加地图层',
  'preview-level': '预览关卡',
  'stop-preview': '停止预览',
  
  // Level hierarchy
  'level': '关卡',
  'sublevels': '子关卡',
  'spawners': '生成器',
  'paths': '路径',
  'map-layers': '地图层',
  'events': '事件',
  
  // Property labels
  'name': '名称',
  'description': '描述',
  'author': '作者',
  'version': '版本',
  'duration': '持续时间（秒）',
  'difficulty': '难度',
  'bounds': '边界',
  'position': '位置',
  'size': '大小',
  'active': '激活',
  'visible': '可见',
  
  // SubLevel properties
  'entry-point': '入口点',
  'exit-point': '出口点',
  'connections': '连接',
  'load-priority': '加载优先级',
  
  // Spawner properties
  'prefab-path': '预制体路径',
  'spawn-pattern': '生成模式',
  'spawn-count': '生成数量',
  'spawn-interval': '生成间隔',
  'spawn-delay': '生成延迟',
  
  // Path properties
  'path-points': '路径点',
  'is-loop': '是否循环',
  'total-length': '总长度',
  
  // Map layer properties
  'layer-type': '层类型',
  'depth': '深度',
  'scroll-speed': '滚动速度',
  'repeat-mode': '重复模式',
  'offset': '偏移',
  
  // Camera settings
  'viewport-size': '视口大小',
  'scroll-speed': '滚动速度',
  'follow-target': '跟随目标',
  'camera-bounds': '相机边界',
  'smoothing': '平滑度',
  
  // Messages
  'no-level-loaded': '未加载关卡。请创建新关卡或加载现有关卡。',
  'no-selection': '未选择项目。请选择一个项目以查看其属性。',
  'unsaved-changes': '您有未保存的更改。是否要在继续之前保存？',
  'level-saved': '关卡保存成功。',
  'level-loaded': '关卡加载成功。',
  'invalid-data': '无效的数据格式。',
  
  // Buttons
  'ok': '确定',
  'cancel': '取消',
  'save': '保存',
  'load': '加载',
  'delete': '删除',
  'duplicate': '复制',
  'add': '添加',
  'remove': '移除',
  'browse': '浏览...',
  'reset': '重置',
  'apply': '应用',
  
  // Validation messages
  'required-field': '此字段为必填项。',
  'invalid-number': '请输入有效数字。',
  'invalid-range': '值必须在 {min} 和 {max} 之间。',
  'invalid-file-path': '无效的文件路径。',
  'name-already-exists': '已存在同名项目。',
  
  // Context menu
  'add-sublevel': '添加子关卡',
  'add-spawner': '添加生成器',
  'add-path': '添加路径',
  'add-map-layer': '添加地图层',
  'duplicate-item': '复制',
  'delete-item': '删除',
  'rename-item': '重命名',
  'copy-item': '复制',
  'paste-item': '粘贴',
  
  // Edit modes
  'select': '选择',
  'add-spawner': '添加生成器',
  'edit-path': '编辑路径',
  'add-background': '添加背景',
  'edit-events': '编辑事件',
  'add-sublevel': '添加子关卡',
  'edit-sublevel': '编辑子关卡'
};
