/**
 * Editor State Management
 * Centralized state management for the level editor
 */
class EditorState {
  constructor(eventBus) {
    this.eventBus = eventBus;
    
    // Core state
    this.currentLevel = null;
    this.selectedItems = [];
    this.editMode = 'select';
    this.isModified = false;
    this.isPreviewMode = false;
    
    // View settings
    this.viewSettings = {
      gridEnabled: true,
      gridSize: 50,
      snapToGrid: false,
      showGizmoLabels: true,
      showPreview: true,
      zoom: 1.0,
      panX: 0,
      panY: 0
    };
    
    // UI state
    this.uiState = {
      hierarchyExpanded: {},
      propertyGroupsExpanded: {},
      panelSizes: {
        leftPanel: 250,
        rightPanel: 280
      }
    };
    
    // History for undo/redo
    this.history = {
      undoStack: [],
      redoStack: [],
      maxHistorySize: 50
    };
    
    // Initialize
    this.initialize();
  }

  /**
   * Initialize the state
   */
  initialize() {
    // Load saved settings from localStorage
    this.loadSettings();
    
    // Setup auto-save for settings
    this.setupAutoSave();
  }

  /**
   * Set the current level
   * @param {Object} level - The level data
   */
  setCurrentLevel(level) {
    const previousLevel = this.currentLevel;
    this.currentLevel = level;
    this.isModified = false;
    
    // Clear selection when level changes
    this.clearSelection();
    
    // Reset view settings for new level
    this.resetViewSettings();
    
    // Emit event
    this.eventBus.emitLevelDataChanged(level);
    
    console.log('[EditorState] Level set:', level?.metadata?.name || 'null');
  }

  /**
   * Get the current level
   * @returns {Object|null} The current level data
   */
  getCurrentLevel() {
    return this.currentLevel;
  }

  /**
   * Mark the level as modified
   */
  markModified() {
    if (!this.isModified) {
      this.isModified = true;
      this.eventBus.emit('level-modified', true);
    }
  }

  /**
   * Mark the level as saved
   */
  markSaved() {
    if (this.isModified) {
      this.isModified = false;
      this.eventBus.emit('level-modified', false);
    }
  }

  /**
   * Check if the level has unsaved changes
   * @returns {boolean} True if modified
   */
  isLevelModified() {
    return this.isModified;
  }

  /**
   * Set the selection
   * @param {Array} items - Array of selected items
   */
  setSelection(items) {
    this.selectedItems = Array.isArray(items) ? items : [items];
    this.eventBus.emitSelectionChanged(this.selectedItems);
  }

  /**
   * Add to selection
   * @param {Object} item - Item to add to selection
   */
  addToSelection(item) {
    if (!this.selectedItems.includes(item)) {
      this.selectedItems.push(item);
      this.eventBus.emitSelectionChanged(this.selectedItems);
    }
  }

  /**
   * Remove from selection
   * @param {Object} item - Item to remove from selection
   */
  removeFromSelection(item) {
    const index = this.selectedItems.indexOf(item);
    if (index !== -1) {
      this.selectedItems.splice(index, 1);
      this.eventBus.emitSelectionChanged(this.selectedItems);
    }
  }

  /**
   * Clear selection
   */
  clearSelection() {
    if (this.selectedItems.length > 0) {
      this.selectedItems = [];
      this.eventBus.emitSelectionChanged(this.selectedItems);
    }
  }

  /**
   * Get current selection
   * @returns {Array} Array of selected items
   */
  getSelection() {
    return this.selectedItems;
  }

  /**
   * Get first selected item
   * @returns {Object|null} First selected item or null
   */
  getFirstSelected() {
    return this.selectedItems.length > 0 ? this.selectedItems[0] : null;
  }

  /**
   * Set edit mode
   * @param {string} mode - The edit mode
   */
  setEditMode(mode) {
    const previousMode = this.editMode;
    this.editMode = mode;
    this.eventBus.emitEditModeChanged(mode, previousMode);
  }

  /**
   * Get current edit mode
   * @returns {string} Current edit mode
   */
  getEditMode() {
    return this.editMode;
  }

  /**
   * Set preview mode
   * @param {boolean} enabled - Whether preview mode is enabled
   */
  setPreviewMode(enabled) {
    this.isPreviewMode = enabled;
    this.eventBus.emit('preview-mode-changed', enabled);
  }

  /**
   * Check if in preview mode
   * @returns {boolean} True if in preview mode
   */
  isInPreviewMode() {
    return this.isPreviewMode;
  }

  /**
   * Update view settings
   * @param {Object} settings - Settings to update
   */
  updateViewSettings(settings) {
    Object.assign(this.viewSettings, settings);
    this.eventBus.emit('view-settings-changed', this.viewSettings);
    this.saveSettings();
  }

  /**
   * Get view settings
   * @returns {Object} Current view settings
   */
  getViewSettings() {
    return this.viewSettings;
  }

  /**
   * Reset view settings to defaults
   */
  resetViewSettings() {
    this.viewSettings.zoom = 1.0;
    this.viewSettings.panX = 0;
    this.viewSettings.panY = 0;
    this.eventBus.emit('view-settings-changed', this.viewSettings);
  }

  /**
   * Update UI state
   * @param {Object} state - UI state to update
   */
  updateUIState(state) {
    Object.assign(this.uiState, state);
    this.saveSettings();
  }

  /**
   * Get UI state
   * @returns {Object} Current UI state
   */
  getUIState() {
    return this.uiState;
  }

  /**
   * Add to history for undo/redo
   * @param {Object} action - Action to add to history
   */
  addToHistory(action) {
    // Add to undo stack
    this.history.undoStack.push(action);
    
    // Clear redo stack
    this.history.redoStack = [];
    
    // Limit history size
    if (this.history.undoStack.length > this.history.maxHistorySize) {
      this.history.undoStack.shift();
    }
    
    this.eventBus.emit('history-changed', {
      canUndo: this.canUndo(),
      canRedo: this.canRedo()
    });
  }

  /**
   * Check if can undo
   * @returns {boolean} True if can undo
   */
  canUndo() {
    return this.history.undoStack.length > 0;
  }

  /**
   * Check if can redo
   * @returns {boolean} True if can redo
   */
  canRedo() {
    return this.history.redoStack.length > 0;
  }

  /**
   * Undo last action
   * @returns {Object|null} Undone action or null
   */
  undo() {
    if (this.canUndo()) {
      const action = this.history.undoStack.pop();
      this.history.redoStack.push(action);
      
      this.eventBus.emit('history-changed', {
        canUndo: this.canUndo(),
        canRedo: this.canRedo()
      });
      
      return action;
    }
    return null;
  }

  /**
   * Redo last undone action
   * @returns {Object|null} Redone action or null
   */
  redo() {
    if (this.canRedo()) {
      const action = this.history.redoStack.pop();
      this.history.undoStack.push(action);
      
      this.eventBus.emit('history-changed', {
        canUndo: this.canUndo(),
        canRedo: this.canRedo()
      });
      
      return action;
    }
    return null;
  }

  /**
   * Load settings from localStorage
   */
  loadSettings() {
    try {
      const saved = localStorage.getItem('level-editor-settings');
      if (saved) {
        const settings = JSON.parse(saved);
        Object.assign(this.viewSettings, settings.viewSettings || {});
        Object.assign(this.uiState, settings.uiState || {});
      }
    } catch (error) {
      console.warn('[EditorState] Failed to load settings:', error);
    }
  }

  /**
   * Save settings to localStorage
   */
  saveSettings() {
    try {
      const settings = {
        viewSettings: this.viewSettings,
        uiState: this.uiState
      };
      localStorage.setItem('level-editor-settings', JSON.stringify(settings));
    } catch (error) {
      console.warn('[EditorState] Failed to save settings:', error);
    }
  }

  /**
   * Setup auto-save for settings
   */
  setupAutoSave() {
    // Save settings periodically
    setInterval(() => {
      this.saveSettings();
    }, 30000); // Every 30 seconds
  }

  /**
   * Destroy the state manager
   */
  destroy() {
    // Save settings one last time
    this.saveSettings();
    
    // Clear all data
    this.currentLevel = null;
    this.selectedItems = [];
    this.history.undoStack = [];
    this.history.redoStack = [];
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EditorState;
} else {
  window.EditorState = EditorState;
}
