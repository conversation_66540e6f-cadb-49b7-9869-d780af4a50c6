/**
 * Spawner Editor Component
 * Specialized editor for spawner configuration and wave management
 */
class SpawnerEditor {
  constructor(container, eventBus, editorState) {
    this.container = container;
    this.eventBus = eventBus;
    this.editorState = editorState;
    
    this.currentSpawner = null;
    this.waveTimeline = null;
    this.previewCanvas = null;
    this.previewCtx = null;
    
    this.initialize();
  }

  /**
   * Initialize the component
   */
  initialize() {
    this.setupEventHandlers();
    this.createUI();
  }

  /**
   * Setup event handlers
   */
  setupEventHandlers() {
    this.eventBus.onSelectionChanged(this.onSelectionChanged, this);
    this.eventBus.onPropertyChanged(this.onPropertyChanged, this);
  }

  /**
   * Handle selection changed
   * @param {Array} selection - Selected items
   */
  onSelectionChanged(selection) {
    const spawnerSelection = selection.find(item => item.type === 'spawner');
    if (spawnerSelection) {
      this.setSpawner(spawnerSelection.data);
    } else {
      this.setSpawner(null);
    }
  }

  /**
   * Handle property changed
   * @param {Object} changeData - Property change data
   */
  onPropertyChanged(changeData) {
    if (changeData.target === this.currentSpawner) {
      this.refreshUI();
    }
  }

  /**
   * Set current spawner
   * @param {Object} spawner - Spawner data
   */
  setSpawner(spawner) {
    this.currentSpawner = spawner;
    this.refreshUI();
  }

  /**
   * Create the UI
   */
  createUI() {
    this.container.innerHTML = `
      <div class="spawner-editor">
        <div class="spawner-header">
          <h3>Spawner Configuration</h3>
          <div class="spawner-actions">
            <button id="test-spawner" class="btn-small">Test</button>
            <button id="reset-spawner" class="btn-small">Reset</button>
          </div>
        </div>
        
        <div class="spawner-content">
          <!-- Basic Properties -->
          <div class="property-section">
            <h4>Basic Properties</h4>
            <div class="property-grid">
              <label>Name:</label>
              <input type="text" id="spawner-name" class="property-input">
              
              <label>Position:</label>
              <div class="vector3-input" id="spawner-position"></div>
              
              <label>Active:</label>
              <input type="checkbox" id="spawner-active" class="property-checkbox">
              
              <label>Prefab Path:</label>
              <div class="file-input">
                <input type="text" id="spawner-prefab" class="property-input" placeholder="Select prefab...">
                <button id="browse-prefab" class="browse-btn">...</button>
              </div>
            </div>
          </div>
          
          <!-- Spawn Pattern -->
          <div class="property-section">
            <h4>Spawn Pattern</h4>
            <div class="property-grid">
              <label>Type:</label>
              <select id="spawn-type" class="property-select">
                <option value="sequential">Sequential</option>
                <option value="random">Random</option>
                <option value="static">Static</option>
                <option value="wave">Wave</option>
              </select>
              
              <label>Count:</label>
              <input type="number" id="spawn-count" class="property-input" min="1" value="1">
              
              <label>Interval (s):</label>
              <input type="number" id="spawn-interval" class="property-input" min="0" step="0.1" value="1.0">
              
              <label>Delay (s):</label>
              <input type="number" id="spawn-delay" class="property-input" min="0" step="0.1" value="0">
            </div>
          </div>
          
          <!-- Wave Configuration -->
          <div class="property-section" id="wave-section" style="display: none;">
            <h4>Wave Configuration</h4>
            <div class="wave-timeline" id="wave-timeline">
              <div class="timeline-header">
                <span>Wave Timeline</span>
                <button id="add-wave" class="btn-small">Add Wave</button>
              </div>
              <div class="timeline-content" id="timeline-content"></div>
            </div>
          </div>
          
          <!-- Spawn Area -->
          <div class="property-section">
            <h4>Spawn Area</h4>
            <div class="property-grid">
              <label>Area Type:</label>
              <select id="area-type" class="property-select">
                <option value="point">Point</option>
                <option value="circle">Circle</option>
                <option value="rectangle">Rectangle</option>
                <option value="line">Line</option>
              </select>
              
              <label>Radius/Size:</label>
              <input type="number" id="area-size" class="property-input" min="0" value="50">
              
              <label>Rotation:</label>
              <input type="number" id="area-rotation" class="property-input" min="0" max="360" value="0">
            </div>
          </div>
          
          <!-- Preview -->
          <div class="property-section">
            <h4>Preview</h4>
            <div class="spawner-preview">
              <canvas id="spawner-preview-canvas" width="200" height="150"></canvas>
              <div class="preview-controls">
                <button id="play-preview" class="btn-small">Play</button>
                <button id="stop-preview" class="btn-small">Stop</button>
                <span id="preview-time">0.0s</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="spawner-footer">
          <button id="apply-spawner" class="btn-primary">Apply Changes</button>
          <button id="revert-spawner" class="btn-secondary">Revert</button>
        </div>
      </div>
    `;
    
    this.setupUIEventHandlers();
    this.createPreviewCanvas();
  }

  /**
   * Setup UI event handlers
   */
  setupUIEventHandlers() {
    // Basic properties
    document.getElementById('spawner-name').addEventListener('input', this.onNameChanged.bind(this));
    document.getElementById('spawner-active').addEventListener('change', this.onActiveChanged.bind(this));
    document.getElementById('spawner-prefab').addEventListener('input', this.onPrefabChanged.bind(this));
    document.getElementById('browse-prefab').addEventListener('click', this.onBrowsePrefab.bind(this));
    
    // Spawn pattern
    document.getElementById('spawn-type').addEventListener('change', this.onSpawnTypeChanged.bind(this));
    document.getElementById('spawn-count').addEventListener('input', this.onSpawnCountChanged.bind(this));
    document.getElementById('spawn-interval').addEventListener('input', this.onSpawnIntervalChanged.bind(this));
    document.getElementById('spawn-delay').addEventListener('input', this.onSpawnDelayChanged.bind(this));
    
    // Spawn area
    document.getElementById('area-type').addEventListener('change', this.onAreaTypeChanged.bind(this));
    document.getElementById('area-size').addEventListener('input', this.onAreaSizeChanged.bind(this));
    document.getElementById('area-rotation').addEventListener('input', this.onAreaRotationChanged.bind(this));
    
    // Wave timeline
    document.getElementById('add-wave').addEventListener('click', this.onAddWave.bind(this));
    
    // Preview controls
    document.getElementById('play-preview').addEventListener('click', this.onPlayPreview.bind(this));
    document.getElementById('stop-preview').addEventListener('click', this.onStopPreview.bind(this));
    
    // Actions
    document.getElementById('test-spawner').addEventListener('click', this.onTestSpawner.bind(this));
    document.getElementById('reset-spawner').addEventListener('click', this.onResetSpawner.bind(this));
    document.getElementById('apply-spawner').addEventListener('click', this.onApplyChanges.bind(this));
    document.getElementById('revert-spawner').addEventListener('click', this.onRevertChanges.bind(this));
  }

  /**
   * Create preview canvas
   */
  createPreviewCanvas() {
    this.previewCanvas = document.getElementById('spawner-preview-canvas');
    this.previewCtx = this.previewCanvas.getContext('2d');
    this.renderPreview();
  }

  /**
   * Refresh UI with current spawner data
   */
  refreshUI() {
    if (!this.currentSpawner) {
      this.container.style.display = 'none';
      return;
    }
    
    this.container.style.display = 'block';
    
    // Update basic properties
    document.getElementById('spawner-name').value = this.currentSpawner.name || '';
    document.getElementById('spawner-active').checked = this.currentSpawner.isActive || false;
    document.getElementById('spawner-prefab').value = this.currentSpawner.prefabPath || '';
    
    // Update position
    this.updateVector3Input('spawner-position', this.currentSpawner.position);
    
    // Update spawn pattern
    const spawnPattern = this.currentSpawner.spawnPattern || {};
    document.getElementById('spawn-type').value = spawnPattern.type || 'sequential';
    document.getElementById('spawn-count').value = spawnPattern.count || 1;
    document.getElementById('spawn-interval').value = spawnPattern.interval || 1.0;
    document.getElementById('spawn-delay').value = spawnPattern.delay || 0;
    
    // Update spawn area
    const spawnArea = this.currentSpawner.spawnArea || {};
    document.getElementById('area-type').value = spawnArea.type || 'point';
    document.getElementById('area-size').value = spawnArea.size || 50;
    document.getElementById('area-rotation').value = spawnArea.rotation || 0;
    
    // Show/hide wave section
    const waveSection = document.getElementById('wave-section');
    waveSection.style.display = spawnPattern.type === 'wave' ? 'block' : 'none';
    
    if (spawnPattern.type === 'wave') {
      this.refreshWaveTimeline();
    }
    
    this.renderPreview();
  }

  /**
   * Update Vector3 input
   * @param {string} containerId - Container ID
   * @param {Object} value - Vector3 value
   */
  updateVector3Input(containerId, value) {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    const vector = value || { x: 0, y: 0, z: 0 };
    container.innerHTML = `
      <div class="vector-component">
        <label>X:</label>
        <input type="number" step="any" value="${vector.x}" data-component="x">
      </div>
      <div class="vector-component">
        <label>Y:</label>
        <input type="number" step="any" value="${vector.y}" data-component="y">
      </div>
      <div class="vector-component">
        <label>Z:</label>
        <input type="number" step="any" value="${vector.z}" data-component="z">
      </div>
    `;
    
    // Add event listeners
    container.querySelectorAll('input').forEach(input => {
      input.addEventListener('input', () => {
        const component = input.dataset.component;
        const newValue = parseFloat(input.value) || 0;
        this.currentSpawner.position[component] = newValue;
        this.editorState.markModified();
        this.renderPreview();
      });
    });
  }

  /**
   * Refresh wave timeline
   */
  refreshWaveTimeline() {
    const timelineContent = document.getElementById('timeline-content');
    if (!timelineContent) return;
    
    const waves = this.currentSpawner.spawnPattern.waves || [];
    
    timelineContent.innerHTML = '';
    waves.forEach((wave, index) => {
      const waveElement = this.createWaveElement(wave, index);
      timelineContent.appendChild(waveElement);
    });
  }

  /**
   * Create wave element
   * @param {Object} wave - Wave data
   * @param {number} index - Wave index
   * @returns {HTMLElement} Wave element
   */
  createWaveElement(wave, index) {
    const waveDiv = document.createElement('div');
    waveDiv.className = 'wave-item';
    waveDiv.innerHTML = `
      <div class="wave-header">
        <span>Wave ${index + 1}</span>
        <button class="remove-wave" data-index="${index}">×</button>
      </div>
      <div class="wave-properties">
        <label>Start Time:</label>
        <input type="number" step="0.1" value="${wave.startTime || 0}" data-prop="startTime" data-index="${index}">
        <label>Count:</label>
        <input type="number" min="1" value="${wave.count || 1}" data-prop="count" data-index="${index}">
        <label>Interval:</label>
        <input type="number" step="0.1" value="${wave.interval || 1}" data-prop="interval" data-index="${index}">
      </div>
    `;
    
    // Add event listeners
    waveDiv.querySelector('.remove-wave').addEventListener('click', () => {
      this.removeWave(index);
    });
    
    waveDiv.querySelectorAll('input').forEach(input => {
      input.addEventListener('input', () => {
        const prop = input.dataset.prop;
        const waveIndex = parseInt(input.dataset.index);
        const value = parseFloat(input.value) || 0;
        
        if (!this.currentSpawner.spawnPattern.waves) {
          this.currentSpawner.spawnPattern.waves = [];
        }
        
        this.currentSpawner.spawnPattern.waves[waveIndex][prop] = value;
        this.editorState.markModified();
      });
    });
    
    return waveDiv;
  }

  /**
   * Render preview
   */
  renderPreview() {
    if (!this.previewCtx || !this.currentSpawner) return;
    
    const canvas = this.previewCanvas;
    const ctx = this.previewCtx;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw background
    ctx.fillStyle = '#1e1e1e';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Draw spawn area
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const spawnArea = this.currentSpawner.spawnArea || {};
    
    ctx.strokeStyle = '#007acc';
    ctx.lineWidth = 2;
    
    switch (spawnArea.type) {
      case 'circle':
        ctx.beginPath();
        ctx.arc(centerX, centerY, (spawnArea.size || 50) / 2, 0, Math.PI * 2);
        ctx.stroke();
        break;
      case 'rectangle':
        const size = spawnArea.size || 50;
        ctx.strokeRect(centerX - size/2, centerY - size/2, size, size);
        break;
      case 'line':
        const lineLength = spawnArea.size || 50;
        ctx.beginPath();
        ctx.moveTo(centerX - lineLength/2, centerY);
        ctx.lineTo(centerX + lineLength/2, centerY);
        ctx.stroke();
        break;
      default: // point
        ctx.beginPath();
        ctx.arc(centerX, centerY, 3, 0, Math.PI * 2);
        ctx.fill();
    }
    
    // Draw spawner icon
    ctx.fillStyle = this.currentSpawner.isActive ? '#ff6b35' : '#666666';
    ctx.beginPath();
    ctx.arc(centerX, centerY, 8, 0, Math.PI * 2);
    ctx.fill();
  }

  // Event handlers
  onNameChanged(event) {
    if (this.currentSpawner) {
      this.currentSpawner.name = event.target.value;
      this.editorState.markModified();
    }
  }

  onActiveChanged(event) {
    if (this.currentSpawner) {
      this.currentSpawner.isActive = event.target.checked;
      this.editorState.markModified();
      this.renderPreview();
    }
  }

  onPrefabChanged(event) {
    if (this.currentSpawner) {
      this.currentSpawner.prefabPath = event.target.value;
      this.editorState.markModified();
    }
  }

  onBrowsePrefab() {
    this.eventBus.emit('open-file-browser', {
      title: 'Select Prefab',
      filters: [{ name: 'Prefab Files', extensions: ['prefab'] }],
      callback: (selectedPath) => {
        if (selectedPath && this.currentSpawner) {
          this.currentSpawner.prefabPath = selectedPath;
          document.getElementById('spawner-prefab').value = selectedPath;
          this.editorState.markModified();
        }
      }
    });
  }

  onSpawnTypeChanged(event) {
    if (this.currentSpawner) {
      if (!this.currentSpawner.spawnPattern) {
        this.currentSpawner.spawnPattern = {};
      }
      this.currentSpawner.spawnPattern.type = event.target.value;
      this.editorState.markModified();
      this.refreshUI();
    }
  }

  onSpawnCountChanged(event) {
    if (this.currentSpawner) {
      if (!this.currentSpawner.spawnPattern) {
        this.currentSpawner.spawnPattern = {};
      }
      this.currentSpawner.spawnPattern.count = parseInt(event.target.value) || 1;
      this.editorState.markModified();
    }
  }

  onSpawnIntervalChanged(event) {
    if (this.currentSpawner) {
      if (!this.currentSpawner.spawnPattern) {
        this.currentSpawner.spawnPattern = {};
      }
      this.currentSpawner.spawnPattern.interval = parseFloat(event.target.value) || 1.0;
      this.editorState.markModified();
    }
  }

  onSpawnDelayChanged(event) {
    if (this.currentSpawner) {
      if (!this.currentSpawner.spawnPattern) {
        this.currentSpawner.spawnPattern = {};
      }
      this.currentSpawner.spawnPattern.delay = parseFloat(event.target.value) || 0;
      this.editorState.markModified();
    }
  }

  onAreaTypeChanged(event) {
    if (this.currentSpawner) {
      if (!this.currentSpawner.spawnArea) {
        this.currentSpawner.spawnArea = {};
      }
      this.currentSpawner.spawnArea.type = event.target.value;
      this.editorState.markModified();
      this.renderPreview();
    }
  }

  onAreaSizeChanged(event) {
    if (this.currentSpawner) {
      if (!this.currentSpawner.spawnArea) {
        this.currentSpawner.spawnArea = {};
      }
      this.currentSpawner.spawnArea.size = parseFloat(event.target.value) || 50;
      this.editorState.markModified();
      this.renderPreview();
    }
  }

  onAreaRotationChanged(event) {
    if (this.currentSpawner) {
      if (!this.currentSpawner.spawnArea) {
        this.currentSpawner.spawnArea = {};
      }
      this.currentSpawner.spawnArea.rotation = parseFloat(event.target.value) || 0;
      this.editorState.markModified();
      this.renderPreview();
    }
  }

  onAddWave() {
    if (this.currentSpawner) {
      if (!this.currentSpawner.spawnPattern) {
        this.currentSpawner.spawnPattern = {};
      }
      if (!this.currentSpawner.spawnPattern.waves) {
        this.currentSpawner.spawnPattern.waves = [];
      }
      
      this.currentSpawner.spawnPattern.waves.push({
        startTime: 0,
        count: 1,
        interval: 1.0
      });
      
      this.editorState.markModified();
      this.refreshWaveTimeline();
    }
  }

  removeWave(index) {
    if (this.currentSpawner && this.currentSpawner.spawnPattern && this.currentSpawner.spawnPattern.waves) {
      this.currentSpawner.spawnPattern.waves.splice(index, 1);
      this.editorState.markModified();
      this.refreshWaveTimeline();
    }
  }

  onPlayPreview() {
    // Implementation for preview playback
    console.log('[SpawnerEditor] Play preview not yet implemented');
  }

  onStopPreview() {
    // Implementation for stopping preview
    console.log('[SpawnerEditor] Stop preview not yet implemented');
  }

  onTestSpawner() {
    // Implementation for testing spawner in scene
    console.log('[SpawnerEditor] Test spawner not yet implemented');
  }

  onResetSpawner() {
    if (this.currentSpawner && confirm('Reset spawner to default values?')) {
      // Reset to default values
      Object.assign(this.currentSpawner, {
        spawnPattern: { type: 'sequential', count: 1, interval: 1.0, delay: 0 },
        spawnArea: { type: 'point', size: 50, rotation: 0 }
      });
      this.editorState.markModified();
      this.refreshUI();
    }
  }

  onApplyChanges() {
    // Apply changes and emit events
    this.eventBus.emitPropertyChanged('spawner', this.currentSpawner, this.currentSpawner);
    console.log('[SpawnerEditor] Changes applied');
  }

  onRevertChanges() {
    // Revert changes
    this.refreshUI();
    console.log('[SpawnerEditor] Changes reverted');
  }

  /**
   * Destroy the component
   */
  destroy() {
    this.eventBus.off('selection-changed', this.onSelectionChanged, this);
    this.eventBus.off('property-changed', this.onPropertyChanged, this);
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = SpawnerEditor;
} else {
  window.SpawnerEditor = SpawnerEditor;
}
