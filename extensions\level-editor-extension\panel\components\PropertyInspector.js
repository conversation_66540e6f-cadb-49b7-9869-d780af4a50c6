/**
 * Property Inspector Component
 * Dynamic property editing panel with validation and custom editors
 */
class PropertyInspector {
  constructor(container, eventBus, editorState) {
    this.container = container;
    this.eventBus = eventBus;
    this.editorState = editorState;
    
    this.currentTarget = null;
    this.currentTargetType = null;
    this.propertyGroups = new Map();
    this.validators = new Map();
    this.customEditors = new Map();
    
    this.initialize();
  }

  /**
   * Initialize the component
   */
  initialize() {
    this.setupEventHandlers();
    this.setupValidators();
    this.setupCustomEditors();
    this.render();
  }

  /**
   * Setup event handlers
   */
  setupEventHandlers() {
    // Listen for selection changes
    this.eventBus.onSelectionChanged(this.onSelectionChanged, this);
    
    // Listen for property changes from other sources
    this.eventBus.onPropertyChanged(this.onExternalPropertyChanged, this);
  }

  /**
   * Setup property validators
   */
  setupValidators() {
    // String validators
    this.validators.set('required', (value) => {
      return value && value.trim().length > 0 ? null : 'This field is required';
    });
    
    this.validators.set('minLength', (value, min) => {
      return value && value.length >= min ? null : `Minimum length is ${min}`;
    });
    
    this.validators.set('maxLength', (value, max) => {
      return value && value.length <= max ? null : `Maximum length is ${max}`;
    });
    
    // Number validators
    this.validators.set('number', (value) => {
      const num = parseFloat(value);
      return !isNaN(num) ? null : 'Please enter a valid number';
    });
    
    this.validators.set('integer', (value) => {
      const num = parseInt(value);
      return Number.isInteger(num) ? null : 'Please enter a valid integer';
    });
    
    this.validators.set('min', (value, min) => {
      const num = parseFloat(value);
      return num >= min ? null : `Value must be at least ${min}`;
    });
    
    this.validators.set('max', (value, max) => {
      const num = parseFloat(value);
      return num <= max ? null : `Value must be at most ${max}`;
    });
    
    this.validators.set('range', (value, min, max) => {
      const num = parseFloat(value);
      return num >= min && num <= max ? null : `Value must be between ${min} and ${max}`;
    });
    
    // File path validators
    this.validators.set('filePath', (value) => {
      return value && value.includes('.') ? null : 'Please enter a valid file path';
    });
    
    // ID validators
    this.validators.set('uniqueId', (value, existingIds) => {
      return !existingIds.includes(value) ? null : 'This ID already exists';
    });
  }

  /**
   * Setup custom property editors
   */
  setupCustomEditors() {
    // Vector3 editor
    this.customEditors.set('vector3', this.createVector3Editor.bind(this));
    
    // Vector2 editor
    this.customEditors.set('vector2', this.createVector2Editor.bind(this));
    
    // Rect editor
    this.customEditors.set('rect', this.createRectEditor.bind(this));
    
    // Color editor
    this.customEditors.set('color', this.createColorEditor.bind(this));
    
    // File path editor
    this.customEditors.set('filePath', this.createFilePathEditor.bind(this));
    
    // Enum editor
    this.customEditors.set('enum', this.createEnumEditor.bind(this));
    
    // Boolean editor
    this.customEditors.set('boolean', this.createBooleanEditor.bind(this));
    
    // Array editor
    this.customEditors.set('array', this.createArrayEditor.bind(this));
  }

  /**
   * Handle selection changed
   * @param {Array} selection - Selected items
   */
  onSelectionChanged(selection) {
    if (selection.length === 0) {
      this.setTarget(null, null);
    } else if (selection.length === 1) {
      const item = selection[0];
      this.setTarget(item.data, item.type);
    } else {
      // Multiple selection - show common properties
      this.setMultipleTargets(selection);
    }
  }

  /**
   * Handle external property changes
   * @param {Object} changeData - Property change data
   */
  onExternalPropertyChanged(changeData) {
    if (changeData.target === this.currentTarget) {
      this.refreshProperty(changeData.propertyName);
    }
  }

  /**
   * Set the target object to inspect
   * @param {Object} target - Target object
   * @param {string} targetType - Type of target
   */
  setTarget(target, targetType) {
    this.currentTarget = target;
    this.currentTargetType = targetType;
    this.render();
  }

  /**
   * Set multiple targets for batch editing
   * @param {Array} targets - Array of target objects
   */
  setMultipleTargets(targets) {
    this.currentTarget = targets;
    this.currentTargetType = 'multiple';
    this.render();
  }

  /**
   * Render the property inspector
   */
  render() {
    this.container.innerHTML = '';
    
    if (!this.currentTarget) {
      this.renderEmptyState();
      return;
    }
    
    if (this.currentTargetType === 'multiple') {
      this.renderMultipleSelection();
      return;
    }
    
    this.renderSingleTarget();
  }

  /**
   * Render empty state
   */
  renderEmptyState() {
    const emptyDiv = document.createElement('div');
    emptyDiv.className = 'no-selection-message';
    emptyDiv.innerHTML = `
      <p>No item selected.</p>
      <p>Select an item to view and edit its properties.</p>
    `;
    this.container.appendChild(emptyDiv);
  }

  /**
   * Render multiple selection
   */
  renderMultipleSelection() {
    const multiDiv = document.createElement('div');
    multiDiv.className = 'multiple-selection-message';
    multiDiv.innerHTML = `
      <p>Multiple items selected (${this.currentTarget.length}).</p>
      <p>Common properties will be shown here.</p>
    `;
    this.container.appendChild(multiDiv);
    
    // TODO: Implement common properties editing
  }

  /**
   * Render single target properties
   */
  renderSingleTarget() {
    const schema = this.getPropertySchema(this.currentTargetType);
    if (!schema) {
      this.renderUnsupportedType();
      return;
    }
    
    // Create property groups
    schema.groups.forEach(group => {
      const groupElement = this.createPropertyGroup(group);
      this.container.appendChild(groupElement);
    });
  }

  /**
   * Render unsupported type message
   */
  renderUnsupportedType() {
    const unsupportedDiv = document.createElement('div');
    unsupportedDiv.className = 'unsupported-type-message';
    unsupportedDiv.innerHTML = `
      <p>Properties for "${this.currentTargetType}" are not yet supported.</p>
    `;
    this.container.appendChild(unsupportedDiv);
  }

  /**
   * Get property schema for target type
   * @param {string} targetType - Type of target
   * @returns {Object|null} Property schema
   */
  getPropertySchema(targetType) {
    const schemas = {
      level: {
        groups: [
          {
            name: 'Metadata',
            properties: [
              { name: 'name', label: 'Name', type: 'string', path: 'metadata.name', validators: ['required'] },
              { name: 'description', label: 'Description', type: 'textarea', path: 'metadata.description' },
              { name: 'author', label: 'Author', type: 'string', path: 'metadata.author' },
              { name: 'version', label: 'Version', type: 'string', path: 'metadata.version', validators: ['required'] },
              { name: 'duration', label: 'Duration (s)', type: 'number', path: 'metadata.duration', validators: ['number', 'min:1'] },
              { name: 'difficulty', label: 'Difficulty', type: 'number', path: 'metadata.difficulty', validators: ['integer', 'range:1,10'] }
            ]
          }
        ]
      },
      
      metadata: {
        groups: [
          {
            name: 'Level Information',
            properties: [
              { name: 'name', label: 'Name', type: 'string', path: 'name', validators: ['required'] },
              { name: 'description', label: 'Description', type: 'textarea', path: 'description' },
              { name: 'author', label: 'Author', type: 'string', path: 'author' },
              { name: 'version', label: 'Version', type: 'string', path: 'version', validators: ['required'] },
              { name: 'duration', label: 'Duration (s)', type: 'number', path: 'duration', validators: ['number', 'min:1'] },
              { name: 'difficulty', label: 'Difficulty', type: 'number', path: 'difficulty', validators: ['integer', 'range:1,10'] }
            ]
          },
          {
            name: 'Timestamps',
            properties: [
              { name: 'createdAt', label: 'Created', type: 'datetime', path: 'createdAt', readonly: true },
              { name: 'modifiedAt', label: 'Modified', type: 'datetime', path: 'modifiedAt', readonly: true }
            ]
          }
        ]
      },
      
      camera: {
        groups: [
          {
            name: 'Camera Settings',
            properties: [
              { name: 'viewportSize', label: 'Viewport Size', type: 'vector2', path: 'viewportSize' },
              { name: 'scrollSpeed', label: 'Scroll Speed', type: 'number', path: 'scrollSpeed', validators: ['number', 'min:0'] },
              { name: 'followTarget', label: 'Follow Target', type: 'string', path: 'followTarget' },
              { name: 'smoothing', label: 'Smoothing', type: 'number', path: 'smoothing', validators: ['number', 'min:0'] }
            ]
          }
        ]
      },
      
      sublevel: {
        groups: [
          {
            name: 'Basic Properties',
            properties: [
              { name: 'name', label: 'Name', type: 'string', path: 'name', validators: ['required'] },
              { name: 'bounds', label: 'Bounds', type: 'rect', path: 'bounds' },
              { name: 'isActive', label: 'Active', type: 'boolean', path: 'isActive' },
              { name: 'loadPriority', label: 'Load Priority', type: 'number', path: 'loadPriority', validators: ['integer', 'min:0'] }
            ]
          },
          {
            name: 'Entry/Exit Points',
            properties: [
              { name: 'entryPoint', label: 'Entry Point', type: 'vector3', path: 'entryPoint.position' },
              { name: 'exitPoint', label: 'Exit Point', type: 'vector3', path: 'exitPoint.position' }
            ]
          }
        ]
      },
      
      spawner: {
        groups: [
          {
            name: 'Basic Properties',
            properties: [
              { name: 'name', label: 'Name', type: 'string', path: 'name', validators: ['required'] },
              { name: 'position', label: 'Position', type: 'vector3', path: 'position' },
              { name: 'prefabPath', label: 'Prefab Path', type: 'filePath', path: 'prefabPath', validators: ['required', 'filePath'] },
              { name: 'isActive', label: 'Active', type: 'boolean', path: 'isActive' }
            ]
          },
          {
            name: 'Spawn Pattern',
            properties: [
              { name: 'spawnType', label: 'Spawn Type', type: 'enum', path: 'spawnPattern.type', 
                options: ['sequential', 'random', 'static'] },
              { name: 'count', label: 'Count', type: 'number', path: 'spawnPattern.count', validators: ['integer', 'min:1'] },
              { name: 'interval', label: 'Interval', type: 'number', path: 'spawnPattern.interval', validators: ['number', 'min:0'] },
              { name: 'delay', label: 'Delay', type: 'number', path: 'spawnPattern.delay', validators: ['number', 'min:0'] }
            ]
          }
        ]
      },
      
      path: {
        groups: [
          {
            name: 'Basic Properties',
            properties: [
              { name: 'name', label: 'Name', type: 'string', path: 'name', validators: ['required'] },
              { name: 'isLoop', label: 'Is Loop', type: 'boolean', path: 'isLoop' },
              { name: 'totalLength', label: 'Total Length', type: 'number', path: 'totalLength', readonly: true }
            ]
          },
          {
            name: 'Path Points',
            properties: [
              { name: 'points', label: 'Points', type: 'array', path: 'points', itemType: 'pathPoint' }
            ]
          }
        ]
      },
      
      map: {
        groups: [
          {
            name: 'Basic Properties',
            properties: [
              { name: 'name', label: 'Name', type: 'string', path: 'editorData.name', validators: ['required'] },
              { name: 'prefabPath', label: 'Prefab Path', type: 'filePath', path: 'prefabPath', validators: ['required', 'filePath'] },
              { name: 'isVisible', label: 'Visible', type: 'boolean', path: 'isVisible' }
            ]
          },
          {
            name: 'Layer Settings',
            properties: [
              { name: 'layerType', label: 'Layer Type', type: 'enum', path: 'layerType',
                options: ['BG_VeryFar', 'BG_Far', 'BG_Mid', 'BG_Close', 'BG_VeryClose', 'Player', 'FG_VeryClose', 'FG_Close'] },
              { name: 'depth', label: 'Depth', type: 'number', path: 'depth', validators: ['integer'] },
              { name: 'scrollSpeed', label: 'Scroll Speed', type: 'vector2', path: 'scrollSpeed' },
              { name: 'repeatMode', label: 'Repeat Mode', type: 'enum', path: 'repeatMode',
                options: ['none', 'vertical', 'horizontal', 'both'] },
              { name: 'offset', label: 'Offset', type: 'vector2', path: 'offset' },
              { name: 'bounds', label: 'Bounds', type: 'rect', path: 'bounds' }
            ]
          }
        ]
      }
    };
    
    return schemas[targetType] || null;
  }

  /**
   * Create a property group
   * @param {Object} groupData - Group data
   * @returns {HTMLElement} Group element
   */
  createPropertyGroup(groupData) {
    const groupElement = document.createElement('div');
    groupElement.className = 'property-group';
    
    // Group header
    const header = document.createElement('div');
    header.className = 'property-group-header';
    header.textContent = groupData.name;
    header.addEventListener('click', () => this.toggleGroup(groupData.name));
    
    // Group content
    const content = document.createElement('div');
    content.className = 'property-group-content';
    
    // Add properties
    groupData.properties.forEach(property => {
      const propertyElement = this.createPropertyEditor(property);
      content.appendChild(propertyElement);
    });
    
    groupElement.appendChild(header);
    groupElement.appendChild(content);
    
    // Check if group should be expanded
    const isExpanded = this.editorState.getUIState().propertyGroupsExpanded[groupData.name] !== false;
    if (!isExpanded) {
      content.style.display = 'none';
      groupElement.classList.add('collapsed');
    }
    
    return groupElement;
  }

  /**
   * Toggle property group expansion
   * @param {string} groupName - Group name
   */
  toggleGroup(groupName) {
    const uiState = this.editorState.getUIState();
    const isExpanded = uiState.propertyGroupsExpanded[groupName] !== false;
    
    uiState.propertyGroupsExpanded[groupName] = !isExpanded;
    this.editorState.updateUIState(uiState);
    
    this.render();
  }

  /**
   * Create a property editor
   * @param {Object} property - Property definition
   * @returns {HTMLElement} Property editor element
   */
  createPropertyEditor(property) {
    const row = document.createElement('div');
    row.className = 'property-row';
    row.dataset.propertyName = property.name;
    
    // Label
    const label = document.createElement('div');
    label.className = 'property-label';
    label.textContent = property.label;
    label.title = property.description || property.label;
    
    // Editor container
    const editorContainer = document.createElement('div');
    editorContainer.className = 'property-editor';
    
    // Create appropriate editor
    let editor;
    if (this.customEditors.has(property.type)) {
      editor = this.customEditors.get(property.type)(property);
    } else {
      editor = this.createDefaultEditor(property);
    }
    
    editorContainer.appendChild(editor);
    
    row.appendChild(label);
    row.appendChild(editorContainer);
    
    return row;
  }

  /**
   * Create default property editor
   * @param {Object} property - Property definition
   * @returns {HTMLElement} Editor element
   */
  createDefaultEditor(property) {
    const value = this.getPropertyValue(property.path);
    
    let input;
    
    switch (property.type) {
      case 'textarea':
        input = document.createElement('textarea');
        input.rows = 3;
        break;
      case 'number':
        input = document.createElement('input');
        input.type = 'number';
        break;
      case 'datetime':
        input = document.createElement('input');
        input.type = 'datetime-local';
        if (value) {
          input.value = new Date(value).toISOString().slice(0, 16);
        }
        break;
      default:
        input = document.createElement('input');
        input.type = 'text';
    }
    
    input.className = 'property-input';
    input.value = value || '';
    input.disabled = property.readonly || false;
    
    // Add event listeners
    input.addEventListener('input', (e) => this.onPropertyInput(property, e.target.value));
    input.addEventListener('blur', (e) => this.onPropertyBlur(property, e.target.value));
    
    return input;
  }

  /**
   * Create Vector3 editor
   * @param {Object} property - Property definition
   * @returns {HTMLElement} Vector3 editor
   */
  createVector3Editor(property) {
    const value = this.getPropertyValue(property.path) || { x: 0, y: 0, z: 0 };

    const container = document.createElement('div');
    container.className = 'vector3-editor';

    ['x', 'y', 'z'].forEach(component => {
      const wrapper = document.createElement('div');
      wrapper.className = 'vector-component';

      const label = document.createElement('span');
      label.textContent = component.toUpperCase();
      label.className = 'vector-label';

      const input = document.createElement('input');
      input.type = 'number';
      input.step = 'any';
      input.value = value[component] || 0;
      input.className = 'vector-input';

      input.addEventListener('input', () => {
        const newValue = { ...value };
        newValue[component] = parseFloat(input.value) || 0;
        this.onPropertyInput(property, newValue);
      });

      wrapper.appendChild(label);
      wrapper.appendChild(input);
      container.appendChild(wrapper);
    });

    return container;
  }

  /**
   * Create Vector2 editor
   * @param {Object} property - Property definition
   * @returns {HTMLElement} Vector2 editor
   */
  createVector2Editor(property) {
    const value = this.getPropertyValue(property.path) || { x: 0, y: 0 };

    const container = document.createElement('div');
    container.className = 'vector2-editor';

    ['x', 'y'].forEach(component => {
      const wrapper = document.createElement('div');
      wrapper.className = 'vector-component';

      const label = document.createElement('span');
      label.textContent = component.toUpperCase();
      label.className = 'vector-label';

      const input = document.createElement('input');
      input.type = 'number';
      input.step = 'any';
      input.value = value[component] || 0;
      input.className = 'vector-input';

      input.addEventListener('input', () => {
        const newValue = { ...value };
        newValue[component] = parseFloat(input.value) || 0;
        this.onPropertyInput(property, newValue);
      });

      wrapper.appendChild(label);
      wrapper.appendChild(input);
      container.appendChild(wrapper);
    });

    return container;
  }

  /**
   * Create Rect editor
   * @param {Object} property - Property definition
   * @returns {HTMLElement} Rect editor
   */
  createRectEditor(property) {
    const value = this.getPropertyValue(property.path) || { x: 0, y: 0, width: 0, height: 0 };

    const container = document.createElement('div');
    container.className = 'rect-editor';

    ['x', 'y', 'width', 'height'].forEach(component => {
      const wrapper = document.createElement('div');
      wrapper.className = 'rect-component';

      const label = document.createElement('span');
      label.textContent = component.charAt(0).toUpperCase() + component.slice(1);
      label.className = 'rect-label';

      const input = document.createElement('input');
      input.type = 'number';
      input.step = 'any';
      input.value = value[component] || 0;
      input.className = 'rect-input';

      input.addEventListener('input', () => {
        const newValue = { ...value };
        newValue[component] = parseFloat(input.value) || 0;
        this.onPropertyInput(property, newValue);
      });

      wrapper.appendChild(label);
      wrapper.appendChild(input);
      container.appendChild(wrapper);
    });

    return container;
  }

  /**
   * Create Color editor
   * @param {Object} property - Property definition
   * @returns {HTMLElement} Color editor
   */
  createColorEditor(property) {
    const value = this.getPropertyValue(property.path) || { r: 255, g: 255, b: 255, a: 255 };

    const container = document.createElement('div');
    container.className = 'color-editor';

    // Color picker
    const colorPicker = document.createElement('input');
    colorPicker.type = 'color';
    colorPicker.className = 'color-picker';

    // Convert to hex
    const toHex = (r, g, b) => {
      return '#' + [r, g, b].map(x => {
        const hex = Math.round(x).toString(16);
        return hex.length === 1 ? '0' + hex : hex;
      }).join('');
    };

    colorPicker.value = toHex(value.r, value.g, value.b);

    colorPicker.addEventListener('input', () => {
      const hex = colorPicker.value;
      const r = parseInt(hex.slice(1, 3), 16);
      const g = parseInt(hex.slice(3, 5), 16);
      const b = parseInt(hex.slice(5, 7), 16);

      const newValue = { ...value, r, g, b };
      this.onPropertyInput(property, newValue);
    });

    // Alpha input
    const alphaWrapper = document.createElement('div');
    alphaWrapper.className = 'alpha-wrapper';

    const alphaLabel = document.createElement('span');
    alphaLabel.textContent = 'A';
    alphaLabel.className = 'alpha-label';

    const alphaInput = document.createElement('input');
    alphaInput.type = 'number';
    alphaInput.min = 0;
    alphaInput.max = 255;
    alphaInput.value = value.a || 255;
    alphaInput.className = 'alpha-input';

    alphaInput.addEventListener('input', () => {
      const newValue = { ...value };
      newValue.a = parseInt(alphaInput.value) || 255;
      this.onPropertyInput(property, newValue);
    });

    alphaWrapper.appendChild(alphaLabel);
    alphaWrapper.appendChild(alphaInput);

    container.appendChild(colorPicker);
    container.appendChild(alphaWrapper);

    return container;
  }

  /**
   * Create File Path editor
   * @param {Object} property - Property definition
   * @returns {HTMLElement} File path editor
   */
  createFilePathEditor(property) {
    const value = this.getPropertyValue(property.path) || '';

    const container = document.createElement('div');
    container.className = 'filepath-editor';

    const input = document.createElement('input');
    input.type = 'text';
    input.value = value;
    input.className = 'filepath-input';
    input.placeholder = 'Enter file path...';

    const browseBtn = document.createElement('button');
    browseBtn.textContent = '...';
    browseBtn.className = 'browse-btn';
    browseBtn.title = 'Browse for file';

    input.addEventListener('input', () => {
      this.onPropertyInput(property, input.value);
    });

    browseBtn.addEventListener('click', () => {
      this.openFileBrowser(property, input);
    });

    container.appendChild(input);
    container.appendChild(browseBtn);

    return container;
  }

  /**
   * Create Enum editor
   * @param {Object} property - Property definition
   * @returns {HTMLElement} Enum editor
   */
  createEnumEditor(property) {
    const value = this.getPropertyValue(property.path) || '';

    const select = document.createElement('select');
    select.className = 'enum-select';

    // Add options
    property.options.forEach(option => {
      const optionElement = document.createElement('option');
      optionElement.value = option;
      optionElement.textContent = option;
      optionElement.selected = option === value;
      select.appendChild(optionElement);
    });

    select.addEventListener('change', () => {
      this.onPropertyInput(property, select.value);
    });

    return select;
  }

  /**
   * Create Boolean editor
   * @param {Object} property - Property definition
   * @returns {HTMLElement} Boolean editor
   */
  createBooleanEditor(property) {
    const value = this.getPropertyValue(property.path) || false;

    const container = document.createElement('div');
    container.className = 'boolean-editor';

    const checkbox = document.createElement('input');
    checkbox.type = 'checkbox';
    checkbox.checked = value;
    checkbox.className = 'boolean-checkbox';

    const label = document.createElement('label');
    label.className = 'boolean-label';
    label.appendChild(checkbox);
    label.appendChild(document.createTextNode(' ' + (property.label || 'Enabled')));

    checkbox.addEventListener('change', () => {
      this.onPropertyInput(property, checkbox.checked);
    });

    container.appendChild(label);

    return container;
  }

  /**
   * Create Array editor
   * @param {Object} property - Property definition
   * @returns {HTMLElement} Array editor
   */
  createArrayEditor(property) {
    const value = this.getPropertyValue(property.path) || [];

    const container = document.createElement('div');
    container.className = 'array-editor';

    // Array header
    const header = document.createElement('div');
    header.className = 'array-header';

    const title = document.createElement('span');
    title.textContent = `${property.label} (${value.length})`;
    title.className = 'array-title';

    const addBtn = document.createElement('button');
    addBtn.textContent = '+';
    addBtn.className = 'array-add-btn';
    addBtn.title = 'Add item';

    addBtn.addEventListener('click', () => {
      this.addArrayItem(property, value);
    });

    header.appendChild(title);
    header.appendChild(addBtn);

    // Array items
    const itemsContainer = document.createElement('div');
    itemsContainer.className = 'array-items';

    value.forEach((item, index) => {
      const itemElement = this.createArrayItem(property, item, index);
      itemsContainer.appendChild(itemElement);
    });

    container.appendChild(header);
    container.appendChild(itemsContainer);

    return container;
  }

  /**
   * Create array item editor
   * @param {Object} property - Property definition
   * @param {any} item - Item value
   * @param {number} index - Item index
   * @returns {HTMLElement} Array item element
   */
  createArrayItem(property, item, index) {
    const container = document.createElement('div');
    container.className = 'array-item';

    const indexLabel = document.createElement('span');
    indexLabel.textContent = `[${index}]`;
    indexLabel.className = 'array-index';

    const editor = document.createElement('input');
    editor.type = 'text';
    editor.value = JSON.stringify(item);
    editor.className = 'array-item-input';

    const removeBtn = document.createElement('button');
    removeBtn.textContent = '×';
    removeBtn.className = 'array-remove-btn';
    removeBtn.title = 'Remove item';

    editor.addEventListener('input', () => {
      try {
        const newValue = JSON.parse(editor.value);
        this.updateArrayItem(property, index, newValue);
      } catch (e) {
        // Invalid JSON, don't update
      }
    });

    removeBtn.addEventListener('click', () => {
      this.removeArrayItem(property, index);
    });

    container.appendChild(indexLabel);
    container.appendChild(editor);
    container.appendChild(removeBtn);

    return container;
  }

  /**
   * Get property value from target object
   * @param {string} path - Property path (dot notation)
   * @returns {any} Property value
   */
  getPropertyValue(path) {
    if (!this.currentTarget || !path) return null;

    const parts = path.split('.');
    let value = this.currentTarget;

    for (const part of parts) {
      if (value && typeof value === 'object' && part in value) {
        value = value[part];
      } else {
        return null;
      }
    }

    return value;
  }

  /**
   * Set property value on target object
   * @param {string} path - Property path (dot notation)
   * @param {any} value - New value
   */
  setPropertyValue(path, value) {
    if (!this.currentTarget || !path) return;

    const parts = path.split('.');
    let target = this.currentTarget;

    // Navigate to parent object
    for (let i = 0; i < parts.length - 1; i++) {
      const part = parts[i];
      if (!target[part] || typeof target[part] !== 'object') {
        target[part] = {};
      }
      target = target[part];
    }

    // Set the final property
    const finalPart = parts[parts.length - 1];
    target[finalPart] = value;

    // Mark as modified
    this.editorState.markModified();

    // Emit property changed event
    this.eventBus.emitPropertyChanged(path, value, this.currentTarget);
  }

  /**
   * Handle property input (real-time changes)
   * @param {Object} property - Property definition
   * @param {any} value - New value
   */
  onPropertyInput(property, value) {
    // Validate the value
    const validationError = this.validateProperty(property, value);
    if (validationError) {
      this.showValidationError(property.name, validationError);
      return;
    }

    // Clear any existing validation error
    this.clearValidationError(property.name);

    // Update the value
    this.setPropertyValue(property.path, value);
  }

  /**
   * Handle property blur (when user finishes editing)
   * @param {Object} property - Property definition
   * @param {any} value - Final value
   */
  onPropertyBlur(property, value) {
    // Perform final validation and cleanup
    this.onPropertyInput(property, value);

    // Add to history for undo/redo
    this.editorState.addToHistory({
      type: 'property-change',
      target: this.currentTarget,
      property: property.path,
      oldValue: this.getPropertyValue(property.path),
      newValue: value
    });
  }

  /**
   * Validate property value
   * @param {Object} property - Property definition
   * @param {any} value - Value to validate
   * @returns {string|null} Error message or null if valid
   */
  validateProperty(property, value) {
    if (!property.validators) return null;

    for (const validatorDef of property.validators) {
      let validatorName, validatorArgs;

      if (typeof validatorDef === 'string') {
        const parts = validatorDef.split(':');
        validatorName = parts[0];
        validatorArgs = parts[1] ? parts[1].split(',').map(arg => {
          const num = parseFloat(arg);
          return isNaN(num) ? arg : num;
        }) : [];
      } else {
        validatorName = validatorDef.name;
        validatorArgs = validatorDef.args || [];
      }

      const validator = this.validators.get(validatorName);
      if (validator) {
        const error = validator(value, ...validatorArgs);
        if (error) return error;
      }
    }

    return null;
  }

  /**
   * Show validation error
   * @param {string} propertyName - Property name
   * @param {string} error - Error message
   */
  showValidationError(propertyName, error) {
    const propertyRow = this.container.querySelector(`[data-property-name="${propertyName}"]`);
    if (!propertyRow) return;

    // Remove existing error
    this.clearValidationError(propertyName);

    // Add error message
    const errorElement = document.createElement('div');
    errorElement.className = 'property-error';
    errorElement.textContent = error;

    propertyRow.appendChild(errorElement);
    propertyRow.classList.add('has-error');
  }

  /**
   * Clear validation error
   * @param {string} propertyName - Property name
   */
  clearValidationError(propertyName) {
    const propertyRow = this.container.querySelector(`[data-property-name="${propertyName}"]`);
    if (!propertyRow) return;

    const errorElement = propertyRow.querySelector('.property-error');
    if (errorElement) {
      errorElement.remove();
    }

    propertyRow.classList.remove('has-error');
  }

  /**
   * Open file browser
   * @param {Object} property - Property definition
   * @param {HTMLElement} input - Input element
   */
  openFileBrowser(property, input) {
    // Send message to main process to open file dialog
    this.eventBus.emit('open-file-browser', {
      property: property,
      currentValue: input.value,
      callback: (selectedPath) => {
        if (selectedPath) {
          input.value = selectedPath;
          this.onPropertyInput(property, selectedPath);
        }
      }
    });
  }

  /**
   * Add array item
   * @param {Object} property - Property definition
   * @param {Array} currentArray - Current array value
   */
  addArrayItem(property, currentArray) {
    const newArray = [...currentArray];

    // Add default item based on item type
    let defaultItem;
    switch (property.itemType) {
      case 'pathPoint':
        defaultItem = {
          position: { x: 0, y: 0, z: 0 },
          controlPoint1: { x: 0, y: 0, z: 0 },
          controlPoint2: { x: 0, y: 0, z: 0 },
          speed: 1.0,
          rotation: 0
        };
        break;
      default:
        defaultItem = '';
    }

    newArray.push(defaultItem);
    this.setPropertyValue(property.path, newArray);
    this.render();
  }

  /**
   * Remove array item
   * @param {Object} property - Property definition
   * @param {number} index - Index to remove
   */
  removeArrayItem(property, index) {
    const currentArray = this.getPropertyValue(property.path) || [];
    const newArray = currentArray.filter((_, i) => i !== index);
    this.setPropertyValue(property.path, newArray);
    this.render();
  }

  /**
   * Update array item
   * @param {Object} property - Property definition
   * @param {number} index - Index to update
   * @param {any} newValue - New value
   */
  updateArrayItem(property, index, newValue) {
    const currentArray = this.getPropertyValue(property.path) || [];
    const newArray = [...currentArray];
    newArray[index] = newValue;
    this.setPropertyValue(property.path, newArray);
  }

  /**
   * Refresh a specific property
   * @param {string} propertyName - Property name to refresh
   */
  refreshProperty(propertyName) {
    const propertyRow = this.container.querySelector(`[data-property-name="${propertyName}"]`);
    if (propertyRow) {
      // Find the property definition and re-render just this property
      // This is a simplified version - in a full implementation,
      // you'd want to store property definitions for efficient updates
      this.render();
    }
  }

  /**
   * Reset all properties to default values
   */
  resetProperties() {
    if (!this.currentTarget) return;

    // This would reset properties to their default values
    // Implementation depends on having default value definitions
    console.log('Reset properties not yet implemented');
  }

  /**
   * Destroy the component
   */
  destroy() {
    this.eventBus.off('selection-changed', this.onSelectionChanged, this);
    this.eventBus.off('property-changed', this.onExternalPropertyChanged, this);

    this.currentTarget = null;
    this.propertyGroups.clear();
    this.validators.clear();
    this.customEditors.clear();
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PropertyInspector;
} else {
  window.PropertyInspector = PropertyInspector;
}
