module.exports = {
  // Panel titles
  'level-editor': 'Level Editor',
  'level-hierarchy': 'Level Hierarchy',
  'property-inspector': 'Property Inspector',
  'sublevel-editor': 'SubLevel Editor',
  'toolbar': 'Toolbar',
  
  // Menu items
  'new-level': 'New Level',
  'load-level': 'Load Level',
  'save-level': 'Save Level',
  'save-level-as': 'Save Level As...',
  'import-level': 'Import Level',
  'export-level': 'Export Level',
  
  // Toolbar actions
  'select-mode': 'Select Mode',
  'add-sublevel': 'Add SubLevel',
  'add-spawner': 'Add Spawner',
  'add-path': 'Add Path',
  'add-map-layer': 'Add Map Layer',
  'preview-level': 'Preview Level',
  'stop-preview': 'Stop Preview',
  
  // Level hierarchy
  'level': 'Level',
  'sublevels': 'SubLevels',
  'spawners': 'Spawners',
  'paths': 'Paths',
  'map-layers': 'Map Layers',
  'events': 'Events',
  
  // Property labels
  'name': 'Name',
  'description': 'Description',
  'author': 'Author',
  'version': 'Version',
  'duration': 'Duration (seconds)',
  'difficulty': 'Difficulty',
  'bounds': 'Bounds',
  'position': 'Position',
  'size': 'Size',
  'active': 'Active',
  'visible': 'Visible',
  
  // SubLevel properties
  'entry-point': 'Entry Point',
  'exit-point': 'Exit Point',
  'connections': 'Connections',
  'load-priority': 'Load Priority',
  
  // Spawner properties
  'prefab-path': 'Prefab Path',
  'spawn-pattern': 'Spawn Pattern',
  'spawn-count': 'Spawn Count',
  'spawn-interval': 'Spawn Interval',
  'spawn-delay': 'Spawn Delay',
  
  // Path properties
  'path-points': 'Path Points',
  'is-loop': 'Is Loop',
  'total-length': 'Total Length',
  
  // Map layer properties
  'layer-type': 'Layer Type',
  'depth': 'Depth',
  'scroll-speed': 'Scroll Speed',
  'repeat-mode': 'Repeat Mode',
  'offset': 'Offset',
  
  // Camera settings
  'viewport-size': 'Viewport Size',
  'scroll-speed': 'Scroll Speed',
  'follow-target': 'Follow Target',
  'camera-bounds': 'Camera Bounds',
  'smoothing': 'Smoothing',
  
  // Messages
  'no-level-loaded': 'No level loaded. Create a new level or load an existing one.',
  'no-selection': 'No item selected. Select an item to view its properties.',
  'unsaved-changes': 'You have unsaved changes. Do you want to save before continuing?',
  'level-saved': 'Level saved successfully.',
  'level-loaded': 'Level loaded successfully.',
  'invalid-data': 'Invalid data format.',
  
  // Buttons
  'ok': 'OK',
  'cancel': 'Cancel',
  'save': 'Save',
  'load': 'Load',
  'delete': 'Delete',
  'duplicate': 'Duplicate',
  'add': 'Add',
  'remove': 'Remove',
  'browse': 'Browse...',
  'reset': 'Reset',
  'apply': 'Apply',
  
  // Validation messages
  'required-field': 'This field is required.',
  'invalid-number': 'Please enter a valid number.',
  'invalid-range': 'Value must be between {min} and {max}.',
  'invalid-file-path': 'Invalid file path.',
  'name-already-exists': 'An item with this name already exists.',
  
  // Context menu
  'add-sublevel': 'Add SubLevel',
  'add-spawner': 'Add Spawner',
  'add-path': 'Add Path',
  'add-map-layer': 'Add Map Layer',
  'duplicate-item': 'Duplicate',
  'delete-item': 'Delete',
  'rename-item': 'Rename',
  'copy-item': 'Copy',
  'paste-item': 'Paste',
  
  // Edit modes
  'select': 'Select',
  'add-spawner': 'Add Spawner',
  'edit-path': 'Edit Path',
  'add-background': 'Add Background',
  'edit-events': 'Edit Events',
  'add-sublevel': 'Add SubLevel',
  'edit-sublevel': 'Edit SubLevel'
};
