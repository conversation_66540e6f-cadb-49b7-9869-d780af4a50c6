/**
 * Level Editor Extension Panel
 * Main entry point for the panel UI - Cocos Creator 3.8 format
 */

'use strict';

// Panel template (HTML content)
exports.template = `
<div class="level-editor-container">
  <header class="level-editor-header">
    <h2>Level Editor</h2>
  </header>

  <div class="level-editor-toolbar">
    <ui-button class="toolbar-btn" id="new-level-btn">New Level</ui-button>
    <ui-button class="toolbar-btn" id="load-level-btn">Load Level</ui-button>
    <ui-button class="toolbar-btn" id="save-level-btn">Save Level</ui-button>
  </div>

  <div class="level-editor-content">
    <div class="status-panel">
      <h3>Level Editor Panel</h3>
      <p>Welcome to the Level Editor!</p>
      <p>Use the buttons above to create, load, or save levels.</p>
      <div class="level-status">
        <strong>Status:</strong> <span id="status-text">Ready</span>
      </div>
    </div>
  </div>
</div>
`;

// Panel styles
exports.style = `
:host {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #2b2b2b;
  color: #ffffff;
  font-family: Arial, sans-serif;
}

.level-editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
}

.level-editor-header h2 {
  margin: 0 0 20px 0;
  color: #ffffff;
}

.level-editor-toolbar {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.toolbar-btn {
  padding: 8px 16px;
  background: #007acc;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.toolbar-btn:hover {
  background: #005a9e;
}

.level-editor-content {
  flex: 1;
  background: #3c3c3c;
  padding: 15px;
  border-radius: 4px;
  overflow-y: auto;
}

.status-panel h3 {
  margin: 0 0 10px 0;
  color: #ffffff;
}

.status-panel p {
  margin: 0 0 10px 0;
  color: #cccccc;
}

.level-status {
  margin-top: 20px;
  padding: 10px;
  background: #4a4a4a;
  border-radius: 4px;
  color: #ffffff;
}
`;

// HTML element selectors
exports.$ = {
  newBtn: '#new-level-btn',
  loadBtn: '#load-level-btn',
  saveBtn: '#save-level-btn',
  statusText: '#status-text'
};

// Panel methods
exports.methods = {
  updateStatus(message) {
    if (this.$.statusText) {
      this.$.statusText.textContent = message;
    }
  },

  setupEventHandlers() {
    console.log('[Level Editor Panel] Setting up event handlers');

    if (this.$.newBtn) {
      this.$.newBtn.addEventListener('click', () => {
        console.log('[Panel] New Level clicked');
        this.updateStatus('Creating new level...');
        Editor.Message.send('level-editor-extension', 'new-level');
      });
    }

    if (this.$.loadBtn) {
      this.$.loadBtn.addEventListener('click', () => {
        console.log('[Panel] Load Level clicked');
        this.updateStatus('Loading level...');
        Editor.Message.send('level-editor-extension', 'load-level');
      });
    }

    if (this.$.saveBtn) {
      this.$.saveBtn.addEventListener('click', () => {
        console.log('[Panel] Save Level clicked');
        this.updateStatus('Saving level...');
        Editor.Message.send('level-editor-extension', 'save-level');
      });
    }

    console.log('[Panel] Event handlers setup complete');
  }
};

// Panel ready function
exports.ready = function() {
  console.log('[Level Editor Panel] Panel ready - using correct Cocos Creator 3.8 format');

  // Setup button event handlers
  this.setupEventHandlers();
};

// Panel lifecycle functions
exports.beforeClose = async function() {
  console.log('[Level Editor Panel] Panel closing...');
  // Return true to allow closing, false to prevent
  return true;
};

exports.close = function() {
  console.log('[Level Editor Panel] Panel closed');
};

// Panel event listeners
exports.listeners = {
  show() {
    console.log('[Level Editor Panel] Panel shown');
  },

  hide() {
    console.log('[Level Editor Panel] Panel hidden');
  },

  resize() {
    console.log('[Level Editor Panel] Panel resized');
  }
};

// All panel functionality is now handled through the proper exports structure above
