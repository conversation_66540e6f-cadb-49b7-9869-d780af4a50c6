/**
 * Editor Event Bus
 * Centralized event system for communication between components
 */
class EditorEventBus {
  constructor() {
    this.listeners = new Map();
    this.onceListeners = new Map();
  }

  /**
   * Subscribe to an event
   * @param {string} eventName - Name of the event
   * @param {Function} callback - Callback function
   * @param {Object} context - Context for the callback (optional)
   */
  on(eventName, callback, context = null) {
    if (!this.listeners.has(eventName)) {
      this.listeners.set(eventName, []);
    }
    
    this.listeners.get(eventName).push({
      callback,
      context
    });
  }

  /**
   * Subscribe to an event (one time only)
   * @param {string} eventName - Name of the event
   * @param {Function} callback - Callback function
   * @param {Object} context - Context for the callback (optional)
   */
  once(eventName, callback, context = null) {
    if (!this.onceListeners.has(eventName)) {
      this.onceListeners.set(eventName, []);
    }
    
    this.onceListeners.get(eventName).push({
      callback,
      context
    });
  }

  /**
   * Unsubscribe from an event
   * @param {string} eventName - Name of the event
   * @param {Function} callback - Callback function to remove
   * @param {Object} context - Context for the callback (optional)
   */
  off(eventName, callback, context = null) {
    // Remove from regular listeners
    if (this.listeners.has(eventName)) {
      const listeners = this.listeners.get(eventName);
      const index = listeners.findIndex(listener => 
        listener.callback === callback && listener.context === context
      );
      if (index !== -1) {
        listeners.splice(index, 1);
        if (listeners.length === 0) {
          this.listeners.delete(eventName);
        }
      }
    }

    // Remove from once listeners
    if (this.onceListeners.has(eventName)) {
      const listeners = this.onceListeners.get(eventName);
      const index = listeners.findIndex(listener => 
        listener.callback === callback && listener.context === context
      );
      if (index !== -1) {
        listeners.splice(index, 1);
        if (listeners.length === 0) {
          this.onceListeners.delete(eventName);
        }
      }
    }
  }

  /**
   * Emit an event
   * @param {string} eventName - Name of the event
   * @param {...any} args - Arguments to pass to callbacks
   */
  emit(eventName, ...args) {
    // Call regular listeners
    if (this.listeners.has(eventName)) {
      const listeners = this.listeners.get(eventName);
      listeners.forEach(listener => {
        try {
          if (listener.context) {
            listener.callback.call(listener.context, ...args);
          } else {
            listener.callback(...args);
          }
        } catch (error) {
          console.error(`Error in event listener for '${eventName}':`, error);
        }
      });
    }

    // Call once listeners and remove them
    if (this.onceListeners.has(eventName)) {
      const listeners = this.onceListeners.get(eventName);
      listeners.forEach(listener => {
        try {
          if (listener.context) {
            listener.callback.call(listener.context, ...args);
          } else {
            listener.callback(...args);
          }
        } catch (error) {
          console.error(`Error in once event listener for '${eventName}':`, error);
        }
      });
      this.onceListeners.delete(eventName);
    }
  }

  /**
   * Remove all listeners for an event
   * @param {string} eventName - Name of the event (optional, removes all if not specified)
   */
  removeAllListeners(eventName = null) {
    if (eventName) {
      this.listeners.delete(eventName);
      this.onceListeners.delete(eventName);
    } else {
      this.listeners.clear();
      this.onceListeners.clear();
    }
  }

  /**
   * Get the number of listeners for an event
   * @param {string} eventName - Name of the event
   * @returns {number} Number of listeners
   */
  listenerCount(eventName) {
    const regularCount = this.listeners.has(eventName) ? this.listeners.get(eventName).length : 0;
    const onceCount = this.onceListeners.has(eventName) ? this.onceListeners.get(eventName).length : 0;
    return regularCount + onceCount;
  }

  /**
   * Get all event names that have listeners
   * @returns {string[]} Array of event names
   */
  eventNames() {
    const names = new Set();
    this.listeners.forEach((_, eventName) => names.add(eventName));
    this.onceListeners.forEach((_, eventName) => names.add(eventName));
    return Array.from(names);
  }

  // Convenience methods for common events

  /**
   * Level data changed event
   * @param {Function} callback - Callback function
   * @param {Object} context - Context (optional)
   */
  onLevelDataChanged(callback, context = null) {
    this.on('level-data-changed', callback, context);
  }

  /**
   * Selection changed event
   * @param {Function} callback - Callback function
   * @param {Object} context - Context (optional)
   */
  onSelectionChanged(callback, context = null) {
    this.on('selection-changed', callback, context);
  }

  /**
   * Property changed event
   * @param {Function} callback - Callback function
   * @param {Object} context - Context (optional)
   */
  onPropertyChanged(callback, context = null) {
    this.on('property-changed', callback, context);
  }

  /**
   * SubLevel modified event
   * @param {Function} callback - Callback function
   * @param {Object} context - Context (optional)
   */
  onSubLevelModified(callback, context = null) {
    this.on('sublevel-modified', callback, context);
  }

  /**
   * Edit mode changed event
   * @param {Function} callback - Callback function
   * @param {Object} context - Context (optional)
   */
  onEditModeChanged(callback, context = null) {
    this.on('edit-mode-changed', callback, context);
  }

  /**
   * Emit level data changed
   * @param {Object} levelData - The level data
   */
  emitLevelDataChanged(levelData) {
    this.emit('level-data-changed', levelData);
  }

  /**
   * Emit selection changed
   * @param {Object} selection - The selection info
   */
  emitSelectionChanged(selection) {
    this.emit('selection-changed', selection);
  }

  /**
   * Emit property changed
   * @param {string} propertyName - Name of the property
   * @param {any} value - New value
   * @param {Object} target - Target object
   */
  emitPropertyChanged(propertyName, value, target) {
    this.emit('property-changed', { propertyName, value, target });
  }

  /**
   * Emit SubLevel modified
   * @param {Object} subLevel - The modified SubLevel
   * @param {string} changeType - Type of change
   */
  emitSubLevelModified(subLevel, changeType) {
    this.emit('sublevel-modified', { subLevel, changeType });
  }

  /**
   * Emit edit mode changed
   * @param {string} mode - New edit mode
   * @param {string} previousMode - Previous edit mode
   */
  emitEditModeChanged(mode, previousMode) {
    this.emit('edit-mode-changed', { mode, previousMode });
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EditorEventBus;
} else {
  window.EditorEventBus = EditorEventBus;
}
