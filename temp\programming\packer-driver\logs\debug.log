14:50:43.073 debug: 2025/7/26 14:50:43
14:50:43.074 debug: Project: E:\M2Game\Client
14:50:43.074 debug: Targets: editor,preview
14:50:43.075 debug: Incremental file seems great.
14:50:43.076 debug: Engine path: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine
14:50:43.083 debug: Initializing target [Editor]
14:50:43.083 debug: Loading cache
14:50:43.085 debug: Loading cache costs 1.543999999999869ms.
14:50:43.085 debug: Engine features shipped in editor: base,gfx-webgl,gfx-webgl2,gfx-empty,gfx-webgpu,3d,animation,skeletal-animation,2d,rich-text,mask,graphics,ui-skew,ui,affine-transform,particle,particle-2d,physics-framework,physics-cannon,physics-physx,physics-ammo,physics-builtin,physics-2d-framework,physics-2d-box2d-jsb,physics-2d-box2d,physics-2d-builtin,physics-2d-box2d-wasm,intersection-2d,primitive,profiler,occlusion-query,geometry-renderer,debug-renderer,audio,video,xr,light-probe,terrain,webview,tween,tiled-map,vendor-google,spine-3.8,spine-4.2,dragon-bones,marionette,procedural-animation,custom-pipeline,custom-pipeline-builtin-scripts,custom-pipeline-post-process,legacy-pipeline,websocket,websocket-server,meshopt
14:50:43.085 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
14:50:43.085 debug: Initializing target [Preview]
14:50:43.085 debug: Loading cache
14:50:43.087 debug: Loading cache costs 2.06230000000005ms.
14:50:43.087 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
14:50:43.118 debug: Sync engine features: 2d,affine-transform,animation,audio,base,custom-pipeline,dragon-bones,gfx-webgl,gfx-webgl2,graphics,intersection-2d,marionette,mask,particle-2d,physics-2d-builtin,profiler,rich-text,skeletal-animation,spine-3.8,tiled-map,tween,ui,video,websocket,webview,custom-pipeline
14:50:43.120 debug: Reset databases. Enumerated domains: [
  {
    "root": "db://internal/",
    "physical": "C:\\ProgramData\\cocos\\editors\\Creator\\3.8.6\\resources\\resources\\3d\\engine\\editor\\assets"
  },
  {
    "root": "db://assets/",
    "physical": "E:\\M2Game\\Client\\assets"
  }
]
14:50:43.120 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///E:/M2Game/Client/assets/"
  }
}
14:50:43.120 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///E:/M2Game/Client/assets/"
  }
}
14:50:43.121 debug: Pulling asset-db.
14:50:43.128 debug: Fetch asset-db cost: 7.061400000000049ms.
14:50:43.129 debug: Build iteration starts.
Number of accumulated asset changes: 48
Feature changed: false
14:50:43.129 debug: Target(editor) build started.
14:50:43.146 debug: Detected change: cce:/internal/x/cc. Last mtime: Thu Jan 01 1970 08:00:15 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间)
14:50:43.146 debug: Inspect cce:/internal/x/cc
14:50:43.191 debug: transform url: 'cce:/internal/x/cc' costs: 45.10 ms
14:50:43.192 debug: Resolve cce:/internal/x/cc-fu/base from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/base.
14:50:43.192 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl.
14:50:43.192 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl2 from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl2.
14:50:43.192 debug: Resolve cce:/internal/x/cc-fu/gfx-empty from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-empty.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/gfx-webgpu from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgpu.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/3d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/3d.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/animation.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/skeletal-animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/skeletal-animation.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/2d.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/sorting from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/sorting.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/rich-text from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/rich-text.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/mask from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/mask.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/graphics from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/graphics.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/ui-skew from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui-skew.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/ui from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/affine-transform from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/affine-transform.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/particle from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/particle-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle-2d.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/physics-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-framework.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/physics-cannon from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-cannon.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/physics-physx from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-physx.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/physics-ammo from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-ammo.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/physics-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-builtin.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/physics-2d-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-framework.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-jsb from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-jsb.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/physics-2d-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-builtin.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-wasm from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-wasm.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/intersection-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/intersection-2d.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/primitive from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/primitive.
14:50:43.193 debug: Resolve cce:/internal/x/cc-fu/profiler from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/profiler.
14:50:43.194 debug: Resolve cce:/internal/x/cc-fu/geometry-renderer from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/geometry-renderer.
14:50:43.194 debug: Resolve cce:/internal/x/cc-fu/audio from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/audio.
14:50:43.194 debug: Resolve cce:/internal/x/cc-fu/video from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/video.
14:50:43.194 debug: Resolve cce:/internal/x/cc-fu/xr from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/xr.
14:50:43.194 debug: Resolve cce:/internal/x/cc-fu/light-probe from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/light-probe.
14:50:43.194 debug: Resolve cce:/internal/x/cc-fu/terrain from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/terrain.
14:50:43.194 debug: Resolve cce:/internal/x/cc-fu/webview from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/webview.
14:50:43.194 debug: Resolve cce:/internal/x/cc-fu/tween from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tween.
14:50:43.194 debug: Resolve cce:/internal/x/cc-fu/tiled-map from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tiled-map.
14:50:43.194 debug: Resolve cce:/internal/x/cc-fu/vendor-google from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/vendor-google.
14:50:43.194 debug: Resolve cce:/internal/x/cc-fu/spine from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/spine.
14:50:43.194 debug: Resolve cce:/internal/x/cc-fu/dragon-bones from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/dragon-bones.
14:50:43.194 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline.
14:50:43.194 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline-post-process from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline-post-process.
14:50:43.194 debug: Resolve cce:/internal/x/cc-fu/legacy-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/legacy-pipeline.
14:50:43.194 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:26:53 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间)
14:50:43.194 debug: Inspect cce:/internal/x/prerequisite-imports
14:50:43.215 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 21.30 ms
14:50:43.217 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
14:50:43.217 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
14:50:43.218 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
14:50:43.218 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
14:50:43.219 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
14:50:43.219 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
14:50:43.220 debug: Resolve file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts.
14:50:43.220 debug: Resolve file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts.
14:50:43.220 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts.
14:50:43.221 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Background.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Background.ts.
14:50:43.221 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts.
14:50:43.221 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts.
14:50:43.221 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts.
14:50:43.222 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Global.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.222 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts.
14:50:43.222 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts.
14:50:43.223 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Menu.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Menu.ts.
14:50:43.223 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.223 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Player.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Player.ts.
14:50:43.224 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts.
14:50:43.224 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts.
14:50:43.224 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts.
14:50:43.224 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts.
14:50:43.225 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
14:50:43.225 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts.
14:50:43.225 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts.
14:50:43.226 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelData.ts.
14:50:43.226 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelSerializer.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelSerializer.ts.
14:50:43.226 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.227 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts.
14:50:43.227 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts.
14:50:43.227 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts.
14:50:43.227 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts.
14:50:43.228 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts.
14:50:43.228 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts.
14:50:43.228 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts.
14:50:43.229 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts.
14:50:43.229 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts.
14:50:43.229 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts.
14:50:43.229 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts.
14:50:43.230 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts.
14:50:43.230 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts.
14:50:43.230 debug: Resolve file:///E:/M2Game/Client/assets/scripts/GameInstance.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/GameInstance.ts.
14:50:43.231 debug: Resolve file:///E:/M2Game/Client/assets/scripts/IMgr.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/IMgr.ts.
14:50:43.231 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts.
14:50:43.231 debug: Resolve file:///E:/M2Game/Client/assets/scripts/MainUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/MainUI.ts.
14:50:43.232 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts.
14:50:43.232 debug: Resolve file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts.
14:50:43.232 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.232 debug: Detected change: cce:/internal/code-quality/cr.mjs. Last mtime: Sat Jul 26 2025 11:44:35 GMT+0800 (中国标准时间), Current mtime: Sat Jul 26 2025 14:50:39 GMT+0800 (中国标准时间)
14:50:43.232 debug: Inspect cce:/internal/code-quality/cr.mjs
14:50:43.241 debug: transform url: 'cce:/internal/code-quality/cr.mjs' costs: 9.10 ms
14:50:43.242 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
14:50:43.242 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
14:50:43.242 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
14:50:43.242 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as external dependency cc/env.
14:50:43.243 debug: Resolve ./builtin-pipeline-settings from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
14:50:43.244 debug: Resolve ./builtin-pipeline-pass from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
14:50:43.244 debug: Resolve ./builtin-pipeline from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
14:50:43.244 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.244 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
14:50:43.244 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
14:50:43.244 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
14:50:43.244 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as external dependency cc/env.
14:50:43.245 debug: Resolve ./builtin-pipeline-types from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
14:50:43.245 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
14:50:43.245 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
14:50:43.245 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
14:50:43.245 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.245 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
14:50:43.245 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
14:50:43.245 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
14:50:43.246 debug: Resolve ./builtin-pipeline-settings from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
14:50:43.246 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as external dependency cc/env.
14:50:43.246 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.246 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
14:50:43.246 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
14:50:43.246 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
14:50:43.246 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as external dependency cc/env.
14:50:43.246 debug: Resolve ./builtin-pipeline-types from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
14:50:43.246 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
14:50:43.246 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
14:50:43.246 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
14:50:43.246 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts as cce:/internal/x/cc.
14:50:43.247 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts as cce:/internal/x/cc.
14:50:43.247 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.247 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts as cce:/internal/x/cc.
14:50:43.247 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts as cce:/internal/x/cc.
14:50:43.247 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts as cce:/internal/x/cc.
14:50:43.247 debug: Resolve ./PersistNode from file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.247 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.247 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts as cce:/internal/x/cc.
14:50:43.247 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts as cce:/internal/x/cc.
14:50:43.247 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts as cce:/internal/x/cc.
14:50:43.247 debug: Resolve ./factroy/AnimFactory from file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts as file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts.
14:50:43.248 debug: Resolve ./factroy/EnemyBulletFactory from file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts as file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts.
14:50:43.248 debug: Resolve ./factroy/EnemyFactory from file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts as file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts.
14:50:43.248 debug: Resolve ./factroy/GoodsFactory from file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts as file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts.
14:50:43.249 debug: Resolve ./factroy/PlayerBulletFactory from file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts as file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts.
14:50:43.249 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.249 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts as cce:/internal/x/cc.
14:50:43.249 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts as cce:/internal/x/cc.
14:50:43.249 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts as cce:/internal/x/cc.
14:50:43.249 debug: Resolve ../PersistNode from file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.250 debug: Resolve ./GameFactory from file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
14:50:43.250 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts as cce:/internal/x/cc.
14:50:43.250 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts as cce:/internal/x/cc.
14:50:43.250 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts as cce:/internal/x/cc.
14:50:43.250 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.250 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as cce:/internal/x/cc.
14:50:43.250 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as cce:/internal/x/cc.
14:50:43.250 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as cce:/internal/x/cc.
14:50:43.251 debug: Resolve ../EnemyBullet from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts.
14:50:43.251 debug: Resolve ../Global from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.251 debug: Resolve ../PersistNode from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.251 debug: Resolve ./GameFactory from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
14:50:43.251 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.251 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as cce:/internal/x/cc.
14:50:43.251 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as cce:/internal/x/cc.
14:50:43.251 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as cce:/internal/x/cc.
14:50:43.252 debug: Resolve ./Global from file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.252 debug: Resolve ./PersistNode from file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.252 debug: Resolve ./Player from file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as file:///E:/M2Game/Client/assets/scripts/Game/Player.ts.
14:50:43.252 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Global.ts as cce:/internal/x/cc.
14:50:43.252 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Global.ts as cce:/internal/x/cc.
14:50:43.252 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Global.ts as cce:/internal/x/cc.
14:50:43.252 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/Player.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.252 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Player.ts as cce:/internal/x/cc.
14:50:43.252 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Player.ts as cce:/internal/x/cc.
14:50:43.252 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Player.ts as cce:/internal/x/cc.
14:50:43.253 debug: Resolve ./Global from file:///E:/M2Game/Client/assets/scripts/Game/Player.ts as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.253 debug: Resolve ./PersistNode from file:///E:/M2Game/Client/assets/scripts/Game/Player.ts as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.253 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.253 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as cce:/internal/x/cc.
14:50:43.253 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as cce:/internal/x/cc.
14:50:43.253 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as cce:/internal/x/cc.
14:50:43.253 debug: Resolve ../Enemy from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts.
14:50:43.254 debug: Resolve ../Global from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.254 debug: Resolve ../PersistNode from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.255 debug: Resolve ./GameFactory from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
14:50:43.255 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.255 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts as cce:/internal/x/cc.
14:50:43.255 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts as cce:/internal/x/cc.
14:50:43.255 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts as cce:/internal/x/cc.
14:50:43.255 debug: Resolve ./Global from file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.256 debug: Resolve ./PersistNode from file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.256 debug: Resolve ./Player from file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts as file:///E:/M2Game/Client/assets/scripts/Game/Player.ts.
14:50:43.256 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.256 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as cce:/internal/x/cc.
14:50:43.256 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as cce:/internal/x/cc.
14:50:43.256 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as cce:/internal/x/cc.
14:50:43.256 debug: Resolve ../Global from file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.257 debug: Resolve ../Goods from file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts.
14:50:43.257 debug: Resolve ../PersistNode from file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.258 debug: Resolve ./GameFactory from file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
14:50:43.258 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.258 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts as cce:/internal/x/cc.
14:50:43.258 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts as cce:/internal/x/cc.
14:50:43.258 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts as cce:/internal/x/cc.
14:50:43.258 debug: Resolve ./Global from file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.258 debug: Resolve ./PersistNode from file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.259 debug: Resolve ./Player from file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts as file:///E:/M2Game/Client/assets/scripts/Game/Player.ts.
14:50:43.259 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.259 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as cce:/internal/x/cc.
14:50:43.259 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as cce:/internal/x/cc.
14:50:43.259 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as cce:/internal/x/cc.
14:50:43.259 debug: Resolve ../Global from file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.260 debug: Resolve ../PersistNode from file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.260 debug: Resolve ../PlayerBullet from file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts.
14:50:43.260 debug: Resolve ./GameFactory from file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
14:50:43.260 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.260 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as cce:/internal/x/cc.
14:50:43.261 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as cce:/internal/x/cc.
14:50:43.261 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as cce:/internal/x/cc.
14:50:43.261 debug: Resolve ./Enemy from file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts.
14:50:43.261 debug: Resolve ./Global from file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.262 debug: Resolve ./PersistNode from file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.262 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/Background.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.262 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Background.ts as cce:/internal/x/cc.
14:50:43.262 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Background.ts as cce:/internal/x/cc.
14:50:43.262 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Background.ts as cce:/internal/x/cc.
14:50:43.262 debug: Resolve ./Global from file:///E:/M2Game/Client/assets/scripts/Game/Background.ts as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.262 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.263 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts as cce:/internal/x/cc.
14:50:43.263 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts as cce:/internal/x/cc.
14:50:43.263 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts as cce:/internal/x/cc.
14:50:43.263 debug: Resolve ./Global from file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.263 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.263 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts as cce:/internal/x/cc.
14:50:43.263 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts as cce:/internal/x/cc.
14:50:43.263 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts as cce:/internal/x/cc.
14:50:43.264 debug: Resolve ./Global from file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.264 debug: Resolve ./PersistNode from file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.264 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Menu.ts as cce:/internal/x/cc.
14:50:43.264 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Menu.ts as cce:/internal/x/cc.
14:50:43.264 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Menu.ts as cce:/internal/x/cc.
14:50:43.264 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelData.ts as cce:/internal/x/cc.
14:50:43.264 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelData.ts as cce:/internal/x/cc.
14:50:43.264 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelSerializer.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.264 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelSerializer.ts as cce:/internal/x/cc.
14:50:43.264 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.264 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts as cce:/internal/x/cc.
14:50:43.264 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts as cce:/internal/x/cc.
14:50:43.264 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts as cce:/internal/x/cc.
14:50:43.265 debug: Resolve ./LevelData from file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelData.ts.
14:50:43.265 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.265 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts as cce:/internal/x/cc.
14:50:43.265 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts as cce:/internal/x/cc.
14:50:43.265 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts as cce:/internal/x/cc.
14:50:43.265 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.265 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.265 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as cce:/internal/x/cc.
14:50:43.265 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as cce:/internal/x/cc.
14:50:43.265 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as cce:/internal/x/cc.
14:50:43.265 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.266 debug: Resolve ../core/LevelSerializer from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelSerializer.ts.
14:50:43.266 debug: Resolve ../runtime/LevelManager from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts.
14:50:43.267 debug: Resolve ../runtime/CameraManager from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts.
14:50:43.267 debug: Resolve ../runtime/PathManager from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts.
14:50:43.267 debug: Resolve ../runtime/SpawnerSystem from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts.
14:50:43.268 debug: Resolve ../runtime/MapSystem from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts.
14:50:43.268 debug: Resolve ../events/EventSystem from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts.
14:50:43.269 debug: Resolve ./EditorGizmos from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts.
14:50:43.269 debug: Resolve ./PathEditor from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts.
14:50:43.269 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.269 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts as cce:/internal/x/cc.
14:50:43.269 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts as cce:/internal/x/cc.
14:50:43.269 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts as cce:/internal/x/cc.
14:50:43.269 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.270 debug: Resolve ./SubLevelComponent from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts.
14:50:43.270 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.270 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts as cce:/internal/x/cc.
14:50:43.270 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts as cce:/internal/x/cc.
14:50:43.271 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts as cce:/internal/x/cc.
14:50:43.271 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.271 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.291 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.291 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.291 debug: Resolve ./SpawnerComponent from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts.
14:50:43.291 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.291 debug: Resolve ./PathFollower from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts.
14:50:43.291 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.291 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve ../core/LevelData from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelData.ts.
14:50:43.291 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.291 debug: Resolve ./MapLayerComponent from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts.
14:50:43.291 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.291 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.291 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.291 debug: Resolve ../runtime/PathManager from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts.
14:50:43.291 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/GameInstance.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/GameInstance.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/GameInstance.ts as cce:/internal/x/cc.
14:50:43.291 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/GameInstance.ts as cce:/internal/x/cc.
14:50:43.292 debug: Resolve ./Luban/LubanMgr from file:///E:/M2Game/Client/assets/scripts/GameInstance.ts as file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts.
14:50:43.292 debug: Resolve ./Network/NetMgr from file:///E:/M2Game/Client/assets/scripts/GameInstance.ts as file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts.
14:50:43.292 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.292 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts as cce:/internal/x/cc.
14:50:43.292 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts as cce:/internal/x/cc.
14:50:43.292 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts as cce:/internal/x/cc.
14:50:43.292 debug: Resolve ../IMgr from file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts as file:///E:/M2Game/Client/assets/scripts/IMgr.ts.
14:50:43.292 debug: Resolve ../AutoGen/Luban/schema from file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts as file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts.
14:50:43.292 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/IMgr.ts as cce:/internal/x/cc.
14:50:43.292 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/IMgr.ts as cce:/internal/x/cc.
14:50:43.292 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/IMgr.ts as cce:/internal/x/cc.
14:50:43.292 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.292 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts as cce:/internal/x/cc.
14:50:43.292 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts as cce:/internal/x/cc.
14:50:43.292 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts as cce:/internal/x/cc.
14:50:43.292 debug: Resolve ../IMgr from file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts as file:///E:/M2Game/Client/assets/scripts/IMgr.ts.
14:50:43.292 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/MainUI.ts as cce:/internal/x/cc.
14:50:43.292 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/MainUI.ts as cce:/internal/x/cc.
14:50:43.292 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/MainUI.ts as cce:/internal/x/cc.
14:50:43.292 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts as cce:/internal/x/cc.
14:50:43.292 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts as cce:/internal/x/cc.
14:50:43.292 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts as cce:/internal/x/cc.
14:50:43.292 debug: Resolve cc/env from file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts as external dependency cc/env.
14:50:43.295 debug: Target(editor) ends with cost 165.51819999999998ms.
14:50:43.295 debug: Target(preview) build started.
14:50:43.296 debug: Detected change: cce:/internal/x/cc. Last mtime: Thu Jan 01 1970 08:00:15 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间)
14:50:43.296 debug: Inspect cce:/internal/x/cc
14:50:43.317 debug: transform url: 'cce:/internal/x/cc' costs: 20.70 ms
14:50:43.318 debug: Resolve cce:/internal/x/cc-fu/2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/2d.
14:50:43.318 debug: Resolve cce:/internal/x/cc-fu/sorting from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/sorting.
14:50:43.318 debug: Resolve cce:/internal/x/cc-fu/affine-transform from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/affine-transform.
14:50:43.318 debug: Resolve cce:/internal/x/cc-fu/animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/animation.
14:50:43.318 debug: Resolve cce:/internal/x/cc-fu/audio from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/audio.
14:50:43.318 debug: Resolve cce:/internal/x/cc-fu/base from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/base.
14:50:43.318 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline.
14:50:43.318 debug: Resolve cce:/internal/x/cc-fu/dragon-bones from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/dragon-bones.
14:50:43.318 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl.
14:50:43.318 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl2 from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl2.
14:50:43.318 debug: Resolve cce:/internal/x/cc-fu/graphics from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/graphics.
14:50:43.318 debug: Resolve cce:/internal/x/cc-fu/intersection-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/intersection-2d.
14:50:43.318 debug: Resolve cce:/internal/x/cc-fu/mask from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/mask.
14:50:43.318 debug: Resolve cce:/internal/x/cc-fu/particle-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle-2d.
14:50:43.318 debug: Resolve cce:/internal/x/cc-fu/physics-2d-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-builtin.
14:50:43.318 debug: Resolve cce:/internal/x/cc-fu/physics-2d-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-framework.
14:50:43.318 debug: Resolve cce:/internal/x/cc-fu/profiler from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/profiler.
14:50:43.318 debug: Resolve cce:/internal/x/cc-fu/rich-text from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/rich-text.
14:50:43.318 debug: Resolve cce:/internal/x/cc-fu/skeletal-animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/skeletal-animation.
14:50:43.319 debug: Resolve cce:/internal/x/cc-fu/spine from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/spine.
14:50:43.319 debug: Resolve cce:/internal/x/cc-fu/tiled-map from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tiled-map.
14:50:43.319 debug: Resolve cce:/internal/x/cc-fu/tween from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tween.
14:50:43.319 debug: Resolve cce:/internal/x/cc-fu/ui from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui.
14:50:43.319 debug: Resolve cce:/internal/x/cc-fu/video from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/video.
14:50:43.319 debug: Resolve cce:/internal/x/cc-fu/webview from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/webview.
14:50:43.319 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:26:53 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间)
14:50:43.319 debug: Inspect cce:/internal/x/prerequisite-imports
14:50:43.332 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 13.40 ms
14:50:43.333 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
14:50:43.334 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
14:50:43.334 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
14:50:43.335 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
14:50:43.335 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
14:50:43.336 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
14:50:43.337 debug: Resolve file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts.
14:50:43.337 debug: Resolve file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts.
14:50:43.338 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts.
14:50:43.338 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Background.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Background.ts.
14:50:43.338 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts.
14:50:43.338 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts.
14:50:43.339 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts.
14:50:43.339 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Global.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.339 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts.
14:50:43.339 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts.
14:50:43.340 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Menu.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Menu.ts.
14:50:43.340 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.340 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Player.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Player.ts.
14:50:43.340 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts.
14:50:43.341 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts.
14:50:43.341 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts.
14:50:43.341 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts.
14:50:43.341 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
14:50:43.342 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts.
14:50:43.342 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts.
14:50:43.342 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelData.ts.
14:50:43.343 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelSerializer.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelSerializer.ts.
14:50:43.343 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.343 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts.
14:50:43.343 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts.
14:50:43.344 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts.
14:50:43.344 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts.
14:50:43.344 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts.
14:50:43.344 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts.
14:50:43.345 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts.
14:50:43.345 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts.
14:50:43.345 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts.
14:50:43.345 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts.
14:50:43.346 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts.
14:50:43.346 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts.
14:50:43.346 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts.
14:50:43.347 debug: Resolve file:///E:/M2Game/Client/assets/scripts/GameInstance.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/GameInstance.ts.
14:50:43.347 debug: Resolve file:///E:/M2Game/Client/assets/scripts/IMgr.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/IMgr.ts.
14:50:43.347 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts.
14:50:43.347 debug: Resolve file:///E:/M2Game/Client/assets/scripts/MainUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/MainUI.ts.
14:50:43.347 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts.
14:50:43.348 debug: Resolve file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts.
14:50:43.348 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.348 debug: Detected change: cce:/internal/code-quality/cr.mjs. Last mtime: Sat Jul 26 2025 11:44:35 GMT+0800 (中国标准时间), Current mtime: Sat Jul 26 2025 14:50:39 GMT+0800 (中国标准时间)
14:50:43.348 debug: Inspect cce:/internal/code-quality/cr.mjs
14:50:43.357 debug: transform url: 'cce:/internal/code-quality/cr.mjs' costs: 8.50 ms
14:50:43.357 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
14:50:43.358 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
14:50:43.358 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
14:50:43.358 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as external dependency cc/env.
14:50:43.358 debug: Resolve ./builtin-pipeline-settings from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
14:50:43.359 debug: Resolve ./builtin-pipeline-pass from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
14:50:43.360 debug: Resolve ./builtin-pipeline from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
14:50:43.360 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.360 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
14:50:43.360 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
14:50:43.360 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
14:50:43.360 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as external dependency cc/env.
14:50:43.360 debug: Resolve ./builtin-pipeline-types from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
14:50:43.360 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
14:50:43.360 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
14:50:43.360 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
14:50:43.360 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.360 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
14:50:43.360 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
14:50:43.360 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
14:50:43.361 debug: Resolve ./builtin-pipeline-settings from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
14:50:43.361 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as external dependency cc/env.
14:50:43.361 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.361 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
14:50:43.361 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
14:50:43.361 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
14:50:43.361 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as external dependency cc/env.
14:50:43.362 debug: Resolve ./builtin-pipeline-types from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
14:50:43.362 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
14:50:43.362 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
14:50:43.362 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
14:50:43.362 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts as cce:/internal/x/cc.
14:50:43.362 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts as cce:/internal/x/cc.
14:50:43.362 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.362 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts as cce:/internal/x/cc.
14:50:43.362 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts as cce:/internal/x/cc.
14:50:43.362 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts as cce:/internal/x/cc.
14:50:43.362 debug: Resolve ./PersistNode from file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.363 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.363 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts as cce:/internal/x/cc.
14:50:43.364 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts as cce:/internal/x/cc.
14:50:43.364 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts as cce:/internal/x/cc.
14:50:43.364 debug: Resolve ./factroy/AnimFactory from file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts as file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts.
14:50:43.364 debug: Resolve ./factroy/EnemyBulletFactory from file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts as file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts.
14:50:43.364 debug: Resolve ./factroy/EnemyFactory from file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts as file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts.
14:50:43.365 debug: Resolve ./factroy/GoodsFactory from file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts as file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts.
14:50:43.365 debug: Resolve ./factroy/PlayerBulletFactory from file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts as file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts.
14:50:43.365 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.365 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts as cce:/internal/x/cc.
14:50:43.365 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts as cce:/internal/x/cc.
14:50:43.365 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts as cce:/internal/x/cc.
14:50:43.365 debug: Resolve ../PersistNode from file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.366 debug: Resolve ./GameFactory from file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
14:50:43.366 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts as cce:/internal/x/cc.
14:50:43.366 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts as cce:/internal/x/cc.
14:50:43.366 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts as cce:/internal/x/cc.
14:50:43.366 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.366 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as cce:/internal/x/cc.
14:50:43.366 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as cce:/internal/x/cc.
14:50:43.366 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as cce:/internal/x/cc.
14:50:43.366 debug: Resolve ../EnemyBullet from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts.
14:50:43.367 debug: Resolve ../Global from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.367 debug: Resolve ../PersistNode from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.367 debug: Resolve ./GameFactory from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
14:50:43.367 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.367 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as cce:/internal/x/cc.
14:50:43.367 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as cce:/internal/x/cc.
14:50:43.367 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as cce:/internal/x/cc.
14:50:43.368 debug: Resolve ./Global from file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.368 debug: Resolve ./PersistNode from file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.368 debug: Resolve ./Player from file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as file:///E:/M2Game/Client/assets/scripts/Game/Player.ts.
14:50:43.368 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Global.ts as cce:/internal/x/cc.
14:50:43.368 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Global.ts as cce:/internal/x/cc.
14:50:43.368 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Global.ts as cce:/internal/x/cc.
14:50:43.368 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/Player.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.368 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Player.ts as cce:/internal/x/cc.
14:50:43.368 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Player.ts as cce:/internal/x/cc.
14:50:43.368 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Player.ts as cce:/internal/x/cc.
14:50:43.369 debug: Resolve ./Global from file:///E:/M2Game/Client/assets/scripts/Game/Player.ts as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.369 debug: Resolve ./PersistNode from file:///E:/M2Game/Client/assets/scripts/Game/Player.ts as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.369 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.369 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as cce:/internal/x/cc.
14:50:43.369 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as cce:/internal/x/cc.
14:50:43.369 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as cce:/internal/x/cc.
14:50:43.369 debug: Resolve ../Enemy from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts.
14:50:43.370 debug: Resolve ../Global from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.370 debug: Resolve ../PersistNode from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.370 debug: Resolve ./GameFactory from file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
14:50:43.370 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.370 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts as cce:/internal/x/cc.
14:50:43.370 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts as cce:/internal/x/cc.
14:50:43.370 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts as cce:/internal/x/cc.
14:50:43.371 debug: Resolve ./Global from file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.371 debug: Resolve ./PersistNode from file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.371 debug: Resolve ./Player from file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts as file:///E:/M2Game/Client/assets/scripts/Game/Player.ts.
14:50:43.371 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.371 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as cce:/internal/x/cc.
14:50:43.371 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as cce:/internal/x/cc.
14:50:43.371 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as cce:/internal/x/cc.
14:50:43.372 debug: Resolve ../Global from file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.373 debug: Resolve ../Goods from file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts.
14:50:43.373 debug: Resolve ../PersistNode from file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.373 debug: Resolve ./GameFactory from file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
14:50:43.373 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.373 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts as cce:/internal/x/cc.
14:50:43.373 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts as cce:/internal/x/cc.
14:50:43.373 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts as cce:/internal/x/cc.
14:50:43.373 debug: Resolve ./Global from file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.374 debug: Resolve ./PersistNode from file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.374 debug: Resolve ./Player from file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts as file:///E:/M2Game/Client/assets/scripts/Game/Player.ts.
14:50:43.374 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.374 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as cce:/internal/x/cc.
14:50:43.374 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as cce:/internal/x/cc.
14:50:43.374 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as cce:/internal/x/cc.
14:50:43.375 debug: Resolve ../Global from file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.375 debug: Resolve ../PersistNode from file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.375 debug: Resolve ../PlayerBullet from file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts.
14:50:43.375 debug: Resolve ./GameFactory from file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
14:50:43.376 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.376 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as cce:/internal/x/cc.
14:50:43.376 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as cce:/internal/x/cc.
14:50:43.376 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as cce:/internal/x/cc.
14:50:43.376 debug: Resolve ./Enemy from file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts.
14:50:43.376 debug: Resolve ./Global from file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.377 debug: Resolve ./PersistNode from file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.377 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/Background.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.377 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Background.ts as cce:/internal/x/cc.
14:50:43.377 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Background.ts as cce:/internal/x/cc.
14:50:43.377 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Background.ts as cce:/internal/x/cc.
14:50:43.377 debug: Resolve ./Global from file:///E:/M2Game/Client/assets/scripts/Game/Background.ts as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.377 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.377 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts as cce:/internal/x/cc.
14:50:43.377 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts as cce:/internal/x/cc.
14:50:43.377 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts as cce:/internal/x/cc.
14:50:43.377 debug: Resolve ./Global from file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.377 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.377 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts as cce:/internal/x/cc.
14:50:43.377 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts as cce:/internal/x/cc.
14:50:43.377 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts as cce:/internal/x/cc.
14:50:43.378 debug: Resolve ./Global from file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:43.378 debug: Resolve ./PersistNode from file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:43.378 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Menu.ts as cce:/internal/x/cc.
14:50:43.378 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Menu.ts as cce:/internal/x/cc.
14:50:43.378 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/Menu.ts as cce:/internal/x/cc.
14:50:43.378 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelData.ts as cce:/internal/x/cc.
14:50:43.378 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelData.ts as cce:/internal/x/cc.
14:50:43.378 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelSerializer.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.378 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelSerializer.ts as cce:/internal/x/cc.
14:50:43.378 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.378 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts as cce:/internal/x/cc.
14:50:43.378 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts as cce:/internal/x/cc.
14:50:43.378 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts as cce:/internal/x/cc.
14:50:43.378 debug: Resolve ./LevelData from file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelData.ts.
14:50:43.379 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.379 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts as cce:/internal/x/cc.
14:50:43.379 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts as cce:/internal/x/cc.
14:50:43.379 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts as cce:/internal/x/cc.
14:50:43.379 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.379 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.379 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as cce:/internal/x/cc.
14:50:43.379 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as cce:/internal/x/cc.
14:50:43.379 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as cce:/internal/x/cc.
14:50:43.379 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.380 debug: Resolve ../core/LevelSerializer from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelSerializer.ts.
14:50:43.380 debug: Resolve ../runtime/LevelManager from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts.
14:50:43.380 debug: Resolve ../runtime/CameraManager from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts.
14:50:43.381 debug: Resolve ../runtime/PathManager from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts.
14:50:43.381 debug: Resolve ../runtime/SpawnerSystem from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts.
14:50:43.382 debug: Resolve ../runtime/MapSystem from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts.
14:50:43.382 debug: Resolve ../events/EventSystem from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts.
14:50:43.382 debug: Resolve ./EditorGizmos from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts.
14:50:43.383 debug: Resolve ./PathEditor from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts.
14:50:43.383 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.383 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts as cce:/internal/x/cc.
14:50:43.383 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts as cce:/internal/x/cc.
14:50:43.383 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts as cce:/internal/x/cc.
14:50:43.383 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.383 debug: Resolve ./SubLevelComponent from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts.
14:50:43.383 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.383 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts as cce:/internal/x/cc.
14:50:43.383 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts as cce:/internal/x/cc.
14:50:43.383 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts as cce:/internal/x/cc.
14:50:43.384 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.384 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.384 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts as cce:/internal/x/cc.
14:50:43.384 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts as cce:/internal/x/cc.
14:50:43.384 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts as cce:/internal/x/cc.
14:50:43.384 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.384 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.384 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts as cce:/internal/x/cc.
14:50:43.384 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts as cce:/internal/x/cc.
14:50:43.384 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts as cce:/internal/x/cc.
14:50:43.384 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.385 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.385 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts as cce:/internal/x/cc.
14:50:43.385 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts as cce:/internal/x/cc.
14:50:43.385 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts as cce:/internal/x/cc.
14:50:43.385 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.385 debug: Resolve ./SpawnerComponent from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts.
14:50:43.385 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.386 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts as cce:/internal/x/cc.
14:50:43.386 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts as cce:/internal/x/cc.
14:50:43.386 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts as cce:/internal/x/cc.
14:50:43.386 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.404 debug: Resolve ./PathFollower from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts.
14:50:43.404 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.404 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve ../core/LevelData from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelData.ts.
14:50:43.404 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.404 debug: Resolve ./MapLayerComponent from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts.
14:50:43.404 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.404 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.404 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve ../core/Types from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:43.404 debug: Resolve ../runtime/PathManager from file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts.
14:50:43.404 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/GameInstance.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/GameInstance.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/GameInstance.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/GameInstance.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve ./Luban/LubanMgr from file:///E:/M2Game/Client/assets/scripts/GameInstance.ts as file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts.
14:50:43.404 debug: Resolve ./Network/NetMgr from file:///E:/M2Game/Client/assets/scripts/GameInstance.ts as file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts.
14:50:43.404 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve ../IMgr from file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts as file:///E:/M2Game/Client/assets/scripts/IMgr.ts.
14:50:43.404 debug: Resolve ../AutoGen/Luban/schema from file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts as file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/IMgr.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/IMgr.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/IMgr.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts as cce:/internal/code-quality/cr.mjs.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve ../IMgr from file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts as file:///E:/M2Game/Client/assets/scripts/IMgr.ts.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/MainUI.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/MainUI.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/MainUI.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts as cce:/internal/x/cc.
14:50:43.404 debug: Resolve cc/env from file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts as external dependency cc/env.
14:50:43.407 debug: Target(preview) ends with cost 112.2820999999999ms.
14:50:43.699 debug: Pulling asset-db.
14:50:44.006 debug: Fetch asset-db cost: 306.95380000000023ms.
14:50:44.006 debug: Build iteration starts.
Number of accumulated asset changes: 48
Feature changed: false
14:50:44.007 debug: Target(editor) build started.
14:50:44.008 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:04 GMT+0800 (中国标准时间)
14:50:44.008 debug: Inspect cce:/internal/x/prerequisite-imports
14:50:44.020 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 11.60 ms
14:50:44.021 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
14:50:44.021 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
14:50:44.022 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
14:50:44.022 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
14:50:44.022 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
14:50:44.023 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
14:50:44.023 debug: Resolve file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts.
14:50:44.023 debug: Resolve file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts.
14:50:44.023 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts.
14:50:44.024 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Background.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Background.ts.
14:50:44.024 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts.
14:50:44.024 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts.
14:50:44.024 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts.
14:50:44.024 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Global.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:44.025 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts.
14:50:44.025 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts.
14:50:44.025 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Menu.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Menu.ts.
14:50:44.025 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:44.025 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Player.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Player.ts.
14:50:44.026 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts.
14:50:44.026 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts.
14:50:44.026 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts.
14:50:44.026 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts.
14:50:44.027 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
14:50:44.027 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts.
14:50:44.027 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts.
14:50:44.027 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelData.ts.
14:50:44.028 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelSerializer.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelSerializer.ts.
14:50:44.028 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:44.029 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts.
14:50:44.029 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts.
14:50:44.029 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts.
14:50:44.030 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts.
14:50:44.030 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts.
14:50:44.030 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts.
14:50:44.030 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts.
14:50:44.030 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts.
14:50:44.031 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts.
14:50:44.031 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts.
14:50:44.031 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts.
14:50:44.031 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts.
14:50:44.031 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts.
14:50:44.031 debug: Resolve file:///E:/M2Game/Client/assets/scripts/GameInstance.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/GameInstance.ts.
14:50:44.032 debug: Resolve file:///E:/M2Game/Client/assets/scripts/IMgr.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/IMgr.ts.
14:50:44.032 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts.
14:50:44.032 debug: Resolve file:///E:/M2Game/Client/assets/scripts/MainUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/MainUI.ts.
14:50:44.032 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts.
14:50:44.033 debug: Resolve file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts.
14:50:44.046 debug: Target(editor) ends with cost 39.792599999999766ms.
14:50:44.047 debug: Target(preview) build started.
14:50:44.048 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:04 GMT+0800 (中国标准时间)
14:50:44.048 debug: Inspect cce:/internal/x/prerequisite-imports
14:50:44.057 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 9.00 ms
14:50:44.058 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
14:50:44.059 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
14:50:44.059 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
14:50:44.059 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
14:50:44.060 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
14:50:44.060 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
14:50:44.060 debug: Resolve file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts.
14:50:44.061 debug: Resolve file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts.
14:50:44.061 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts.
14:50:44.061 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Background.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Background.ts.
14:50:44.061 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts.
14:50:44.062 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts.
14:50:44.062 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts.
14:50:44.062 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Global.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Global.ts.
14:50:44.062 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts.
14:50:44.063 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts.
14:50:44.063 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Menu.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Menu.ts.
14:50:44.063 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts.
14:50:44.064 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/Player.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/Player.ts.
14:50:44.064 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts.
14:50:44.064 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts.
14:50:44.064 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts.
14:50:44.065 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts.
14:50:44.065 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
14:50:44.065 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts.
14:50:44.065 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts.
14:50:44.066 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelData.ts.
14:50:44.066 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelSerializer.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelSerializer.ts.
14:50:44.066 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts.
14:50:44.067 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts.
14:50:44.067 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts.
14:50:44.067 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts.
14:50:44.068 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts.
14:50:44.068 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts.
14:50:44.068 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts.
14:50:44.069 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts.
14:50:44.069 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts.
14:50:44.069 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts.
14:50:44.070 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts.
14:50:44.070 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts.
14:50:44.070 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts.
14:50:44.070 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts.
14:50:44.070 debug: Resolve file:///E:/M2Game/Client/assets/scripts/GameInstance.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/GameInstance.ts.
14:50:44.071 debug: Resolve file:///E:/M2Game/Client/assets/scripts/IMgr.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/IMgr.ts.
14:50:44.071 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts.
14:50:44.071 debug: Resolve file:///E:/M2Game/Client/assets/scripts/MainUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/MainUI.ts.
14:50:44.071 debug: Resolve file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts.
14:50:44.072 debug: Resolve file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts.
14:50:44.086 debug: Target(preview) ends with cost 38.98139999999967ms.
