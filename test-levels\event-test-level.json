{"version": "1.0.0", "format": "json", "timestamp": "2025-01-26T10:00:00.000Z", "data": {"metadata": {"name": "Event System Test Level", "version": "1.0.0", "duration": 120, "difficulty": 2, "description": "A test level focused on testing the event system with various triggers and actions", "author": "Test Generator", "createdAt": "2025-01-26T10:00:00.000Z", "modifiedAt": "2025-01-26T10:00:00.000Z"}, "subLevels": [{"id": "sublevel_events", "name": "Event Test Area", "bounds": {"x": 0, "y": 0, "width": 1920, "height": 1500}, "maps": [{"id": "bg_test_area", "prefabPath": "prefabs/backgrounds/test_grid", "layerType": "BG_Mid", "depth": -600, "scrollSpeed": {"x": 0, "y": 0.2}, "repeatMode": "vertical", "offset": {"x": 0, "y": 0}, "bounds": {"x": 0, "y": 0, "width": 1920, "height": 1500}, "isVisible": true}], "spawners": [{"id": "spawner_event_triggered", "name": "Event Triggered Spawner", "position": {"x": 960, "y": 1200, "z": 0}, "prefabPath": "prefabs/enemies/test_drone", "spawnPattern": {"type": "sequential", "entities": [{"prefabPath": "prefabs/enemies/test_drone", "weight": 1.0}], "count": 3, "interval": 1.0, "delay": 0}, "pathId": "path_test_straight", "isActive": false, "waves": [{"id": "wave_event_test", "name": "Event Test Wave", "startTime": 0, "endTime": 10.0, "spawnerConfigs": [{"spawnerId": "spawner_event_triggered", "spawnTime": 0, "interval": 1.0}], "isActive": true}]}], "paths": [{"id": "path_test_straight", "name": "Test Straight Path", "points": [{"position": {"x": 960, "y": 1200, "z": 0}, "controlPoint1": {"x": 960, "y": 1100, "z": 0}, "controlPoint2": {"x": 960, "y": 1000, "z": 0}, "speed": 150, "rotation": 0}, {"position": {"x": 960, "y": 200, "z": 0}, "controlPoint1": {"x": 960, "y": 300, "z": 0}, "controlPoint2": {"x": 960, "y": 100, "z": 0}, "speed": 150, "rotation": 0}], "isLoop": false, "totalLength": 1000}], "events": {"nodes": {}, "connections": [], "triggers": [{"id": "trigger_time_10s", "type": "time", "condition": {"timeReached": 10}, "targetNodeId": "node_activate_spawner", "isActive": true}, {"id": "trigger_time_30s", "type": "time", "condition": {"timeReached": 30}, "targetNodeId": "node_play_warning_sound", "isActive": true}, {"id": "trigger_player_damaged", "type": "condition", "condition": {"eventType": "player_damaged"}, "targetNodeId": "node_screen_flash", "isActive": true}]}, "entryPoint": {"position": {"x": 960, "y": 100, "z": 0}, "entryType": "smooth", "showCardSelection": false, "entryDelay": 1.0}, "exitPoint": {"position": {"x": 960, "y": 1400, "z": 0}, "exitType": "smooth", "showCardSelection": false, "exitDelay": 1.0}, "connections": []}], "globalEvents": {"nodes": {}, "connections": [], "triggers": [{"id": "trigger_level_start", "type": "time", "condition": {"timeReached": 0}, "targetNodeId": "node_start_background_music", "isActive": true}, {"id": "trigger_level_halfway", "type": "time", "condition": {"timeReached": 60}, "targetNodeId": "node_intensity_increase", "isActive": true}, {"id": "trigger_level_complete", "type": "condition", "condition": {"eventType": "sublevel_exit", "subLevelId": "sublevel_events"}, "targetNodeId": "node_victory_fanfare", "isActive": true}]}, "cameraSettings": {"viewportSize": {"width": 1920, "height": 1080}, "scrollSpeed": 80, "followTarget": "player", "smoothing": 4.0, "bounds": {"x": 0, "y": 0, "width": 1920, "height": 1500}}}}