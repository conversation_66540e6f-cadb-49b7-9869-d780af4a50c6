/**
 * Toolbar Actions Component
 * Handles toolbar button actions and mode switching
 */
class ToolbarActions {
  constructor(container, eventBus, editorState) {
    this.container = container;
    this.eventBus = eventBus;
    this.editorState = editorState;
    
    this.buttons = new Map();
    this.currentMode = 'select';
    
    this.initialize();
  }

  /**
   * Initialize the component
   */
  initialize() {
    this.setupButtons();
    this.setupEventHandlers();
    this.updateButtonStates();
  }

  /**
   * Setup toolbar buttons
   */
  setupButtons() {
    // File operations
    this.buttons.set('new-level', this.container.querySelector('#btn-new-level'));
    this.buttons.set('load-level', this.container.querySelector('#btn-load-level'));
    this.buttons.set('save-level', this.container.querySelector('#btn-save-level'));
    
    // Edit modes
    this.buttons.set('select-mode', this.container.querySelector('#btn-select-mode'));
    this.buttons.set('add-sublevel', this.container.querySelector('#btn-add-sublevel'));
    this.buttons.set('add-spawner', this.container.querySelector('#btn-add-spawner'));
    this.buttons.set('add-path', this.container.querySelector('#btn-add-path'));
    
    // Preview
    this.buttons.set('preview', this.container.querySelector('#btn-preview'));
    this.buttons.set('stop-preview', this.container.querySelector('#btn-stop-preview'));
    
    // Status
    this.statusElement = this.container.querySelector('#level-status');
    
    // Add click handlers
    this.buttons.forEach((button, action) => {
      if (button) {
        button.addEventListener('click', () => this.handleAction(action));
      }
    });
  }

  /**
   * Setup event handlers
   */
  setupEventHandlers() {
    // Listen for level data changes
    this.eventBus.onLevelDataChanged(this.onLevelDataChanged, this);
    
    // Listen for edit mode changes
    this.eventBus.onEditModeChanged(this.onEditModeChanged, this);
    
    // Listen for level modified state
    this.eventBus.on('level-modified', this.onLevelModified, this);
    
    // Listen for preview mode changes
    this.eventBus.on('preview-mode-changed', this.onPreviewModeChanged, this);
  }

  /**
   * Handle toolbar action
   * @param {string} action - Action name
   */
  handleAction(action) {
    switch (action) {
      case 'new-level':
        this.newLevel();
        break;
      case 'load-level':
        this.loadLevel();
        break;
      case 'save-level':
        this.saveLevel();
        break;
      case 'select-mode':
        this.setEditMode('select');
        break;
      case 'add-sublevel':
        this.setEditMode('add-sublevel');
        break;
      case 'add-spawner':
        this.setEditMode('add-spawner');
        break;
      case 'add-path':
        this.setEditMode('add-path');
        break;
      case 'preview':
        this.startPreview();
        break;
      case 'stop-preview':
        this.stopPreview();
        break;
    }
  }

  /**
   * Create new level
   */
  newLevel() {
    // Check for unsaved changes
    if (this.editorState.isLevelModified()) {
      if (!confirm('You have unsaved changes. Create new level anyway?')) {
        return;
      }
    }
    
    // Send message to main process
    this.eventBus.emit('send-to-main', 'new-level');
  }

  /**
   * Load level
   */
  loadLevel() {
    // Check for unsaved changes
    if (this.editorState.isLevelModified()) {
      if (!confirm('You have unsaved changes. Load level anyway?')) {
        return;
      }
    }
    
    // Send message to main process
    this.eventBus.emit('send-to-main', 'load-level');
  }

  /**
   * Save level
   */
  saveLevel() {
    const currentLevel = this.editorState.getCurrentLevel();
    if (!currentLevel) {
      alert('No level to save');
      return;
    }
    
    // Send level data to main process
    this.eventBus.emit('send-to-main', 'sync-level-data', currentLevel);
    this.eventBus.emit('send-to-main', 'save-level');
  }

  /**
   * Set edit mode
   * @param {string} mode - Edit mode
   */
  setEditMode(mode) {
    this.currentMode = mode;
    this.editorState.setEditMode(mode);
    this.updateModeButtons();
  }

  /**
   * Start preview mode
   */
  startPreview() {
    const currentLevel = this.editorState.getCurrentLevel();
    if (!currentLevel) {
      alert('No level to preview');
      return;
    }
    
    this.editorState.setPreviewMode(true);
    this.eventBus.emit('start-preview', currentLevel);
  }

  /**
   * Stop preview mode
   */
  stopPreview() {
    this.editorState.setPreviewMode(false);
    this.eventBus.emit('stop-preview');
  }

  /**
   * Handle level data changed
   * @param {Object} levelData - New level data
   */
  onLevelDataChanged(levelData) {
    this.updateStatus(levelData);
    this.updateButtonStates();
  }

  /**
   * Handle edit mode changed
   * @param {Object} modeData - Mode change data
   */
  onEditModeChanged(modeData) {
    this.currentMode = modeData.mode;
    this.updateModeButtons();
  }

  /**
   * Handle level modified state changed
   * @param {boolean} isModified - Whether level is modified
   */
  onLevelModified(isModified) {
    this.updateStatus();
  }

  /**
   * Handle preview mode changed
   * @param {boolean} isPreview - Whether in preview mode
   */
  onPreviewModeChanged(isPreview) {
    this.updatePreviewButtons(isPreview);
    this.updateButtonStates();
  }

  /**
   * Update status display
   * @param {Object} levelData - Level data (optional)
   */
  updateStatus(levelData = null) {
    if (!this.statusElement) return;
    
    const currentLevel = levelData || this.editorState.getCurrentLevel();
    const isModified = this.editorState.isLevelModified();
    
    if (currentLevel) {
      const modifiedIndicator = isModified ? ' *' : '';
      this.statusElement.textContent = `${currentLevel.metadata.name}${modifiedIndicator}`;
    } else {
      this.statusElement.textContent = 'No level loaded';
    }
  }

  /**
   * Update button states
   */
  updateButtonStates() {
    const hasLevel = this.editorState.getCurrentLevel() !== null;
    const isPreview = this.editorState.isInPreviewMode();
    
    // Enable/disable buttons based on state
    this.setButtonEnabled('save-level', hasLevel && !isPreview);
    this.setButtonEnabled('select-mode', hasLevel && !isPreview);
    this.setButtonEnabled('add-sublevel', hasLevel && !isPreview);
    this.setButtonEnabled('add-spawner', hasLevel && !isPreview);
    this.setButtonEnabled('add-path', hasLevel && !isPreview);
    this.setButtonEnabled('preview', hasLevel && !isPreview);
  }

  /**
   * Update mode buttons
   */
  updateModeButtons() {
    // Clear all mode button active states
    const modeButtons = ['select-mode', 'add-sublevel', 'add-spawner', 'add-path'];
    modeButtons.forEach(buttonName => {
      const button = this.buttons.get(buttonName);
      if (button) {
        button.classList.remove('active');
      }
    });
    
    // Set active mode button
    const modeButtonMap = {
      'select': 'select-mode',
      'add-sublevel': 'add-sublevel',
      'add-spawner': 'add-spawner',
      'add-path': 'add-path'
    };
    
    const activeButtonName = modeButtonMap[this.currentMode];
    if (activeButtonName) {
      const activeButton = this.buttons.get(activeButtonName);
      if (activeButton) {
        activeButton.classList.add('active');
      }
    }
  }

  /**
   * Update preview buttons
   * @param {boolean} isPreview - Whether in preview mode
   */
  updatePreviewButtons(isPreview) {
    const previewButton = this.buttons.get('preview');
    const stopButton = this.buttons.get('stop-preview');
    
    if (previewButton && stopButton) {
      previewButton.style.display = isPreview ? 'none' : 'flex';
      stopButton.style.display = isPreview ? 'flex' : 'none';
    }
  }

  /**
   * Set button enabled state
   * @param {string} buttonName - Button name
   * @param {boolean} enabled - Whether button is enabled
   */
  setButtonEnabled(buttonName, enabled) {
    const button = this.buttons.get(buttonName);
    if (button) {
      button.disabled = !enabled;
      if (enabled) {
        button.classList.remove('disabled');
      } else {
        button.classList.add('disabled');
      }
    }
  }

  /**
   * Add custom action handler
   * @param {string} actionName - Action name
   * @param {Function} handler - Action handler
   */
  addActionHandler(actionName, handler) {
    // Store custom handlers for extension
    if (!this.customHandlers) {
      this.customHandlers = new Map();
    }
    this.customHandlers.set(actionName, handler);
  }

  /**
   * Remove custom action handler
   * @param {string} actionName - Action name
   */
  removeActionHandler(actionName) {
    if (this.customHandlers) {
      this.customHandlers.delete(actionName);
    }
  }

  /**
   * Trigger custom action
   * @param {string} actionName - Action name
   * @param {...any} args - Action arguments
   */
  triggerAction(actionName, ...args) {
    if (this.customHandlers && this.customHandlers.has(actionName)) {
      const handler = this.customHandlers.get(actionName);
      handler(...args);
    }
  }

  /**
   * Destroy the component
   */
  destroy() {
    // Remove event listeners
    this.eventBus.off('level-data-changed', this.onLevelDataChanged, this);
    this.eventBus.off('edit-mode-changed', this.onEditModeChanged, this);
    this.eventBus.off('level-modified', this.onLevelModified, this);
    this.eventBus.off('preview-mode-changed', this.onPreviewModeChanged, this);
    
    // Clear references
    this.buttons.clear();
    if (this.customHandlers) {
      this.customHandlers.clear();
    }
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ToolbarActions;
} else {
  window.ToolbarActions = ToolbarActions;
}
