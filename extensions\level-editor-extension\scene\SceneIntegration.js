/**
 * Scene Integration Component
 * Main coordinator for scene view integration with the level editor
 */

const SceneGizmos = require('./SceneGizmos');
const SceneManipulation = require('./SceneManipulation');

class SceneIntegration {
  constructor() {
    this.sceneGizmos = null;
    this.sceneManipulation = null;
    this.isEnabled = false;
    this.levelData = null;
    this.selectedItems = [];
    
    // Integration settings
    this.settings = {
      enableGizmos: true,
      enableManipulation: true,
      showInPlayMode: false,
      autoSave: true,
      undoRedoEnabled: true
    };
    
    this.initialize();
  }

  /**
   * Initialize the scene integration
   */
  initialize() {
    this.createSystems();
    this.setupEventHandlers();
    this.setupEditorHooks();
  }

  /**
   * Create scene systems
   */
  createSystems() {
    // Create gizmo system
    this.sceneGizmos = new SceneGizmos();
    
    // Create manipulation system
    this.sceneManipulation = new SceneManipulation(this.sceneGizmos);
  }

  /**
   * Setup event handlers
   */
  setupEventHandlers() {
    // Listen for manipulation events
    cc.systemEvent.on('level-editor-manipulation-start', this.onManipulationStart, this);
    cc.systemEvent.on('level-editor-manipulation-update', this.onManipulationUpdate, this);
    cc.systemEvent.on('level-editor-manipulation-end', this.onManipulationEnd, this);
    cc.systemEvent.on('level-editor-settings-changed', this.onSettingsChanged, this);
    
    // Listen for editor events
    cc.systemEvent.on('level-editor-enabled', this.onEditorEnabled, this);
    cc.systemEvent.on('level-editor-disabled', this.onEditorDisabled, this);
    cc.systemEvent.on('level-editor-level-changed', this.onLevelChanged, this);
    cc.systemEvent.on('level-editor-selection-changed', this.onSelectionChanged, this);
    
    // Listen for play mode changes
    cc.game.on(cc.game.EVENT_GAME_INITED, this.onGameInited, this);
    cc.director.on(cc.Director.EVENT_BEFORE_SCENE_LOADING, this.onBeforeSceneLoading, this);
    cc.director.on(cc.Director.EVENT_AFTER_SCENE_LAUNCH, this.onAfterSceneLaunch, this);
  }

  /**
   * Setup editor hooks
   */
  setupEditorHooks() {
    // Hook into Cocos Creator's editor systems
    if (CC_EDITOR) {
      this.setupEditorModeHooks();
    }
  }

  /**
   * Setup editor mode hooks
   */
  setupEditorModeHooks() {
    // Listen for editor mode changes
    Editor.on('scene:enter-preview-mode', this.onEnterPreviewMode, this);
    Editor.on('scene:exit-preview-mode', this.onExitPreviewMode, this);
    Editor.on('scene:play-on-device', this.onPlayOnDevice, this);
    
    // Listen for scene view events
    Editor.on('scene:camera-changed', this.onCameraChanged, this);
    Editor.on('scene:gizmo-changed', this.onGizmoChanged, this);
    
    // Listen for selection events
    Editor.on('selection:changed', this.onEditorSelectionChanged, this);
  }

  /**
   * Enable scene integration
   * @param {Object} levelData - Level data
   */
  enable(levelData) {
    this.isEnabled = true;
    this.levelData = levelData;
    
    if (this.settings.enableGizmos) {
      this.sceneGizmos.setEnabled(true);
      this.sceneGizmos.setLevelData(levelData);
    }
    
    if (this.settings.enableManipulation) {
      this.sceneManipulation.setEnabled(true);
    }
    
    // Emit enabled event
    cc.systemEvent.emit('level-editor-enabled');
  }

  /**
   * Disable scene integration
   */
  disable() {
    this.isEnabled = false;
    
    if (this.sceneGizmos) {
      this.sceneGizmos.setEnabled(false);
    }
    
    if (this.sceneManipulation) {
      this.sceneManipulation.setEnabled(false);
    }
    
    // Emit disabled event
    cc.systemEvent.emit('level-editor-disabled');
  }

  /**
   * Update level data
   * @param {Object} levelData - New level data
   */
  setLevelData(levelData) {
    this.levelData = levelData;
    
    if (this.isEnabled && this.sceneGizmos) {
      this.sceneGizmos.setLevelData(levelData);
    }
    
    // Emit level changed event
    cc.systemEvent.emit('level-editor-level-changed', levelData);
  }

  /**
   * Set selected items
   * @param {Array} selectedItems - Selected items
   */
  setSelectedItems(selectedItems) {
    this.selectedItems = selectedItems;
    
    if (this.isEnabled && this.sceneGizmos) {
      this.sceneGizmos.setSelectedItems(selectedItems);
    }
    
    // Emit selection changed event
    cc.systemEvent.emit('level-editor-selection-changed', selectedItems);
  }

  /**
   * Update settings
   * @param {Object} settings - New settings
   */
  updateSettings(settings) {
    Object.assign(this.settings, settings);
    
    if (this.sceneGizmos) {
      this.sceneGizmos.updateSettings(settings);
    }
    
    if (this.sceneManipulation) {
      this.sceneManipulation.updateSettings(settings);
    }
  }

  /**
   * Handle manipulation start
   * @param {Object} event - Manipulation event
   */
  onManipulationStart(event) {
    // Notify panel about manipulation start
    this.notifyPanel('manipulation-start', event);
    
    // Start undo group if enabled
    if (this.settings.undoRedoEnabled) {
      this.startUndoGroup();
    }
  }

  /**
   * Handle manipulation update
   * @param {Object} event - Manipulation event
   */
  onManipulationUpdate(event) {
    // Notify panel about manipulation update
    this.notifyPanel('manipulation-update', event);
    
    // Mark level as modified
    this.markLevelModified();
  }

  /**
   * Handle manipulation end
   * @param {Object} event - Manipulation event
   */
  onManipulationEnd(event) {
    // Notify panel about manipulation end
    this.notifyPanel('manipulation-end', event);
    
    // End undo group if enabled
    if (this.settings.undoRedoEnabled) {
      this.endUndoGroup();
    }
    
    // Auto-save if enabled
    if (this.settings.autoSave) {
      this.autoSave();
    }
  }

  /**
   * Handle settings changed
   * @param {Object} settings - New settings
   */
  onSettingsChanged(settings) {
    this.updateSettings(settings);
  }

  /**
   * Handle editor enabled
   */
  onEditorEnabled() {
    // Update UI state
    this.updateEditorUI(true);
  }

  /**
   * Handle editor disabled
   */
  onEditorDisabled() {
    // Update UI state
    this.updateEditorUI(false);
  }

  /**
   * Handle level changed
   * @param {Object} levelData - New level data
   */
  onLevelChanged(levelData) {
    // Update internal state
    this.levelData = levelData;
  }

  /**
   * Handle selection changed
   * @param {Array} selectedItems - Selected items
   */
  onSelectionChanged(selectedItems) {
    // Update internal state
    this.selectedItems = selectedItems;
  }

  /**
   * Handle game initialized
   */
  onGameInited() {
    // Setup game-specific hooks
  }

  /**
   * Handle before scene loading
   */
  onBeforeSceneLoading() {
    // Disable gizmos during scene loading
    if (this.sceneGizmos) {
      this.sceneGizmos.setEnabled(false);
    }
  }

  /**
   * Handle after scene launch
   */
  onAfterSceneLaunch() {
    // Re-enable gizmos after scene launch
    if (this.isEnabled && this.sceneGizmos) {
      this.sceneGizmos.setEnabled(true);
    }
  }

  /**
   * Handle enter preview mode
   */
  onEnterPreviewMode() {
    if (!this.settings.showInPlayMode) {
      this.disable();
    }
  }

  /**
   * Handle exit preview mode
   */
  onExitPreviewMode() {
    if (this.levelData) {
      this.enable(this.levelData);
    }
  }

  /**
   * Handle play on device
   */
  onPlayOnDevice() {
    // Disable for device play
    this.disable();
  }

  /**
   * Handle camera changed
   */
  onCameraChanged() {
    // Update gizmo scale based on camera
    if (this.sceneGizmos) {
      this.sceneGizmos.updateGizmos();
    }
  }

  /**
   * Handle gizmo changed
   */
  onGizmoChanged() {
    // Handle built-in gizmo changes
  }

  /**
   * Handle editor selection changed
   * @param {Array} selection - Editor selection
   */
  onEditorSelectionChanged(selection) {
    // Sync with editor selection if needed
  }

  /**
   * Notify panel about events
   * @param {string} eventType - Event type
   * @param {Object} data - Event data
   */
  notifyPanel(eventType, data) {
    // Send message to panel
    if (CC_EDITOR) {
      Editor.Ipc.sendToPanel('level-editor', eventType, data);
    }
  }

  /**
   * Start undo group
   */
  startUndoGroup() {
    if (CC_EDITOR) {
      Editor.Undo.recordBegin();
    }
  }

  /**
   * End undo group
   */
  endUndoGroup() {
    if (CC_EDITOR) {
      Editor.Undo.recordEnd();
    }
  }

  /**
   * Mark level as modified
   */
  markLevelModified() {
    // Mark the level data as modified
    this.notifyPanel('level-modified', { levelData: this.levelData });
  }

  /**
   * Auto-save level
   */
  autoSave() {
    // Trigger auto-save
    this.notifyPanel('auto-save', { levelData: this.levelData });
  }

  /**
   * Update editor UI
   * @param {boolean} enabled - Whether editor is enabled
   */
  updateEditorUI(enabled) {
    // Update editor UI state
    if (CC_EDITOR) {
      Editor.Ipc.sendToPanel('level-editor', 'editor-state-changed', { enabled });
    }
  }

  /**
   * Get current scene bounds
   * @returns {Object} Scene bounds
   */
  getSceneBounds() {
    const scene = cc.director.getScene();
    if (!scene) return { x: 0, y: 0, width: 1000, height: 1000 };
    
    // Calculate scene bounds based on content
    let minX = 0, minY = 0, maxX = 1000, maxY = 1000;
    
    // Iterate through scene nodes to find bounds
    scene.children.forEach(child => {
      const worldPos = child.getWorldPosition();
      const contentSize = child.getContentSize();
      
      minX = Math.min(minX, worldPos.x - contentSize.width / 2);
      minY = Math.min(minY, worldPos.y - contentSize.height / 2);
      maxX = Math.max(maxX, worldPos.x + contentSize.width / 2);
      maxY = Math.max(maxY, worldPos.y + contentSize.height / 2);
    });
    
    return {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY
    };
  }

  /**
   * Focus on item in scene
   * @param {Object} item - Item to focus on
   */
  focusOnItem(item) {
    if (!item || !item.data) return;
    
    let position = null;
    
    // Get position based on item type
    switch (item.type) {
      case 'spawner':
      case 'path-point':
      case 'entry-point':
      case 'exit-point':
        position = item.data.position;
        break;
      case 'sublevel':
        const bounds = item.data.bounds;
        position = {
          x: bounds.x + bounds.width / 2,
          y: bounds.y + bounds.height / 2,
          z: 0
        };
        break;
    }
    
    if (position && CC_EDITOR) {
      // Focus camera on position
      Editor.Ipc.sendToPanel('scene', 'scene:focus-on-position', position);
    }
  }

  /**
   * Export scene data
   * @returns {Object} Scene data
   */
  exportSceneData() {
    return {
      levelData: this.levelData,
      selectedItems: this.selectedItems,
      settings: this.settings,
      sceneBounds: this.getSceneBounds()
    };
  }

  /**
   * Import scene data
   * @param {Object} sceneData - Scene data to import
   */
  importSceneData(sceneData) {
    if (sceneData.levelData) {
      this.setLevelData(sceneData.levelData);
    }
    
    if (sceneData.selectedItems) {
      this.setSelectedItems(sceneData.selectedItems);
    }
    
    if (sceneData.settings) {
      this.updateSettings(sceneData.settings);
    }
  }

  /**
   * Destroy the scene integration
   */
  destroy() {
    // Remove event listeners
    cc.systemEvent.off('level-editor-manipulation-start', this.onManipulationStart, this);
    cc.systemEvent.off('level-editor-manipulation-update', this.onManipulationUpdate, this);
    cc.systemEvent.off('level-editor-manipulation-end', this.onManipulationEnd, this);
    cc.systemEvent.off('level-editor-settings-changed', this.onSettingsChanged, this);
    cc.systemEvent.off('level-editor-enabled', this.onEditorEnabled, this);
    cc.systemEvent.off('level-editor-disabled', this.onEditorDisabled, this);
    cc.systemEvent.off('level-editor-level-changed', this.onLevelChanged, this);
    cc.systemEvent.off('level-editor-selection-changed', this.onSelectionChanged, this);
    
    cc.game.off(cc.game.EVENT_GAME_INITED, this.onGameInited, this);
    cc.director.off(cc.Director.EVENT_BEFORE_SCENE_LOADING, this.onBeforeSceneLoading, this);
    cc.director.off(cc.Director.EVENT_AFTER_SCENE_LAUNCH, this.onAfterSceneLaunch, this);
    
    if (CC_EDITOR) {
      Editor.off('scene:enter-preview-mode', this.onEnterPreviewMode, this);
      Editor.off('scene:exit-preview-mode', this.onExitPreviewMode, this);
      Editor.off('scene:play-on-device', this.onPlayOnDevice, this);
      Editor.off('scene:camera-changed', this.onCameraChanged, this);
      Editor.off('scene:gizmo-changed', this.onGizmoChanged, this);
      Editor.off('selection:changed', this.onEditorSelectionChanged, this);
    }
    
    // Destroy systems
    if (this.sceneGizmos) {
      this.sceneGizmos.destroy();
      this.sceneGizmos = null;
    }
    
    if (this.sceneManipulation) {
      this.sceneManipulation.destroy();
      this.sceneManipulation = null;
    }
    
    // Reset state
    this.isEnabled = false;
    this.levelData = null;
    this.selectedItems = [];
  }
}

// Create global instance
let sceneIntegration = null;

/**
 * Get scene integration instance
 * @returns {SceneIntegration} Scene integration instance
 */
function getSceneIntegration() {
  if (!sceneIntegration) {
    sceneIntegration = new SceneIntegration();
  }
  return sceneIntegration;
}

/**
 * Destroy scene integration
 */
function destroySceneIntegration() {
  if (sceneIntegration) {
    sceneIntegration.destroy();
    sceneIntegration = null;
  }
}

module.exports = {
  SceneIntegration,
  getSceneIntegration,
  destroySceneIntegration
};
