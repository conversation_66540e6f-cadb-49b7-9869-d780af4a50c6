import { _decorator, Component, Node, Enum } from "cc";
import { World, WorldInitializeData, WorldState, GameMode, DifficultyLevel } from "./base/World";
import { System } from "./base/System";
const { ccclass, property } = _decorator;

/**
 * Bootstrap component - the main entry point for the World system
 * Extends cc.Component to integrate with Cocos Creator's lifecycle
 * Creates and manages the World instance and drives the world update loop
 */
@ccclass("Bootstrap")
export class Bootstrap extends Component {

    @property({ displayName: "Auto Start", tooltip: "Automatically start the world on component start" })
    public autoStart: boolean = true;

    @property({ displayName: "Game Mode", type: Enum(GameMode), tooltip: "The game mode to initialize" })
    public gameMode: GameMode = GameMode.STORY;

    @property({ displayName: "Level ID", tooltip: "The level identifier to load" })
    public levelId: string = "level_001";

    @property({ displayName: "Difficulty", type: Enum(DifficultyLevel), tooltip: "The difficulty level" })
    public difficulty: DifficultyLevel = DifficultyLevel.NORMAL;

    @property({ displayName: "Random Seed", tooltip: "Random seed for deterministic gameplay (0 = use current time)" })
    public randomSeed: number = 0;

    @property({ displayName: "Time Scale", range: [0.1, 5.0, 0.1], tooltip: "Time scale for the world (1.0 = normal speed)" })
    public timeScale: number = 1.0;

    @property({ displayName: "Enable Debug", tooltip: "Enable debug features" })
    public enableDebug: boolean = false;

    @property({ displayName: "Max Particles", tooltip: "Maximum number of particles" })
    public maxParticles: number = 1000;

    // World instance
    private _world: World | null = null;

    // Initialization data
    private _initializeData: WorldInitializeData | null = null;

    // State tracking
    private _isInitialized: boolean = false;
    private _isPaused: boolean = false;

    /**
     * Component lifecycle - called when component is loaded
     */
    onLoad(): void {
        console.log("Bootstrap: Component loaded");

        // Create world instance
        this._world = new World();

        // Set up world event callbacks
        this._world.setOnStateChanged(this._onWorldStateChanged.bind(this));
        this._world.setOnError(this._onWorldError.bind(this));

        // Prepare initialization data
        this._prepareInitializeData();
    }

    /**
     * Component lifecycle - called when component starts
     */
    start(): void {
        console.log("Bootstrap: Component started");

        if (this.autoStart) {
            this.startWorld();
        }
    }

    /**
     * Component lifecycle - called every frame
     */
    update(deltaTime: number): void {
        if (!this._world || !this._isInitialized || this._isPaused) {
            return;
        }

        // Update the world
        this._world.update(deltaTime);
    }

    /**
     * Component lifecycle - called after all updates
     */
    lateUpdate(): void {
        if (!this._world || !this._isInitialized || this._isPaused) {
            return;
        }

        // Late update the world
        this._world.lateUpdate(0); // LateUpdate doesn't need deltaTime in this context
    }

    /**
     * Component lifecycle - called when component is destroyed
     */
    onDestroy(): void {
        console.log("Bootstrap: Component destroyed");

        if (this._world) {
            this._world.destroy();
            this._world = null;
        }

        this._isInitialized = false;
    }

    /**
     * Start the world with the configured settings
     * @returns Promise that resolves when the world is started
     */
    public async startWorld(): Promise<boolean> {
        if (!this._world || this._isInitialized) {
            console.warn("Bootstrap: Cannot start world - already initialized or no world instance");
            return false;
        }

        if (!this._initializeData) {
            console.error("Bootstrap: Cannot start world - no initialization data");
            return false;
        }

        console.log("Bootstrap: Starting world...");

        const success = await this._world.initialize(this._initializeData);
        if (success) {
            this._isInitialized = true;
            console.log("Bootstrap: World started successfully");
        } else {
            console.error("Bootstrap: Failed to start world");
        }

        return success;
    }

    /**
     * Stop the world
     */
    public stopWorld(): void {
        if (!this._world || !this._isInitialized) {
            console.warn("Bootstrap: Cannot stop world - not initialized");
            return;
        }

        console.log("Bootstrap: Stopping world...");
        this._world.stop();
        this._isInitialized = false;
    }

    /**
     * Pause the world
     */
    public pauseWorld(): void {
        if (!this._world || !this._isInitialized) {
            console.warn("Bootstrap: Cannot pause world - not initialized");
            return;
        }

        this._world.pause();
        this._isPaused = true;
        console.log("Bootstrap: World paused");
    }

    /**
     * Resume the world
     */
    public resumeWorld(): void {
        if (!this._world || !this._isInitialized) {
            console.warn("Bootstrap: Cannot resume world - not initialized");
            return;
        }

        this._world.start();
        this._isPaused = false;
        console.log("Bootstrap: World resumed");
    }

    /**
     * Register a system to the world
     * @param system The system to register
     * @returns true if registration was successful
     */
    public registerSystem(system: System): boolean {
        if (!this._world) {
            console.warn("Bootstrap: Cannot register system - no world instance");
            return false;
        }

        return this._world.registerSystem(system);
    }

    /**
     * Unregister a system from the world
     * @param systemName The name of the system to unregister
     * @returns true if unregistration was successful
     */
    public unregisterSystem(systemName: string): boolean {
        if (!this._world) {
            console.warn("Bootstrap: Cannot unregister system - no world instance");
            return false;
        }

        return this._world.unregisterSystem(systemName);
    }

    /**
     * Get a system by name
     * @param systemName The name of the system to get
     * @returns The system instance or null if not found
     */
    public getSystem<T extends System>(systemName: string): T | null {
        if (!this._world) {
            console.warn("Bootstrap: Cannot get system - no world instance");
            return null;
        }

        return this._world.getSystem<T>(systemName);
    }

    /**
     * Get the world instance
     * @returns The world instance or null if not created
     */
    public getWorld(): World | null {
        return this._world;
    }

    /**
     * Check if the world is initialized
     * @returns true if the world is initialized
     */
    public isWorldInitialized(): boolean {
        return this._isInitialized;
    }

    /**
     * Check if the world is paused
     * @returns true if the world is paused
     */
    public isWorldPaused(): boolean {
        return this._isPaused;
    }

    /**
     * Get the current world state
     * @returns The current world state or null if no world
     */
    public getWorldState(): WorldState | null {
        return this._world ? this._world.getState() : null;
    }

    /**
     * Prepare the initialization data from component properties
     */
    private _prepareInitializeData(): void {
        this._initializeData = new WorldInitializeData();

        // Set basic properties
        this._initializeData.modeId = this.gameMode;
        this._initializeData.levelId = this.levelId;
        this._initializeData.difficulty = this.difficulty;
        this._initializeData.randomSeed = this.randomSeed > 0 ? this.randomSeed : Date.now();

        // Set physics config
        this._initializeData.physicsConfig.timeScale = this.timeScale;

        // Set render config
        this._initializeData.renderConfig.maxParticles = this.maxParticles;

        // Set debug flags
        this._initializeData.debugFlags.enableDebugDraw = this.enableDebug;
        this._initializeData.debugFlags.showCollisionBounds = this.enableDebug;
        this._initializeData.debugFlags.showPerformanceStats = this.enableDebug;
        this._initializeData.debugFlags.logSystemUpdates = this.enableDebug;

        console.log("Bootstrap: Initialization data prepared", this._initializeData);
    }

    /**
     * Handle world state changes
     * @param oldState The previous world state
     * @param newState The new world state
     */
    private _onWorldStateChanged(oldState: WorldState, newState: WorldState): void {
        console.log(`Bootstrap: World state changed from ${oldState} to ${newState}`);

        // Handle specific state transitions
        switch (newState) {
            case WorldState.RUNNING:
                this._isPaused = false;
                break;
            case WorldState.PAUSED:
                this._isPaused = true;
                break;
            case WorldState.STOPPED:
                this._isInitialized = false;
                this._isPaused = false;
                break;
            case WorldState.ERROR:
                this._isInitialized = false;
                this._isPaused = false;
                break;
        }
    }

    /**
     * Handle world errors
     * @param error The error that occurred
     */
    private _onWorldError(error: Error): void {
        console.error("Bootstrap: World error occurred:", error);

        // You can add custom error handling here
        // For example, show error UI, restart world, etc.
    }
}