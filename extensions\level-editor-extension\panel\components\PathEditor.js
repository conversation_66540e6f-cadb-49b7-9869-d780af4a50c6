/**
 * Path Editor Component
 * Specialized editor for path creation and bezier curve editing
 */
class PathEditor {
  constructor(container, eventBus, editorState) {
    this.container = container;
    this.eventBus = eventBus;
    this.editorState = editorState;
    
    this.currentPath = null;
    this.previewCanvas = null;
    this.previewCtx = null;
    this.animationFrame = null;
    this.previewTime = 0;
    this.isPlaying = false;
    
    this.initialize();
  }

  /**
   * Initialize the component
   */
  initialize() {
    this.setupEventHandlers();
    this.createUI();
  }

  /**
   * Setup event handlers
   */
  setupEventHandlers() {
    this.eventBus.onSelectionChanged(this.onSelectionChanged, this);
    this.eventBus.onPropertyChanged(this.onPropertyChanged, this);
  }

  /**
   * Handle selection changed
   * @param {Array} selection - Selected items
   */
  onSelectionChanged(selection) {
    const pathSelection = selection.find(item => item.type === 'path');
    if (pathSelection) {
      this.setPath(pathSelection.data);
    } else {
      this.setPath(null);
    }
  }

  /**
   * Handle property changed
   * @param {Object} changeData - Property change data
   */
  onPropertyChanged(changeData) {
    if (changeData.target === this.currentPath) {
      this.refreshUI();
    }
  }

  /**
   * Set current path
   * @param {Object} path - Path data
   */
  setPath(path) {
    this.currentPath = path;
    this.refreshUI();
  }

  /**
   * Create the UI
   */
  createUI() {
    this.container.innerHTML = `
      <div class="path-editor">
        <div class="path-header">
          <h3>Path Configuration</h3>
          <div class="path-actions">
            <button id="smooth-path" class="btn-small">Smooth</button>
            <button id="reverse-path" class="btn-small">Reverse</button>
          </div>
        </div>
        
        <div class="path-content">
          <!-- Basic Properties -->
          <div class="property-section">
            <h4>Basic Properties</h4>
            <div class="property-grid">
              <label>Name:</label>
              <input type="text" id="path-name" class="property-input">
              
              <label>Is Loop:</label>
              <input type="checkbox" id="path-loop" class="property-checkbox">
              
              <label>Total Length:</label>
              <input type="number" id="path-length" class="property-input" readonly>
              
              <label>Color:</label>
              <input type="color" id="path-color" class="color-picker">
            </div>
          </div>
          
          <!-- Path Points -->
          <div class="property-section">
            <h4>Path Points</h4>
            <div class="points-header">
              <span id="points-count">0 points</span>
              <div class="points-actions">
                <button id="add-point" class="btn-small">Add Point</button>
                <button id="distribute-points" class="btn-small">Distribute</button>
              </div>
            </div>
            <div class="points-list" id="points-list"></div>
          </div>
          
          <!-- Path Settings -->
          <div class="property-section">
            <h4>Path Settings</h4>
            <div class="property-grid">
              <label>Default Speed:</label>
              <input type="number" id="default-speed" class="property-input" min="0" step="0.1" value="1.0">
              
              <label>Smoothness:</label>
              <input type="range" id="path-smoothness" class="property-slider" min="0" max="1" step="0.1" value="0.5">
              
              <label>Auto Tangents:</label>
              <input type="checkbox" id="auto-tangents" class="property-checkbox" checked>
              
              <label>Closed Path:</label>
              <input type="checkbox" id="closed-path" class="property-checkbox">
            </div>
          </div>
          
          <!-- Preview -->
          <div class="property-section">
            <h4>Path Preview</h4>
            <div class="path-preview">
              <canvas id="path-preview-canvas" width="300" height="200"></canvas>
              <div class="preview-controls">
                <button id="play-path" class="btn-small">Play</button>
                <button id="stop-path" class="btn-small">Stop</button>
                <button id="reset-path" class="btn-small">Reset</button>
                <div class="preview-info">
                  <span id="preview-time">0.0s</span>
                  <span id="preview-progress">0%</span>
                </div>
              </div>
              <div class="preview-settings">
                <label>Speed:</label>
                <input type="range" id="preview-speed" class="property-slider" min="0.1" max="3" step="0.1" value="1">
                <label>Show Tangents:</label>
                <input type="checkbox" id="show-tangents" class="property-checkbox" checked>
              </div>
            </div>
          </div>
        </div>
        
        <div class="path-footer">
          <button id="apply-path" class="btn-primary">Apply Changes</button>
          <button id="revert-path" class="btn-secondary">Revert</button>
        </div>
      </div>
    `;
    
    this.setupUIEventHandlers();
    this.createPreviewCanvas();
  }

  /**
   * Setup UI event handlers
   */
  setupUIEventHandlers() {
    // Basic properties
    document.getElementById('path-name').addEventListener('input', this.onNameChanged.bind(this));
    document.getElementById('path-loop').addEventListener('change', this.onLoopChanged.bind(this));
    document.getElementById('path-color').addEventListener('input', this.onColorChanged.bind(this));
    
    // Path settings
    document.getElementById('default-speed').addEventListener('input', this.onDefaultSpeedChanged.bind(this));
    document.getElementById('path-smoothness').addEventListener('input', this.onSmoothnessChanged.bind(this));
    document.getElementById('auto-tangents').addEventListener('change', this.onAutoTangentsChanged.bind(this));
    document.getElementById('closed-path').addEventListener('change', this.onClosedPathChanged.bind(this));
    
    // Point management
    document.getElementById('add-point').addEventListener('click', this.onAddPoint.bind(this));
    document.getElementById('distribute-points').addEventListener('click', this.onDistributePoints.bind(this));
    
    // Path actions
    document.getElementById('smooth-path').addEventListener('click', this.onSmoothPath.bind(this));
    document.getElementById('reverse-path').addEventListener('click', this.onReversePath.bind(this));
    
    // Preview controls
    document.getElementById('play-path').addEventListener('click', this.onPlayPreview.bind(this));
    document.getElementById('stop-path').addEventListener('click', this.onStopPreview.bind(this));
    document.getElementById('reset-path').addEventListener('click', this.onResetPreview.bind(this));
    document.getElementById('preview-speed').addEventListener('input', this.onPreviewSpeedChanged.bind(this));
    document.getElementById('show-tangents').addEventListener('change', this.onShowTangentsChanged.bind(this));
    
    // Actions
    document.getElementById('apply-path').addEventListener('click', this.onApplyChanges.bind(this));
    document.getElementById('revert-path').addEventListener('click', this.onRevertChanges.bind(this));
  }

  /**
   * Create preview canvas
   */
  createPreviewCanvas() {
    this.previewCanvas = document.getElementById('path-preview-canvas');
    this.previewCtx = this.previewCanvas.getContext('2d');
    this.renderPreview();
  }

  /**
   * Refresh UI with current path data
   */
  refreshUI() {
    if (!this.currentPath) {
      this.container.style.display = 'none';
      return;
    }
    
    this.container.style.display = 'block';
    
    // Update basic properties
    document.getElementById('path-name').value = this.currentPath.name || '';
    document.getElementById('path-loop').checked = this.currentPath.isLoop || false;
    document.getElementById('path-length').value = this.calculatePathLength().toFixed(2);
    
    // Update color
    const color = this.currentPath.editorData?.color || '#00ff88';
    document.getElementById('path-color').value = color;
    
    // Update path settings
    document.getElementById('default-speed').value = this.currentPath.defaultSpeed || 1.0;
    document.getElementById('path-smoothness').value = this.currentPath.smoothness || 0.5;
    document.getElementById('auto-tangents').checked = this.currentPath.autoTangents !== false;
    document.getElementById('closed-path').checked = this.currentPath.isLoop || false;
    
    // Update points list
    this.refreshPointsList();
    
    // Update preview
    this.renderPreview();
  }

  /**
   * Refresh points list
   */
  refreshPointsList() {
    const pointsList = document.getElementById('points-list');
    const pointsCount = document.getElementById('points-count');
    
    if (!this.currentPath || !this.currentPath.points) {
      pointsList.innerHTML = '<div class="no-points">No points defined</div>';
      pointsCount.textContent = '0 points';
      return;
    }
    
    const points = this.currentPath.points;
    pointsCount.textContent = `${points.length} points`;
    
    pointsList.innerHTML = '';
    points.forEach((point, index) => {
      const pointElement = this.createPointElement(point, index);
      pointsList.appendChild(pointElement);
    });
  }

  /**
   * Create point element
   * @param {Object} point - Point data
   * @param {number} index - Point index
   * @returns {HTMLElement} Point element
   */
  createPointElement(point, index) {
    const pointDiv = document.createElement('div');
    pointDiv.className = 'point-item';
    pointDiv.innerHTML = `
      <div class="point-header">
        <span>Point ${index + 1}</span>
        <div class="point-actions">
          <button class="move-up" data-index="${index}" ${index === 0 ? 'disabled' : ''}>↑</button>
          <button class="move-down" data-index="${index}" ${index === this.currentPath.points.length - 1 ? 'disabled' : ''}>↓</button>
          <button class="remove-point" data-index="${index}">×</button>
        </div>
      </div>
      <div class="point-properties">
        <div class="property-row">
          <label>Position:</label>
          <div class="vector3-input">
            <input type="number" step="any" value="${point.position.x}" data-prop="position.x" data-index="${index}" placeholder="X">
            <input type="number" step="any" value="${point.position.y}" data-prop="position.y" data-index="${index}" placeholder="Y">
            <input type="number" step="any" value="${point.position.z}" data-prop="position.z" data-index="${index}" placeholder="Z">
          </div>
        </div>
        <div class="property-row">
          <label>Speed:</label>
          <input type="number" step="0.1" min="0" value="${point.speed || 1.0}" data-prop="speed" data-index="${index}">
        </div>
        <div class="property-row">
          <label>Rotation:</label>
          <input type="number" step="1" value="${point.rotation || 0}" data-prop="rotation" data-index="${index}">
        </div>
        <div class="tangent-controls" ${this.currentPath.autoTangents ? 'style="display: none;"' : ''}>
          <div class="property-row">
            <label>In Tangent:</label>
            <div class="vector3-input">
              <input type="number" step="any" value="${point.controlPoint1?.x || 0}" data-prop="controlPoint1.x" data-index="${index}" placeholder="X">
              <input type="number" step="any" value="${point.controlPoint1?.y || 0}" data-prop="controlPoint1.y" data-index="${index}" placeholder="Y">
              <input type="number" step="any" value="${point.controlPoint1?.z || 0}" data-prop="controlPoint1.z" data-index="${index}" placeholder="Z">
            </div>
          </div>
          <div class="property-row">
            <label>Out Tangent:</label>
            <div class="vector3-input">
              <input type="number" step="any" value="${point.controlPoint2?.x || 0}" data-prop="controlPoint2.x" data-index="${index}" placeholder="X">
              <input type="number" step="any" value="${point.controlPoint2?.y || 0}" data-prop="controlPoint2.y" data-index="${index}" placeholder="Y">
              <input type="number" step="any" value="${point.controlPoint2?.z || 0}" data-prop="controlPoint2.z" data-index="${index}" placeholder="Z">
            </div>
          </div>
        </div>
      </div>
    `;
    
    // Add event listeners
    pointDiv.querySelector('.move-up').addEventListener('click', () => this.movePoint(index, -1));
    pointDiv.querySelector('.move-down').addEventListener('click', () => this.movePoint(index, 1));
    pointDiv.querySelector('.remove-point').addEventListener('click', () => this.removePoint(index));
    
    pointDiv.querySelectorAll('input[data-prop]').forEach(input => {
      input.addEventListener('input', () => {
        const prop = input.dataset.prop;
        const pointIndex = parseInt(input.dataset.index);
        const value = parseFloat(input.value) || 0;
        
        this.setPointProperty(pointIndex, prop, value);
      });
    });
    
    return pointDiv;
  }

  /**
   * Set point property
   * @param {number} pointIndex - Point index
   * @param {string} property - Property path
   * @param {any} value - Property value
   */
  setPointProperty(pointIndex, property, value) {
    if (!this.currentPath || !this.currentPath.points[pointIndex]) return;
    
    const point = this.currentPath.points[pointIndex];
    const parts = property.split('.');
    
    if (parts.length === 1) {
      point[parts[0]] = value;
    } else if (parts.length === 2) {
      if (!point[parts[0]]) {
        point[parts[0]] = {};
      }
      point[parts[0]][parts[1]] = value;
    }
    
    // Auto-calculate tangents if enabled
    if (this.currentPath.autoTangents && property.startsWith('position')) {
      this.calculateAutoTangents(pointIndex);
    }
    
    this.editorState.markModified();
    this.renderPreview();
  }

  /**
   * Calculate automatic tangents for a point
   * @param {number} pointIndex - Point index
   */
  calculateAutoTangents(pointIndex) {
    const points = this.currentPath.points;
    if (!points || points.length < 2) return;
    
    const point = points[pointIndex];
    const prevPoint = points[pointIndex - 1];
    const nextPoint = points[pointIndex + 1];
    
    if (prevPoint && nextPoint) {
      // Calculate tangent based on neighboring points
      const tangentX = (nextPoint.position.x - prevPoint.position.x) * 0.25;
      const tangentY = (nextPoint.position.y - prevPoint.position.y) * 0.25;
      const tangentZ = (nextPoint.position.z - prevPoint.position.z) * 0.25;
      
      point.controlPoint1 = { x: -tangentX, y: -tangentY, z: -tangentZ };
      point.controlPoint2 = { x: tangentX, y: tangentY, z: tangentZ };
    } else if (nextPoint) {
      // First point
      const tangentX = (nextPoint.position.x - point.position.x) * 0.25;
      const tangentY = (nextPoint.position.y - point.position.y) * 0.25;
      const tangentZ = (nextPoint.position.z - point.position.z) * 0.25;
      
      point.controlPoint1 = { x: 0, y: 0, z: 0 };
      point.controlPoint2 = { x: tangentX, y: tangentY, z: tangentZ };
    } else if (prevPoint) {
      // Last point
      const tangentX = (point.position.x - prevPoint.position.x) * 0.25;
      const tangentY = (point.position.y - prevPoint.position.y) * 0.25;
      const tangentZ = (point.position.z - prevPoint.position.z) * 0.25;
      
      point.controlPoint1 = { x: -tangentX, y: -tangentY, z: -tangentZ };
      point.controlPoint2 = { x: 0, y: 0, z: 0 };
    }
  }

  /**
   * Calculate total path length
   * @returns {number} Path length
   */
  calculatePathLength() {
    if (!this.currentPath || !this.currentPath.points || this.currentPath.points.length < 2) {
      return 0;
    }
    
    let totalLength = 0;
    const points = this.currentPath.points;
    
    for (let i = 1; i < points.length; i++) {
      const p1 = points[i - 1].position;
      const p2 = points[i].position;
      
      const dx = p2.x - p1.x;
      const dy = p2.y - p1.y;
      const dz = p2.z - p1.z;
      
      totalLength += Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
    
    // Add closing segment if loop
    if (this.currentPath.isLoop && points.length > 2) {
      const p1 = points[points.length - 1].position;
      const p2 = points[0].position;
      
      const dx = p2.x - p1.x;
      const dy = p2.y - p1.y;
      const dz = p2.z - p1.z;
      
      totalLength += Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
    
    return totalLength;
  }

  /**
   * Render preview
   */
  renderPreview() {
    if (!this.previewCtx || !this.currentPath) return;
    
    const canvas = this.previewCanvas;
    const ctx = this.previewCtx;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw background
    ctx.fillStyle = '#1e1e1e';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    if (!this.currentPath.points || this.currentPath.points.length === 0) {
      ctx.fillStyle = '#666666';
      ctx.font = '14px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('No path points', canvas.width / 2, canvas.height / 2);
      return;
    }
    
    // Calculate bounds for fitting
    const bounds = this.calculatePathBounds();
    const scale = Math.min(
      (canvas.width - 40) / bounds.width,
      (canvas.height - 40) / bounds.height
    );
    const offsetX = (canvas.width - bounds.width * scale) / 2 - bounds.minX * scale;
    const offsetY = (canvas.height - bounds.height * scale) / 2 - bounds.minY * scale;
    
    // Transform function
    const transform = (point) => ({
      x: point.x * scale + offsetX,
      y: point.y * scale + offsetY
    });
    
    // Draw path
    this.drawPath(ctx, transform);
    
    // Draw points
    this.drawPoints(ctx, transform);
    
    // Draw tangents if enabled
    if (document.getElementById('show-tangents')?.checked) {
      this.drawTangents(ctx, transform);
    }
    
    // Draw animation if playing
    if (this.isPlaying) {
      this.drawAnimation(ctx, transform);
    }
  }

  /**
   * Calculate path bounds
   * @returns {Object} Bounds {minX, minY, maxX, maxY, width, height}
   */
  calculatePathBounds() {
    const points = this.currentPath.points;
    if (!points || points.length === 0) {
      return { minX: 0, minY: 0, maxX: 100, maxY: 100, width: 100, height: 100 };
    }
    
    let minX = points[0].position.x;
    let minY = points[0].position.y;
    let maxX = points[0].position.x;
    let maxY = points[0].position.y;
    
    points.forEach(point => {
      minX = Math.min(minX, point.position.x);
      minY = Math.min(minY, point.position.y);
      maxX = Math.max(maxX, point.position.x);
      maxY = Math.max(maxY, point.position.y);
    });
    
    // Add some padding
    const padding = 50;
    minX -= padding;
    minY -= padding;
    maxX += padding;
    maxY += padding;
    
    return {
      minX, minY, maxX, maxY,
      width: maxX - minX,
      height: maxY - minY
    };
  }

  /**
   * Draw path curve
   * @param {CanvasRenderingContext2D} ctx - Canvas context
   * @param {Function} transform - Transform function
   */
  drawPath(ctx, transform) {
    const points = this.currentPath.points;
    if (points.length < 2) return;
    
    ctx.strokeStyle = this.currentPath.editorData?.color || '#00ff88';
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    
    ctx.beginPath();
    
    const firstPoint = transform(points[0].position);
    ctx.moveTo(firstPoint.x, firstPoint.y);
    
    for (let i = 1; i < points.length; i++) {
      const prevPoint = points[i - 1];
      const currPoint = points[i];
      
      const cp1 = transform({
        x: prevPoint.position.x + prevPoint.controlPoint2.x,
        y: prevPoint.position.y + prevPoint.controlPoint2.y
      });
      
      const cp2 = transform({
        x: currPoint.position.x + currPoint.controlPoint1.x,
        y: currPoint.position.y + currPoint.controlPoint1.y
      });
      
      const endPoint = transform(currPoint.position);
      
      ctx.bezierCurveTo(cp1.x, cp1.y, cp2.x, cp2.y, endPoint.x, endPoint.y);
    }
    
    // Close path if loop
    if (this.currentPath.isLoop && points.length > 2) {
      const lastPoint = points[points.length - 1];
      const firstPoint = points[0];
      
      const cp1 = transform({
        x: lastPoint.position.x + lastPoint.controlPoint2.x,
        y: lastPoint.position.y + lastPoint.controlPoint2.y
      });
      
      const cp2 = transform({
        x: firstPoint.position.x + firstPoint.controlPoint1.x,
        y: firstPoint.position.y + firstPoint.controlPoint1.y
      });
      
      const endPoint = transform(firstPoint.position);
      
      ctx.bezierCurveTo(cp1.x, cp1.y, cp2.x, cp2.y, endPoint.x, endPoint.y);
    }
    
    ctx.stroke();
  }

  /**
   * Draw path points
   * @param {CanvasRenderingContext2D} ctx - Canvas context
   * @param {Function} transform - Transform function
   */
  drawPoints(ctx, transform) {
    const points = this.currentPath.points;
    
    points.forEach((point, index) => {
      const pos = transform(point.position);
      
      // Draw point
      ctx.fillStyle = '#ffffff';
      ctx.strokeStyle = '#333333';
      ctx.lineWidth = 2;
      
      ctx.beginPath();
      ctx.arc(pos.x, pos.y, 6, 0, Math.PI * 2);
      ctx.fill();
      ctx.stroke();
      
      // Draw point number
      ctx.fillStyle = '#333333';
      ctx.font = '10px Arial';
      ctx.textAlign = 'center';
      ctx.fillText((index + 1).toString(), pos.x, pos.y + 3);
    });
  }

  /**
   * Draw tangent handles
   * @param {CanvasRenderingContext2D} ctx - Canvas context
   * @param {Function} transform - Transform function
   */
  drawTangents(ctx, transform) {
    const points = this.currentPath.points;
    
    ctx.strokeStyle = '#ffaa00';
    ctx.lineWidth = 1;
    ctx.setLineDash([2, 2]);
    
    points.forEach(point => {
      const pos = transform(point.position);
      
      // In tangent
      if (point.controlPoint1) {
        const cp1 = transform({
          x: point.position.x + point.controlPoint1.x,
          y: point.position.y + point.controlPoint1.y
        });
        
        ctx.beginPath();
        ctx.moveTo(pos.x, pos.y);
        ctx.lineTo(cp1.x, cp1.y);
        ctx.stroke();
        
        // Control point handle
        ctx.fillStyle = '#ffaa00';
        ctx.beginPath();
        ctx.arc(cp1.x, cp1.y, 3, 0, Math.PI * 2);
        ctx.fill();
      }
      
      // Out tangent
      if (point.controlPoint2) {
        const cp2 = transform({
          x: point.position.x + point.controlPoint2.x,
          y: point.position.y + point.controlPoint2.y
        });
        
        ctx.beginPath();
        ctx.moveTo(pos.x, pos.y);
        ctx.lineTo(cp2.x, cp2.y);
        ctx.stroke();
        
        // Control point handle
        ctx.fillStyle = '#ffaa00';
        ctx.beginPath();
        ctx.arc(cp2.x, cp2.y, 3, 0, Math.PI * 2);
        ctx.fill();
      }
    });
    
    ctx.setLineDash([]);
  }

  /**
   * Draw animation
   * @param {CanvasRenderingContext2D} ctx - Canvas context
   * @param {Function} transform - Transform function
   */
  drawAnimation(ctx, transform) {
    // Calculate position along path based on time
    const progress = (this.previewTime % 5) / 5; // 5 second loop
    const position = this.getPositionAtProgress(progress);
    
    if (position) {
      const pos = transform(position);
      
      // Draw animated object
      ctx.fillStyle = '#ff0000';
      ctx.strokeStyle = '#ffffff';
      ctx.lineWidth = 2;
      
      ctx.beginPath();
      ctx.arc(pos.x, pos.y, 8, 0, Math.PI * 2);
      ctx.fill();
      ctx.stroke();
      
      // Update progress display
      document.getElementById('preview-progress').textContent = `${Math.round(progress * 100)}%`;
    }
  }

  /**
   * Get position at progress along path
   * @param {number} progress - Progress (0-1)
   * @returns {Object|null} Position {x, y}
   */
  getPositionAtProgress(progress) {
    const points = this.currentPath.points;
    if (!points || points.length < 2) return null;
    
    const segmentCount = this.currentPath.isLoop ? points.length : points.length - 1;
    const segmentProgress = progress * segmentCount;
    const segmentIndex = Math.floor(segmentProgress);
    const t = segmentProgress - segmentIndex;
    
    const p1 = points[segmentIndex % points.length];
    const p2 = points[(segmentIndex + 1) % points.length];
    
    // Simple linear interpolation for now
    // In a full implementation, you'd use bezier curve interpolation
    return {
      x: p1.position.x + (p2.position.x - p1.position.x) * t,
      y: p1.position.y + (p2.position.y - p1.position.y) * t
    };
  }

  // Event handlers
  onNameChanged(event) {
    if (this.currentPath) {
      this.currentPath.name = event.target.value;
      this.editorState.markModified();
    }
  }

  onLoopChanged(event) {
    if (this.currentPath) {
      this.currentPath.isLoop = event.target.checked;
      this.editorState.markModified();
      this.renderPreview();
    }
  }

  onColorChanged(event) {
    if (this.currentPath) {
      if (!this.currentPath.editorData) {
        this.currentPath.editorData = {};
      }
      this.currentPath.editorData.color = event.target.value;
      this.editorState.markModified();
      this.renderPreview();
    }
  }

  onDefaultSpeedChanged(event) {
    if (this.currentPath) {
      this.currentPath.defaultSpeed = parseFloat(event.target.value) || 1.0;
      this.editorState.markModified();
    }
  }

  onSmoothnessChanged(event) {
    if (this.currentPath) {
      this.currentPath.smoothness = parseFloat(event.target.value) || 0.5;
      this.editorState.markModified();
      this.renderPreview();
    }
  }

  onAutoTangentsChanged(event) {
    if (this.currentPath) {
      this.currentPath.autoTangents = event.target.checked;
      this.editorState.markModified();
      
      if (event.target.checked) {
        // Recalculate all tangents
        this.currentPath.points.forEach((_, index) => {
          this.calculateAutoTangents(index);
        });
      }
      
      this.refreshPointsList();
      this.renderPreview();
    }
  }

  onClosedPathChanged(event) {
    if (this.currentPath) {
      this.currentPath.isLoop = event.target.checked;
      document.getElementById('path-loop').checked = event.target.checked;
      this.editorState.markModified();
      this.renderPreview();
    }
  }

  onAddPoint() {
    if (this.currentPath) {
      if (!this.currentPath.points) {
        this.currentPath.points = [];
      }
      
      // Add point at end of path or at origin if first point
      let newPosition = { x: 0, y: 0, z: 0 };
      if (this.currentPath.points.length > 0) {
        const lastPoint = this.currentPath.points[this.currentPath.points.length - 1];
        newPosition = {
          x: lastPoint.position.x + 100,
          y: lastPoint.position.y,
          z: lastPoint.position.z
        };
      }
      
      const newPoint = {
        position: newPosition,
        controlPoint1: { x: -25, y: 0, z: 0 },
        controlPoint2: { x: 25, y: 0, z: 0 },
        speed: this.currentPath.defaultSpeed || 1.0,
        rotation: 0
      };
      
      this.currentPath.points.push(newPoint);
      this.editorState.markModified();
      this.refreshUI();
    }
  }

  onDistributePoints() {
    if (this.currentPath && this.currentPath.points && this.currentPath.points.length > 2) {
      // Distribute points evenly along a line or circle
      const pointCount = this.currentPath.points.length;
      const radius = 100;
      
      this.currentPath.points.forEach((point, index) => {
        const angle = (index / pointCount) * Math.PI * 2;
        point.position.x = Math.cos(angle) * radius;
        point.position.y = Math.sin(angle) * radius;
        
        if (this.currentPath.autoTangents) {
          this.calculateAutoTangents(index);
        }
      });
      
      this.editorState.markModified();
      this.refreshUI();
    }
  }

  movePoint(index, direction) {
    if (!this.currentPath || !this.currentPath.points) return;
    
    const newIndex = index + direction;
    if (newIndex < 0 || newIndex >= this.currentPath.points.length) return;
    
    // Swap points
    const temp = this.currentPath.points[index];
    this.currentPath.points[index] = this.currentPath.points[newIndex];
    this.currentPath.points[newIndex] = temp;
    
    this.editorState.markModified();
    this.refreshPointsList();
    this.renderPreview();
  }

  removePoint(index) {
    if (!this.currentPath || !this.currentPath.points) return;
    
    if (confirm(`Remove point ${index + 1}?`)) {
      this.currentPath.points.splice(index, 1);
      this.editorState.markModified();
      this.refreshUI();
    }
  }

  onSmoothPath() {
    if (this.currentPath && this.currentPath.points) {
      // Apply smoothing algorithm
      this.currentPath.points.forEach((_, index) => {
        this.calculateAutoTangents(index);
      });
      
      this.editorState.markModified();
      this.refreshPointsList();
      this.renderPreview();
    }
  }

  onReversePath() {
    if (this.currentPath && this.currentPath.points) {
      this.currentPath.points.reverse();
      
      // Reverse control points
      this.currentPath.points.forEach(point => {
        const temp = point.controlPoint1;
        point.controlPoint1 = point.controlPoint2;
        point.controlPoint2 = temp;
      });
      
      this.editorState.markModified();
      this.refreshPointsList();
      this.renderPreview();
    }
  }

  onPlayPreview() {
    this.isPlaying = true;
    this.previewTime = 0;
    this.animatePreview();
    
    document.getElementById('play-path').disabled = true;
    document.getElementById('stop-path').disabled = false;
  }

  onStopPreview() {
    this.isPlaying = false;
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
    }
    
    document.getElementById('play-path').disabled = false;
    document.getElementById('stop-path').disabled = true;
  }

  onResetPreview() {
    this.onStopPreview();
    this.previewTime = 0;
    document.getElementById('preview-time').textContent = '0.0s';
    document.getElementById('preview-progress').textContent = '0%';
    this.renderPreview();
  }

  onPreviewSpeedChanged(event) {
    // Preview speed is handled in animation loop
  }

  onShowTangentsChanged(event) {
    this.renderPreview();
  }

  animatePreview() {
    if (!this.isPlaying) return;
    
    const speed = parseFloat(document.getElementById('preview-speed').value) || 1;
    this.previewTime += 0.016 * speed; // ~60fps
    
    document.getElementById('preview-time').textContent = this.previewTime.toFixed(1) + 's';
    
    this.renderPreview();
    
    this.animationFrame = requestAnimationFrame(() => this.animatePreview());
  }

  onApplyChanges() {
    // Apply changes and emit events
    this.eventBus.emitPropertyChanged('path', this.currentPath, this.currentPath);
    console.log('[PathEditor] Changes applied');
  }

  onRevertChanges() {
    // Revert changes
    this.refreshUI();
    console.log('[PathEditor] Changes reverted');
  }

  /**
   * Destroy the component
   */
  destroy() {
    this.onStopPreview();
    this.eventBus.off('selection-changed', this.onSelectionChanged, this);
    this.eventBus.off('property-changed', this.onPropertyChanged, this);
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PathEditor;
} else {
  window.PathEditor = PathEditor;
}
