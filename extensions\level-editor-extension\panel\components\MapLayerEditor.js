/**
 * Map Layer Editor Component
 * Specialized editor for map layer configuration and parallax settings
 */
class MapLayerEditor {
  constructor(container, eventBus, editorState) {
    this.container = container;
    this.eventBus = eventBus;
    this.editorState = editorState;
    
    this.currentMapLayer = null;
    this.previewCanvas = null;
    this.previewCtx = null;
    this.scrollAnimation = null;
    this.scrollOffset = { x: 0, y: 0 };
    
    this.initialize();
  }

  /**
   * Initialize the component
   */
  initialize() {
    this.setupEventHandlers();
    this.createUI();
  }

  /**
   * Setup event handlers
   */
  setupEventHandlers() {
    this.eventBus.onSelectionChanged(this.onSelectionChanged, this);
    this.eventBus.onPropertyChanged(this.onPropertyChanged, this);
  }

  /**
   * Handle selection changed
   * @param {Array} selection - Selected items
   */
  onSelectionChanged(selection) {
    const mapSelection = selection.find(item => item.type === 'map');
    if (mapSelection) {
      this.setMapLayer(mapSelection.data);
    } else {
      this.setMapLayer(null);
    }
  }

  /**
   * Handle property changed
   * @param {Object} changeData - Property change data
   */
  onPropertyChanged(changeData) {
    if (changeData.target === this.currentMapLayer) {
      this.refreshUI();
    }
  }

  /**
   * Set current map layer
   * @param {Object} mapLayer - Map layer data
   */
  setMapLayer(mapLayer) {
    this.currentMapLayer = mapLayer;
    this.refreshUI();
  }

  /**
   * Create the UI
   */
  createUI() {
    this.container.innerHTML = `
      <div class="map-layer-editor">
        <div class="map-header">
          <h3>Map Layer Configuration</h3>
          <div class="map-actions">
            <button id="preview-scroll" class="btn-small">Preview Scroll</button>
            <button id="fit-bounds" class="btn-small">Fit Bounds</button>
          </div>
        </div>
        
        <div class="map-content">
          <!-- Basic Properties -->
          <div class="property-section">
            <h4>Basic Properties</h4>
            <div class="property-grid">
              <label>Name:</label>
              <input type="text" id="map-name" class="property-input">
              
              <label>Visible:</label>
              <input type="checkbox" id="map-visible" class="property-checkbox">
              
              <label>Prefab Path:</label>
              <div class="file-input">
                <input type="text" id="map-prefab" class="property-input" placeholder="Select prefab...">
                <button id="browse-map-prefab" class="browse-btn">...</button>
              </div>
              
              <label>Texture Path:</label>
              <div class="file-input">
                <input type="text" id="map-texture" class="property-input" placeholder="Select texture...">
                <button id="browse-map-texture" class="browse-btn">...</button>
              </div>
            </div>
          </div>
          
          <!-- Layer Settings -->
          <div class="property-section">
            <h4>Layer Settings</h4>
            <div class="property-grid">
              <label>Layer Type:</label>
              <select id="layer-type" class="property-select">
                <option value="BG_VeryFar">Background - Very Far</option>
                <option value="BG_Far">Background - Far</option>
                <option value="BG_Mid">Background - Mid</option>
                <option value="BG_Close">Background - Close</option>
                <option value="BG_VeryClose">Background - Very Close</option>
                <option value="Player">Player Layer</option>
                <option value="FG_VeryClose">Foreground - Very Close</option>
                <option value="FG_Close">Foreground - Close</option>
              </select>
              
              <label>Depth:</label>
              <input type="number" id="layer-depth" class="property-input" value="0">
              
              <label>Sort Order:</label>
              <input type="number" id="sort-order" class="property-input" value="0">
              
              <label>Alpha:</label>
              <input type="range" id="layer-alpha" class="property-slider" min="0" max="1" step="0.1" value="1">
              <span id="alpha-value">1.0</span>
            </div>
          </div>
          
          <!-- Parallax Settings -->
          <div class="property-section">
            <h4>Parallax Settings</h4>
            <div class="property-grid">
              <label>Scroll Speed:</label>
              <div class="vector2-input" id="scroll-speed">
                <div class="vector-component">
                  <label>X:</label>
                  <input type="number" step="0.1" value="1.0" data-component="x">
                </div>
                <div class="vector-component">
                  <label>Y:</label>
                  <input type="number" step="0.1" value="1.0" data-component="y">
                </div>
              </div>
              
              <label>Offset:</label>
              <div class="vector2-input" id="layer-offset">
                <div class="vector-component">
                  <label>X:</label>
                  <input type="number" step="any" value="0" data-component="x">
                </div>
                <div class="vector-component">
                  <label>Y:</label>
                  <input type="number" step="any" value="0" data-component="y">
                </div>
              </div>
              
              <label>Follow Camera:</label>
              <input type="checkbox" id="follow-camera" class="property-checkbox" checked>
              
              <label>Lock X Axis:</label>
              <input type="checkbox" id="lock-x" class="property-checkbox">
              
              <label>Lock Y Axis:</label>
              <input type="checkbox" id="lock-y" class="property-checkbox">
            </div>
          </div>
          
          <!-- Repeat Settings -->
          <div class="property-section">
            <h4>Repeat Settings</h4>
            <div class="property-grid">
              <label>Repeat Mode:</label>
              <select id="repeat-mode" class="property-select">
                <option value="none">No Repeat</option>
                <option value="horizontal">Horizontal</option>
                <option value="vertical">Vertical</option>
                <option value="both">Both</option>
              </select>
              
              <label>Tile Size:</label>
              <div class="vector2-input" id="tile-size">
                <div class="vector-component">
                  <label>W:</label>
                  <input type="number" min="1" value="256" data-component="width">
                </div>
                <div class="vector-component">
                  <label>H:</label>
                  <input type="number" min="1" value="256" data-component="height">
                </div>
              </div>
              
              <label>Seamless:</label>
              <input type="checkbox" id="seamless-repeat" class="property-checkbox" checked>
              
              <label>Auto Tile:</label>
              <input type="checkbox" id="auto-tile" class="property-checkbox">
            </div>
          </div>
          
          <!-- Bounds -->
          <div class="property-section">
            <h4>Layer Bounds</h4>
            <div class="property-grid">
              <label>Position:</label>
              <div class="vector2-input" id="bounds-position">
                <div class="vector-component">
                  <label>X:</label>
                  <input type="number" step="any" value="0" data-component="x">
                </div>
                <div class="vector-component">
                  <label>Y:</label>
                  <input type="number" step="any" value="0" data-component="y">
                </div>
              </div>
              
              <label>Size:</label>
              <div class="vector2-input" id="bounds-size">
                <div class="vector-component">
                  <label>W:</label>
                  <input type="number" min="1" value="1000" data-component="width">
                </div>
                <div class="vector-component">
                  <label>H:</label>
                  <input type="number" min="1" value="1000" data-component="height">
                </div>
              </div>
              
              <label>Auto Bounds:</label>
              <input type="checkbox" id="auto-bounds" class="property-checkbox">
            </div>
          </div>
          
          <!-- Preview -->
          <div class="property-section">
            <h4>Layer Preview</h4>
            <div class="map-preview">
              <canvas id="map-preview-canvas" width="300" height="200"></canvas>
              <div class="preview-controls">
                <button id="play-scroll" class="btn-small">Play Scroll</button>
                <button id="stop-scroll" class="btn-small">Stop</button>
                <button id="reset-scroll" class="btn-small">Reset</button>
                <div class="scroll-info">
                  <span>Scroll: </span>
                  <span id="scroll-x">0</span>, <span id="scroll-y">0</span>
                </div>
              </div>
              <div class="preview-settings">
                <label>Camera Speed:</label>
                <input type="range" id="camera-speed" class="property-slider" min="0.1" max="5" step="0.1" value="1">
                <label>Show Grid:</label>
                <input type="checkbox" id="show-grid" class="property-checkbox" checked>
              </div>
            </div>
          </div>
          
          <!-- Layer Order -->
          <div class="property-section">
            <h4>Layer Order</h4>
            <div class="layer-order-controls">
              <button id="move-to-front" class="btn-small">Move to Front</button>
              <button id="move-forward" class="btn-small">Move Forward</button>
              <button id="move-backward" class="btn-small">Move Backward</button>
              <button id="move-to-back" class="btn-small">Move to Back</button>
            </div>
            <div class="layer-order-info">
              <span>Current Order: </span>
              <span id="current-order">0</span>
            </div>
          </div>
        </div>
        
        <div class="map-footer">
          <button id="apply-map" class="btn-primary">Apply Changes</button>
          <button id="revert-map" class="btn-secondary">Revert</button>
        </div>
      </div>
    `;
    
    this.setupUIEventHandlers();
    this.createPreviewCanvas();
  }

  /**
   * Setup UI event handlers
   */
  setupUIEventHandlers() {
    // Basic properties
    document.getElementById('map-name').addEventListener('input', this.onNameChanged.bind(this));
    document.getElementById('map-visible').addEventListener('change', this.onVisibleChanged.bind(this));
    document.getElementById('map-prefab').addEventListener('input', this.onPrefabChanged.bind(this));
    document.getElementById('map-texture').addEventListener('input', this.onTextureChanged.bind(this));
    document.getElementById('browse-map-prefab').addEventListener('click', this.onBrowsePrefab.bind(this));
    document.getElementById('browse-map-texture').addEventListener('click', this.onBrowseTexture.bind(this));
    
    // Layer settings
    document.getElementById('layer-type').addEventListener('change', this.onLayerTypeChanged.bind(this));
    document.getElementById('layer-depth').addEventListener('input', this.onDepthChanged.bind(this));
    document.getElementById('sort-order').addEventListener('input', this.onSortOrderChanged.bind(this));
    document.getElementById('layer-alpha').addEventListener('input', this.onAlphaChanged.bind(this));
    
    // Parallax settings
    this.setupVector2Input('scroll-speed', this.onScrollSpeedChanged.bind(this));
    this.setupVector2Input('layer-offset', this.onOffsetChanged.bind(this));
    document.getElementById('follow-camera').addEventListener('change', this.onFollowCameraChanged.bind(this));
    document.getElementById('lock-x').addEventListener('change', this.onLockXChanged.bind(this));
    document.getElementById('lock-y').addEventListener('change', this.onLockYChanged.bind(this));
    
    // Repeat settings
    document.getElementById('repeat-mode').addEventListener('change', this.onRepeatModeChanged.bind(this));
    this.setupVector2Input('tile-size', this.onTileSizeChanged.bind(this));
    document.getElementById('seamless-repeat').addEventListener('change', this.onSeamlessChanged.bind(this));
    document.getElementById('auto-tile').addEventListener('change', this.onAutoTileChanged.bind(this));
    
    // Bounds
    this.setupVector2Input('bounds-position', this.onBoundsPositionChanged.bind(this));
    this.setupVector2Input('bounds-size', this.onBoundsSizeChanged.bind(this));
    document.getElementById('auto-bounds').addEventListener('change', this.onAutoBoundsChanged.bind(this));
    
    // Preview controls
    document.getElementById('play-scroll').addEventListener('click', this.onPlayScroll.bind(this));
    document.getElementById('stop-scroll').addEventListener('click', this.onStopScroll.bind(this));
    document.getElementById('reset-scroll').addEventListener('click', this.onResetScroll.bind(this));
    document.getElementById('camera-speed').addEventListener('input', this.onCameraSpeedChanged.bind(this));
    document.getElementById('show-grid').addEventListener('change', this.onShowGridChanged.bind(this));
    
    // Layer order
    document.getElementById('move-to-front').addEventListener('click', () => this.moveLayer('front'));
    document.getElementById('move-forward').addEventListener('click', () => this.moveLayer('forward'));
    document.getElementById('move-backward').addEventListener('click', () => this.moveLayer('backward'));
    document.getElementById('move-to-back').addEventListener('click', () => this.moveLayer('back'));
    
    // Actions
    document.getElementById('preview-scroll').addEventListener('click', this.onPreviewScroll.bind(this));
    document.getElementById('fit-bounds').addEventListener('click', this.onFitBounds.bind(this));
    document.getElementById('apply-map').addEventListener('click', this.onApplyChanges.bind(this));
    document.getElementById('revert-map').addEventListener('click', this.onRevertChanges.bind(this));
  }

  /**
   * Setup Vector2 input
   * @param {string} containerId - Container ID
   * @param {Function} callback - Change callback
   */
  setupVector2Input(containerId, callback) {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    container.querySelectorAll('input').forEach(input => {
      input.addEventListener('input', callback);
    });
  }

  /**
   * Create preview canvas
   */
  createPreviewCanvas() {
    this.previewCanvas = document.getElementById('map-preview-canvas');
    this.previewCtx = this.previewCanvas.getContext('2d');
    this.renderPreview();
  }

  /**
   * Refresh UI with current map layer data
   */
  refreshUI() {
    if (!this.currentMapLayer) {
      this.container.style.display = 'none';
      return;
    }
    
    this.container.style.display = 'block';
    
    // Update basic properties
    document.getElementById('map-name').value = this.currentMapLayer.editorData?.name || '';
    document.getElementById('map-visible').checked = this.currentMapLayer.isVisible !== false;
    document.getElementById('map-prefab').value = this.currentMapLayer.prefabPath || '';
    document.getElementById('map-texture').value = this.currentMapLayer.texturePath || '';
    
    // Update layer settings
    document.getElementById('layer-type').value = this.currentMapLayer.layerType || 'BG_Mid';
    document.getElementById('layer-depth').value = this.currentMapLayer.depth || 0;
    document.getElementById('sort-order').value = this.currentMapLayer.sortOrder || 0;
    
    const alpha = this.currentMapLayer.alpha || 1.0;
    document.getElementById('layer-alpha').value = alpha;
    document.getElementById('alpha-value').textContent = alpha.toFixed(1);
    
    // Update parallax settings
    this.updateVector2Input('scroll-speed', this.currentMapLayer.scrollSpeed || { x: 1, y: 1 });
    this.updateVector2Input('layer-offset', this.currentMapLayer.offset || { x: 0, y: 0 });
    document.getElementById('follow-camera').checked = this.currentMapLayer.followCamera !== false;
    document.getElementById('lock-x').checked = this.currentMapLayer.lockX || false;
    document.getElementById('lock-y').checked = this.currentMapLayer.lockY || false;
    
    // Update repeat settings
    document.getElementById('repeat-mode').value = this.currentMapLayer.repeatMode || 'none';
    this.updateVector2Input('tile-size', this.currentMapLayer.tileSize || { width: 256, height: 256 });
    document.getElementById('seamless-repeat').checked = this.currentMapLayer.seamlessRepeat !== false;
    document.getElementById('auto-tile').checked = this.currentMapLayer.autoTile || false;
    
    // Update bounds
    const bounds = this.currentMapLayer.bounds || { x: 0, y: 0, width: 1000, height: 1000 };
    this.updateVector2Input('bounds-position', { x: bounds.x, y: bounds.y });
    this.updateVector2Input('bounds-size', { width: bounds.width, height: bounds.height });
    document.getElementById('auto-bounds').checked = this.currentMapLayer.autoBounds || false;
    
    // Update layer order info
    document.getElementById('current-order').textContent = this.currentMapLayer.depth || 0;
    
    this.renderPreview();
  }

  /**
   * Update Vector2 input
   * @param {string} containerId - Container ID
   * @param {Object} value - Vector2 value
   */
  updateVector2Input(containerId, value) {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    container.querySelectorAll('input').forEach(input => {
      const component = input.dataset.component;
      if (value[component] !== undefined) {
        input.value = value[component];
      }
    });
  }

  /**
   * Get Vector2 value from input
   * @param {string} containerId - Container ID
   * @returns {Object} Vector2 value
   */
  getVector2Value(containerId) {
    const container = document.getElementById(containerId);
    if (!container) return { x: 0, y: 0 };
    
    const result = {};
    container.querySelectorAll('input').forEach(input => {
      const component = input.dataset.component;
      result[component] = parseFloat(input.value) || 0;
    });
    
    return result;
  }

  /**
   * Render preview
   */
  renderPreview() {
    if (!this.previewCtx || !this.currentMapLayer) return;
    
    const canvas = this.previewCanvas;
    const ctx = this.previewCtx;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw background
    ctx.fillStyle = '#1e1e1e';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Draw grid if enabled
    if (document.getElementById('show-grid')?.checked) {
      this.drawGrid(ctx);
    }
    
    // Draw layer representation
    this.drawLayer(ctx);
    
    // Draw bounds
    this.drawBounds(ctx);
    
    // Update scroll info
    document.getElementById('scroll-x').textContent = Math.round(this.scrollOffset.x);
    document.getElementById('scroll-y').textContent = Math.round(this.scrollOffset.y);
  }

  /**
   * Draw grid
   * @param {CanvasRenderingContext2D} ctx - Canvas context
   */
  drawGrid(ctx) {
    const gridSize = 20;
    
    ctx.strokeStyle = '#404040';
    ctx.lineWidth = 1;
    ctx.globalAlpha = 0.3;
    
    ctx.beginPath();
    
    // Vertical lines
    for (let x = 0; x <= this.previewCanvas.width; x += gridSize) {
      ctx.moveTo(x, 0);
      ctx.lineTo(x, this.previewCanvas.height);
    }
    
    // Horizontal lines
    for (let y = 0; y <= this.previewCanvas.height; y += gridSize) {
      ctx.moveTo(0, y);
      ctx.lineTo(this.previewCanvas.width, y);
    }
    
    ctx.stroke();
    ctx.globalAlpha = 1.0;
  }

  /**
   * Draw layer representation
   * @param {CanvasRenderingContext2D} ctx - Canvas context
   */
  drawLayer(ctx) {
    const scrollSpeed = this.currentMapLayer.scrollSpeed || { x: 1, y: 1 };
    const offset = this.currentMapLayer.offset || { x: 0, y: 0 };
    const alpha = this.currentMapLayer.alpha || 1.0;
    
    // Calculate layer position based on scroll
    const layerX = offset.x + this.scrollOffset.x * scrollSpeed.x;
    const layerY = offset.y + this.scrollOffset.y * scrollSpeed.y;
    
    // Draw layer as colored rectangle
    const layerColor = this.getLayerColor(this.currentMapLayer.layerType);
    ctx.fillStyle = layerColor;
    ctx.globalAlpha = alpha;
    
    // Draw main layer
    const layerWidth = 100;
    const layerHeight = 60;
    const centerX = this.previewCanvas.width / 2;
    const centerY = this.previewCanvas.height / 2;
    
    ctx.fillRect(
      centerX - layerWidth / 2 + layerX % 50,
      centerY - layerHeight / 2 + layerY % 30,
      layerWidth,
      layerHeight
    );
    
    // Draw repeat tiles if enabled
    const repeatMode = this.currentMapLayer.repeatMode || 'none';
    if (repeatMode !== 'none') {
      ctx.globalAlpha = alpha * 0.5;
      
      if (repeatMode === 'horizontal' || repeatMode === 'both') {
        // Draw horizontal repeats
        for (let i = -2; i <= 2; i++) {
          if (i === 0) continue;
          ctx.fillRect(
            centerX - layerWidth / 2 + layerX % 50 + i * layerWidth,
            centerY - layerHeight / 2 + layerY % 30,
            layerWidth,
            layerHeight
          );
        }
      }
      
      if (repeatMode === 'vertical' || repeatMode === 'both') {
        // Draw vertical repeats
        for (let i = -2; i <= 2; i++) {
          if (i === 0) continue;
          ctx.fillRect(
            centerX - layerWidth / 2 + layerX % 50,
            centerY - layerHeight / 2 + layerY % 30 + i * layerHeight,
            layerWidth,
            layerHeight
          );
        }
      }
    }
    
    ctx.globalAlpha = 1.0;
    
    // Draw layer label
    ctx.fillStyle = '#ffffff';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(
      this.currentMapLayer.layerType || 'Layer',
      centerX + layerX % 50,
      centerY + layerY % 30
    );
  }

  /**
   * Get layer color based on type
   * @param {string} layerType - Layer type
   * @returns {string} Color
   */
  getLayerColor(layerType) {
    const colors = {
      'BG_VeryFar': '#2a4d3a',
      'BG_Far': '#3a5d4a',
      'BG_Mid': '#4a6d5a',
      'BG_Close': '#5a7d6a',
      'BG_VeryClose': '#6a8d7a',
      'Player': '#ff6b35',
      'FG_VeryClose': '#7a9d8a',
      'FG_Close': '#8aad9a'
    };
    
    return colors[layerType] || '#666666';
  }

  /**
   * Draw bounds
   * @param {CanvasRenderingContext2D} ctx - Canvas context
   */
  drawBounds(ctx) {
    const bounds = this.currentMapLayer.bounds || { x: 0, y: 0, width: 1000, height: 1000 };
    
    // Scale bounds to fit preview
    const scale = Math.min(
      this.previewCanvas.width / (bounds.width + 100),
      this.previewCanvas.height / (bounds.height + 100)
    );
    
    const boundsWidth = bounds.width * scale;
    const boundsHeight = bounds.height * scale;
    const boundsX = (this.previewCanvas.width - boundsWidth) / 2;
    const boundsY = (this.previewCanvas.height - boundsHeight) / 2;
    
    // Draw bounds outline
    ctx.strokeStyle = '#007acc';
    ctx.lineWidth = 2;
    ctx.setLineDash([5, 5]);
    ctx.strokeRect(boundsX, boundsY, boundsWidth, boundsHeight);
    ctx.setLineDash([]);
  }

  // Event handlers
  onNameChanged(event) {
    if (this.currentMapLayer) {
      if (!this.currentMapLayer.editorData) {
        this.currentMapLayer.editorData = {};
      }
      this.currentMapLayer.editorData.name = event.target.value;
      this.editorState.markModified();
    }
  }

  onVisibleChanged(event) {
    if (this.currentMapLayer) {
      this.currentMapLayer.isVisible = event.target.checked;
      this.editorState.markModified();
      this.renderPreview();
    }
  }

  onPrefabChanged(event) {
    if (this.currentMapLayer) {
      this.currentMapLayer.prefabPath = event.target.value;
      this.editorState.markModified();
    }
  }

  onTextureChanged(event) {
    if (this.currentMapLayer) {
      this.currentMapLayer.texturePath = event.target.value;
      this.editorState.markModified();
    }
  }

  onBrowsePrefab() {
    this.eventBus.emit('open-file-browser', {
      title: 'Select Map Prefab',
      filters: [{ name: 'Prefab Files', extensions: ['prefab'] }],
      callback: (selectedPath) => {
        if (selectedPath && this.currentMapLayer) {
          this.currentMapLayer.prefabPath = selectedPath;
          document.getElementById('map-prefab').value = selectedPath;
          this.editorState.markModified();
        }
      }
    });
  }

  onBrowseTexture() {
    this.eventBus.emit('open-file-browser', {
      title: 'Select Texture',
      filters: [{ name: 'Image Files', extensions: ['png', 'jpg', 'jpeg'] }],
      callback: (selectedPath) => {
        if (selectedPath && this.currentMapLayer) {
          this.currentMapLayer.texturePath = selectedPath;
          document.getElementById('map-texture').value = selectedPath;
          this.editorState.markModified();
        }
      }
    });
  }

  onLayerTypeChanged(event) {
    if (this.currentMapLayer) {
      this.currentMapLayer.layerType = event.target.value;
      this.editorState.markModified();
      this.renderPreview();
    }
  }

  onDepthChanged(event) {
    if (this.currentMapLayer) {
      this.currentMapLayer.depth = parseInt(event.target.value) || 0;
      this.editorState.markModified();
      document.getElementById('current-order').textContent = this.currentMapLayer.depth;
    }
  }

  onSortOrderChanged(event) {
    if (this.currentMapLayer) {
      this.currentMapLayer.sortOrder = parseInt(event.target.value) || 0;
      this.editorState.markModified();
    }
  }

  onAlphaChanged(event) {
    if (this.currentMapLayer) {
      const alpha = parseFloat(event.target.value) || 1.0;
      this.currentMapLayer.alpha = alpha;
      document.getElementById('alpha-value').textContent = alpha.toFixed(1);
      this.editorState.markModified();
      this.renderPreview();
    }
  }

  onScrollSpeedChanged() {
    if (this.currentMapLayer) {
      this.currentMapLayer.scrollSpeed = this.getVector2Value('scroll-speed');
      this.editorState.markModified();
      this.renderPreview();
    }
  }

  onOffsetChanged() {
    if (this.currentMapLayer) {
      this.currentMapLayer.offset = this.getVector2Value('layer-offset');
      this.editorState.markModified();
      this.renderPreview();
    }
  }

  onFollowCameraChanged(event) {
    if (this.currentMapLayer) {
      this.currentMapLayer.followCamera = event.target.checked;
      this.editorState.markModified();
    }
  }

  onLockXChanged(event) {
    if (this.currentMapLayer) {
      this.currentMapLayer.lockX = event.target.checked;
      this.editorState.markModified();
    }
  }

  onLockYChanged(event) {
    if (this.currentMapLayer) {
      this.currentMapLayer.lockY = event.target.checked;
      this.editorState.markModified();
    }
  }

  onRepeatModeChanged(event) {
    if (this.currentMapLayer) {
      this.currentMapLayer.repeatMode = event.target.value;
      this.editorState.markModified();
      this.renderPreview();
    }
  }

  onTileSizeChanged() {
    if (this.currentMapLayer) {
      this.currentMapLayer.tileSize = this.getVector2Value('tile-size');
      this.editorState.markModified();
    }
  }

  onSeamlessChanged(event) {
    if (this.currentMapLayer) {
      this.currentMapLayer.seamlessRepeat = event.target.checked;
      this.editorState.markModified();
    }
  }

  onAutoTileChanged(event) {
    if (this.currentMapLayer) {
      this.currentMapLayer.autoTile = event.target.checked;
      this.editorState.markModified();
    }
  }

  onBoundsPositionChanged() {
    if (this.currentMapLayer) {
      const position = this.getVector2Value('bounds-position');
      if (!this.currentMapLayer.bounds) {
        this.currentMapLayer.bounds = { x: 0, y: 0, width: 1000, height: 1000 };
      }
      this.currentMapLayer.bounds.x = position.x;
      this.currentMapLayer.bounds.y = position.y;
      this.editorState.markModified();
      this.renderPreview();
    }
  }

  onBoundsSizeChanged() {
    if (this.currentMapLayer) {
      const size = this.getVector2Value('bounds-size');
      if (!this.currentMapLayer.bounds) {
        this.currentMapLayer.bounds = { x: 0, y: 0, width: 1000, height: 1000 };
      }
      this.currentMapLayer.bounds.width = size.width;
      this.currentMapLayer.bounds.height = size.height;
      this.editorState.markModified();
      this.renderPreview();
    }
  }

  onAutoBoundsChanged(event) {
    if (this.currentMapLayer) {
      this.currentMapLayer.autoBounds = event.target.checked;
      this.editorState.markModified();
    }
  }

  onPlayScroll() {
    if (this.scrollAnimation) {
      cancelAnimationFrame(this.scrollAnimation);
    }
    
    this.animateScroll();
    document.getElementById('play-scroll').disabled = true;
    document.getElementById('stop-scroll').disabled = false;
  }

  onStopScroll() {
    if (this.scrollAnimation) {
      cancelAnimationFrame(this.scrollAnimation);
      this.scrollAnimation = null;
    }
    
    document.getElementById('play-scroll').disabled = false;
    document.getElementById('stop-scroll').disabled = true;
  }

  onResetScroll() {
    this.onStopScroll();
    this.scrollOffset = { x: 0, y: 0 };
    this.renderPreview();
  }

  onCameraSpeedChanged() {
    // Camera speed is used in animation loop
  }

  onShowGridChanged() {
    this.renderPreview();
  }

  animateScroll() {
    if (!this.scrollAnimation) return;
    
    const speed = parseFloat(document.getElementById('camera-speed').value) || 1;
    this.scrollOffset.x += speed;
    this.scrollOffset.y += speed * 0.5;
    
    this.renderPreview();
    
    this.scrollAnimation = requestAnimationFrame(() => this.animateScroll());
  }

  moveLayer(direction) {
    if (!this.currentMapLayer) return;
    
    const currentDepth = this.currentMapLayer.depth || 0;
    
    switch (direction) {
      case 'front':
        this.currentMapLayer.depth = 100;
        break;
      case 'forward':
        this.currentMapLayer.depth = currentDepth + 1;
        break;
      case 'backward':
        this.currentMapLayer.depth = currentDepth - 1;
        break;
      case 'back':
        this.currentMapLayer.depth = -100;
        break;
    }
    
    this.editorState.markModified();
    document.getElementById('layer-depth').value = this.currentMapLayer.depth;
    document.getElementById('current-order').textContent = this.currentMapLayer.depth;
  }

  onPreviewScroll() {
    // Implementation for previewing scroll in scene
    console.log('[MapLayerEditor] Preview scroll not yet implemented');
  }

  onFitBounds() {
    if (this.currentMapLayer) {
      // Auto-fit bounds to content
      this.currentMapLayer.bounds = { x: -500, y: -500, width: 1000, height: 1000 };
      this.updateVector2Input('bounds-position', { x: -500, y: -500 });
      this.updateVector2Input('bounds-size', { width: 1000, height: 1000 });
      this.editorState.markModified();
      this.renderPreview();
    }
  }

  onApplyChanges() {
    // Apply changes and emit events
    this.eventBus.emitPropertyChanged('map', this.currentMapLayer, this.currentMapLayer);
    console.log('[MapLayerEditor] Changes applied');
  }

  onRevertChanges() {
    // Revert changes
    this.refreshUI();
    console.log('[MapLayerEditor] Changes reverted');
  }

  /**
   * Destroy the component
   */
  destroy() {
    this.onStopScroll();
    this.eventBus.off('selection-changed', this.onSelectionChanged, this);
    this.eventBus.off('property-changed', this.onPropertyChanged, this);
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = MapLayerEditor;
} else {
  window.MapLayerEditor = MapLayerEditor;
}
