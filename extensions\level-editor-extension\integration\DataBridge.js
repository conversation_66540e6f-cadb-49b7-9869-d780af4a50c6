/**
 * Data Bridge Component
 * Bridges the extension with the existing LevelEditor.ts system
 */
class DataBridge {
  constructor(eventBus, editorState) {
    this.eventBus = eventBus;
    this.editorState = editorState;
    
    // Reference to the existing LevelEditor component
    this.levelEditorComponent = null;
    this.isConnected = false;
    
    // Data synchronization state
    this.syncInProgress = false;
    this.lastSyncTimestamp = 0;
    this.syncQueue = [];
    
    // Integration settings
    this.settings = {
      autoSync: true,
      syncInterval: 1000, // 1 second
      bidirectionalSync: true,
      validateOnSync: true,
      backupOnSync: true
    };
    
    this.initialize();
  }

  /**
   * Initialize the data bridge
   */
  initialize() {
    this.setupEventHandlers();
    this.startSyncTimer();
  }

  /**
   * Setup event handlers
   */
  setupEventHandlers() {
    // Listen for extension events
    this.eventBus.onLevelDataChanged(this.onExtensionLevelChanged, this);
    this.eventBus.onPropertyChanged(this.onExtensionPropertyChanged, this);
    this.eventBus.on('level-modified', this.onExtensionLevelModified, this);
    
    // Listen for main process events
    this.eventBus.on('send-to-main', this.onSendToMain, this);
    
    // Listen for scene integration events
    if (typeof cc !== 'undefined') {
      cc.systemEvent.on('level-editor-manipulation-end', this.onSceneManipulationEnd, this);
    }
  }

  /**
   * Connect to the existing LevelEditor component
   * @param {Object} levelEditorComponent - The LevelEditor component instance
   */
  connectToLevelEditor(levelEditorComponent) {
    this.levelEditorComponent = levelEditorComponent;
    this.isConnected = true;
    
    // Setup bidirectional event listeners
    if (this.settings.bidirectionalSync) {
      this.setupLevelEditorListeners();
    }
    
    // Initial sync
    this.performInitialSync();
    
    console.log('[DataBridge] Connected to LevelEditor component');
  }

  /**
   * Disconnect from the LevelEditor component
   */
  disconnectFromLevelEditor() {
    if (this.levelEditorComponent) {
      this.removeLevelEditorListeners();
    }
    
    this.levelEditorComponent = null;
    this.isConnected = false;
    
    console.log('[DataBridge] Disconnected from LevelEditor component');
  }

  /**
   * Setup listeners for the LevelEditor component
   */
  setupLevelEditorListeners() {
    if (!this.levelEditorComponent) return;
    
    // Listen for level changes from the component
    this.levelEditorComponent.onLevelLoaded = this.onLevelEditorLevelLoaded.bind(this);
    this.levelEditorComponent.onLevelSaved = this.onLevelEditorLevelSaved.bind(this);
    this.levelEditorComponent.onSelectionChanged = this.onLevelEditorSelectionChanged.bind(this);
    this.levelEditorComponent.onEditModeChanged = this.onLevelEditorEditModeChanged.bind(this);
  }

  /**
   * Remove listeners from the LevelEditor component
   */
  removeLevelEditorListeners() {
    if (!this.levelEditorComponent) return;
    
    this.levelEditorComponent.onLevelLoaded = null;
    this.levelEditorComponent.onLevelSaved = null;
    this.levelEditorComponent.onSelectionChanged = null;
    this.levelEditorComponent.onEditModeChanged = null;
  }

  /**
   * Perform initial synchronization
   */
  performInitialSync() {
    if (!this.isConnected) return;
    
    try {
      // Get current level from LevelEditor component
      const currentLevel = this.levelEditorComponent.getCurrentLevel();
      if (currentLevel) {
        // Convert from TypeScript format to extension format
        const extensionLevel = this.convertToExtensionFormat(currentLevel);
        
        // Update extension state
        this.editorState.setCurrentLevel(extensionLevel);
        
        console.log('[DataBridge] Initial sync completed');
      }
    } catch (error) {
      console.error('[DataBridge] Initial sync failed:', error);
    }
  }

  /**
   * Convert LevelEditor data to extension format
   * @param {Object} levelData - LevelEditor format data
   * @returns {Object} Extension format data
   */
  convertToExtensionFormat(levelData) {
    // The data structures are already compatible, but we may need to add
    // extension-specific properties or handle format differences
    const extensionData = JSON.parse(JSON.stringify(levelData));
    
    // Add extension-specific metadata
    if (!extensionData.extensionData) {
      extensionData.extensionData = {
        version: '1.0.0',
        lastModifiedBy: 'level-editor-extension',
        editorSettings: this.editorState.getSettings()
      };
    }
    
    // Ensure all sublevels have required properties
    extensionData.subLevels.forEach(subLevel => {
      if (!subLevel.editorData) {
        subLevel.editorData = {
          expanded: true,
          visible: true,
          locked: false
        };
      }
      
      // Ensure spawners have editor data
      subLevel.spawners.forEach(spawner => {
        if (!spawner.editorData) {
          spawner.editorData = {
            color: '#ff6b35',
            notes: ''
          };
        }
      });
      
      // Ensure paths have editor data
      subLevel.paths.forEach(path => {
        if (!path.editorData) {
          path.editorData = {
            color: '#00ff88',
            showDirection: true,
            showSpeed: false,
            notes: ''
          };
        }
      });
      
      // Ensure maps have editor data
      subLevel.maps.forEach(map => {
        if (!map.editorData) {
          map.editorData = {
            name: map.id,
            color: { r: 255, g: 255, b: 255, a: 255 },
            locked: false
          };
        }
      });
    });
    
    return extensionData;
  }

  /**
   * Convert extension data to LevelEditor format
   * @param {Object} extensionData - Extension format data
   * @returns {Object} LevelEditor format data
   */
  convertToLevelEditorFormat(extensionData) {
    // Remove extension-specific properties
    const levelEditorData = JSON.parse(JSON.stringify(extensionData));
    
    // Remove extension metadata
    delete levelEditorData.extensionData;
    
    // Clean up editor-only properties that shouldn't be in runtime data
    levelEditorData.subLevels.forEach(subLevel => {
      // Keep editorData for now as it's used by the LevelEditor component
      // In a production system, you might want to remove it for runtime builds
      
      subLevel.spawners.forEach(spawner => {
        // Clean up any extension-specific spawner properties
      });
      
      subLevel.paths.forEach(path => {
        // Clean up any extension-specific path properties
      });
      
      subLevel.maps.forEach(map => {
        // Clean up any extension-specific map properties
      });
    });
    
    return levelEditorData;
  }

  /**
   * Sync data from extension to LevelEditor
   * @param {Object} levelData - Level data to sync
   */
  syncToLevelEditor(levelData) {
    if (!this.isConnected || this.syncInProgress) return;
    
    this.syncInProgress = true;
    
    try {
      // Convert to LevelEditor format
      const levelEditorData = this.convertToLevelEditorFormat(levelData);
      
      // Update LevelEditor component
      this.levelEditorComponent.loadLevel(levelEditorData);
      
      console.log('[DataBridge] Synced to LevelEditor component');
    } catch (error) {
      console.error('[DataBridge] Failed to sync to LevelEditor:', error);
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Sync data from LevelEditor to extension
   */
  syncFromLevelEditor() {
    if (!this.isConnected || this.syncInProgress) return;
    
    this.syncInProgress = true;
    
    try {
      // Get current level from LevelEditor
      const levelData = this.levelEditorComponent.getCurrentLevel();
      if (!levelData) return;
      
      // Convert to extension format
      const extensionData = this.convertToExtensionFormat(levelData);
      
      // Update extension state
      this.editorState.setCurrentLevel(extensionData);
      
      console.log('[DataBridge] Synced from LevelEditor component');
    } catch (error) {
      console.error('[DataBridge] Failed to sync from LevelEditor:', error);
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Queue a sync operation
   * @param {string} type - Sync type ('to-leveleditor' or 'from-leveleditor')
   * @param {Object} data - Data to sync
   */
  queueSync(type, data) {
    this.syncQueue.push({
      type: type,
      data: data,
      timestamp: Date.now()
    });
    
    // Process queue if auto-sync is enabled
    if (this.settings.autoSync) {
      this.processSyncQueue();
    }
  }

  /**
   * Process the sync queue
   */
  processSyncQueue() {
    if (this.syncQueue.length === 0 || this.syncInProgress) return;
    
    const syncItem = this.syncQueue.shift();
    
    switch (syncItem.type) {
      case 'to-leveleditor':
        this.syncToLevelEditor(syncItem.data);
        break;
      case 'from-leveleditor':
        this.syncFromLevelEditor();
        break;
    }
    
    // Continue processing queue
    if (this.syncQueue.length > 0) {
      setTimeout(() => this.processSyncQueue(), 100);
    }
  }

  /**
   * Start sync timer
   */
  startSyncTimer() {
    if (this.settings.autoSync && this.settings.syncInterval > 0) {
      setInterval(() => {
        if (this.isConnected && !this.syncInProgress) {
          this.processSyncQueue();
        }
      }, this.settings.syncInterval);
    }
  }

  /**
   * Validate level data
   * @param {Object} levelData - Level data to validate
   * @returns {Object} Validation result
   */
  validateLevelData(levelData) {
    const errors = [];
    const warnings = [];
    
    try {
      // Basic structure validation
      if (!levelData.metadata) {
        errors.push('Missing metadata');
      }
      
      if (!levelData.subLevels || !Array.isArray(levelData.subLevels)) {
        errors.push('Missing or invalid subLevels array');
      }
      
      // Validate each sublevel
      levelData.subLevels.forEach((subLevel, index) => {
        if (!subLevel.id) {
          errors.push(`SubLevel ${index}: Missing ID`);
        }
        
        if (!subLevel.bounds) {
          errors.push(`SubLevel ${index}: Missing bounds`);
        }
        
        // Validate spawners
        subLevel.spawners.forEach((spawner, spawnerIndex) => {
          if (!spawner.id) {
            errors.push(`SubLevel ${index}, Spawner ${spawnerIndex}: Missing ID`);
          }
          
          if (!spawner.position) {
            errors.push(`SubLevel ${index}, Spawner ${spawnerIndex}: Missing position`);
          }
        });
        
        // Validate paths
        subLevel.paths.forEach((path, pathIndex) => {
          if (!path.id) {
            errors.push(`SubLevel ${index}, Path ${pathIndex}: Missing ID`);
          }
          
          if (!path.points || path.points.length < 2) {
            warnings.push(`SubLevel ${index}, Path ${pathIndex}: Path should have at least 2 points`);
          }
        });
      });
      
    } catch (error) {
      errors.push(`Validation error: ${error.message}`);
    }
    
    return {
      isValid: errors.length === 0,
      errors: errors,
      warnings: warnings
    };
  }

  // Event handlers for extension events
  onExtensionLevelChanged(levelData) {
    if (this.settings.bidirectionalSync) {
      this.queueSync('to-leveleditor', levelData);
    }
  }

  onExtensionPropertyChanged(changeData) {
    // Handle individual property changes
    if (this.isConnected && this.settings.autoSync) {
      const currentLevel = this.editorState.getCurrentLevel();
      if (currentLevel) {
        this.queueSync('to-leveleditor', currentLevel);
      }
    }
  }

  onExtensionLevelModified() {
    // Mark as modified in LevelEditor component
    if (this.isConnected && this.levelEditorComponent.markAsModified) {
      this.levelEditorComponent.markAsModified();
    }
  }

  onSendToMain(message, data) {
    // Handle messages that need to be forwarded to the LevelEditor component
    switch (message) {
      case 'save-level':
        if (this.isConnected) {
          try {
            const serializedData = this.levelEditorComponent.saveLevel();
            this.eventBus.emit('level-saved', { data: serializedData });
          } catch (error) {
            console.error('[DataBridge] Failed to save through LevelEditor:', error);
          }
        }
        break;
      
      case 'create-spawner':
        if (this.isConnected) {
          this.createSpawnerInLevelEditor(data);
        }
        break;
      
      case 'create-path':
        if (this.isConnected) {
          this.createPathInLevelEditor(data);
        }
        break;
    }
  }

  onSceneManipulationEnd(event) {
    // Handle scene manipulation completion
    if (this.settings.autoSync) {
      const currentLevel = this.editorState.getCurrentLevel();
      if (currentLevel) {
        this.queueSync('to-leveleditor', currentLevel);
      }
    }
  }

  // Event handlers for LevelEditor component events
  onLevelEditorLevelLoaded(levelData) {
    if (this.settings.bidirectionalSync && !this.syncInProgress) {
      const extensionData = this.convertToExtensionFormat(levelData);
      this.editorState.setCurrentLevel(extensionData);
    }
  }

  onLevelEditorLevelSaved(levelData) {
    // Handle level saved event
    this.eventBus.emit('level-saved', { data: levelData });
  }

  onLevelEditorSelectionChanged(selection) {
    // Sync selection changes
    if (this.settings.bidirectionalSync) {
      this.editorState.setSelection(selection);
    }
  }

  onLevelEditorEditModeChanged(editMode) {
    // Sync edit mode changes
    if (this.settings.bidirectionalSync) {
      this.editorState.setEditMode(editMode);
    }
  }

  /**
   * Create spawner in LevelEditor component
   * @param {Object} spawnerData - Spawner data
   */
  createSpawnerInLevelEditor(spawnerData) {
    if (!this.isConnected) return;
    
    try {
      // Use LevelEditor's spawner creation methods
      if (this.levelEditorComponent.createSpawner) {
        this.levelEditorComponent.createSpawner(spawnerData);
      }
    } catch (error) {
      console.error('[DataBridge] Failed to create spawner in LevelEditor:', error);
    }
  }

  /**
   * Create path in LevelEditor component
   * @param {Object} pathData - Path data
   */
  createPathInLevelEditor(pathData) {
    if (!this.isConnected) return;
    
    try {
      // Use LevelEditor's path creation methods
      if (this.levelEditorComponent.createPath) {
        this.levelEditorComponent.createPath(pathData);
      }
    } catch (error) {
      console.error('[DataBridge] Failed to create path in LevelEditor:', error);
    }
  }

  /**
   * Update settings
   * @param {Object} settings - New settings
   */
  updateSettings(settings) {
    Object.assign(this.settings, settings);
  }

  /**
   * Get sync status
   * @returns {Object} Sync status
   */
  getSyncStatus() {
    return {
      isConnected: this.isConnected,
      syncInProgress: this.syncInProgress,
      queueLength: this.syncQueue.length,
      lastSyncTimestamp: this.lastSyncTimestamp,
      settings: this.settings
    };
  }

  /**
   * Force sync now
   * @param {string} direction - 'to-leveleditor', 'from-leveleditor', or 'both'
   */
  forceSync(direction = 'both') {
    if (!this.isConnected) return;
    
    switch (direction) {
      case 'to-leveleditor':
        const currentLevel = this.editorState.getCurrentLevel();
        if (currentLevel) {
          this.syncToLevelEditor(currentLevel);
        }
        break;
      
      case 'from-leveleditor':
        this.syncFromLevelEditor();
        break;
      
      case 'both':
        this.syncFromLevelEditor();
        break;
    }
  }

  /**
   * Destroy the data bridge
   */
  destroy() {
    // Remove event listeners
    this.eventBus.off('level-data-changed', this.onExtensionLevelChanged, this);
    this.eventBus.off('property-changed', this.onExtensionPropertyChanged, this);
    this.eventBus.off('level-modified', this.onExtensionLevelModified, this);
    this.eventBus.off('send-to-main', this.onSendToMain, this);
    
    if (typeof cc !== 'undefined') {
      cc.systemEvent.off('level-editor-manipulation-end', this.onSceneManipulationEnd, this);
    }
    
    // Disconnect from LevelEditor
    this.disconnectFromLevelEditor();
    
    // Clear sync queue
    this.syncQueue = [];
    this.syncInProgress = false;
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DataBridge;
} else {
  window.DataBridge = DataBridge;
}
