/**
 * Main Level Editor Panel Class
 * Coordinates all components and manages the overall panel state
 */
class LevelEditorPanel {
  constructor() {
    this.eventBus = null;
    this.editorState = null;
    this.levelHierarchy = null;
    this.propertyInspector = null;
    this.subLevelEditor = null;
    this.toolbarActions = null;
    this.integrationManager = null;

    // Component-specific editors
    this.spawnerEditor = null;
    this.pathEditor = null;
    this.mapLayerEditor = null;

    this.isInitialized = false;
    this.currentMenuData = null;
  }

  /**
   * Initialize the panel
   */
  initialize() {
    if (this.isInitialized) return;
    
    try {
      // Create core systems
      this.eventBus = new EditorEventBus();
      this.editorState = new EditorState(this.eventBus);
      
      // Get container elements
      const hierarchyContainer = document.getElementById('level-hierarchy');
      const inspectorContainer = document.getElementById('property-inspector');
      const editorContainer = document.getElementById('sublevel-editor');
      const toolbarContainer = document.getElementById('toolbar');

      // Create main components
      this.levelHierarchy = new LevelHierarchy(hierarchyContainer, this.eventBus, this.editorState);
      this.propertyInspector = new PropertyInspector(inspectorContainer, this.eventBus, this.editorState);
      this.subLevelEditor = new SubLevelEditor(editorContainer, this.eventBus, this.editorState);
      this.toolbarActions = new ToolbarActions(toolbarContainer, this.eventBus, this.editorState);

      // Create component-specific editors (initially hidden)
      // this.createComponentEditors();

      // Create integration manager
      // this.integrationManager = new IntegrationManager(this.eventBus, this.editorState);
      
      // Setup inter-component communication
      this.setupCommunication();
      
      // Setup context menu
      this.setupContextMenu();
      
      this.isInitialized = true;
      console.log('[LevelEditorPanel] Panel initialized successfully');
      
    } catch (error) {
      console.error('[LevelEditorPanel] Failed to initialize panel:', error);
    }
  }

  /**
   * Setup communication between components
   */
  setupCommunication() {
    // Handle messages to main process
    this.eventBus.on('send-to-main', (message, data) => {
      if (window.parent && window.parent.postMessage) {
        window.parent.postMessage({
          type: 'level-editor-message',
          message: message,
          data: data
        }, '*');
      }
    });
    
    // Handle focus requests
    this.eventBus.on('focus-item', (focusData) => {
      // Focus on item in sublevel editor
      if (this.subLevelEditor) {
        // Implementation would depend on the specific focus requirements
        console.log('[LevelEditorPanel] Focus item:', focusData);
      }
    });
    
    // Handle file browser requests
    this.eventBus.on('open-file-browser', (browserData) => {
      // Send file browser request to main process
      this.eventBus.emit('send-to-main', 'open-file-browser', browserData);
    });
  }

  /**
   * Setup context menu
   */
  setupContextMenu() {
    const contextMenu = document.getElementById('context-menu');
    if (!contextMenu) return;
    
    // Handle context menu show requests
    this.eventBus.on('show-context-menu', (menuData) => {
      this.showContextMenu(menuData, contextMenu);
    });
    
    // Hide context menu on click outside
    document.addEventListener('click', (event) => {
      if (!contextMenu.contains(event.target)) {
        this.hideContextMenu(contextMenu);
      }
    });
    
    // Handle context menu item clicks
    contextMenu.addEventListener('click', (event) => {
      const item = event.target.closest('.context-menu-item');
      if (item) {
        const action = item.dataset.action;
        this.handleContextMenuAction(action);
        this.hideContextMenu(contextMenu);
      }
    });
  }

  /**
   * Show context menu
   * @param {Object} menuData - Menu data
   * @param {HTMLElement} contextMenu - Context menu element
   */
  showContextMenu(menuData, contextMenu) {
    // Clear existing items
    contextMenu.innerHTML = '';
    
    // Add menu items
    menuData.actions.forEach(action => {
      if (action === '---') {
        const separator = document.createElement('div');
        separator.className = 'context-menu-separator';
        contextMenu.appendChild(separator);
      } else {
        const item = document.createElement('div');
        item.className = 'context-menu-item';
        item.dataset.action = action;
        item.textContent = this.getActionLabel(action);
        contextMenu.appendChild(item);
      }
    });
    
    // Position and show menu
    contextMenu.style.left = menuData.x + 'px';
    contextMenu.style.top = menuData.y + 'px';
    contextMenu.style.display = 'block';
    
    // Store menu data for action handling
    this.currentMenuData = menuData;
  }

  /**
   * Hide context menu
   * @param {HTMLElement} contextMenu - Context menu element
   */
  hideContextMenu(contextMenu) {
    contextMenu.style.display = 'none';
    this.currentMenuData = null;
  }

  /**
   * Get action label
   * @param {string} action - Action name
   * @returns {string} Action label
   */
  getActionLabel(action) {
    const labels = {
      'add-sublevel': 'Add SubLevel',
      'add-spawner': 'Add Spawner',
      'add-path': 'Add Path',
      'add-map-layer': 'Add Map Layer',
      'duplicate': 'Duplicate',
      'delete': 'Delete',
      'rename': 'Rename',
      'edit-spawner': 'Edit Spawner',
      'edit-path': 'Edit Path',
      'edit-connection': 'Edit Connection',
      'fit-to-content': 'Fit to Content',
      'reset-bounds': 'Reset Bounds'
    };
    
    return labels[action] || action;
  }

  /**
   * Handle context menu action
   * @param {string} action - Action name
   */
  handleContextMenuAction(action) {
    if (!this.currentMenuData) return;
    
    switch (action) {
      case 'add-sublevel':
        this.addSubLevel();
        break;
      case 'add-spawner':
        this.addSpawner();
        break;
      case 'add-path':
        this.addPath();
        break;
      case 'delete':
        this.deleteSelected();
        break;
      case 'duplicate':
        this.duplicateSelected();
        break;
      // Add more action handlers as needed
      default:
        console.log('[LevelEditorPanel] Unhandled context menu action:', action);
    }
  }

  /**
   * Add new sublevel
   */
  addSubLevel() {
    const currentLevel = this.editorState.getCurrentLevel();
    if (!currentLevel) return;
    
    // Create new sublevel from template
    const newSubLevel = {
      id: 'sublevel_' + Date.now(),
      name: 'New SubLevel',
      bounds: { x: 0, y: 0, width: 1000, height: 1000 },
      maps: [],
      spawners: [],
      paths: [],
      events: { nodes: {}, connections: [], triggers: [] },
      entryPoint: { position: { x: 0, y: 0, z: 0 }, entryType: 'smooth', showCardSelection: false },
      connections: [],
      isActive: false,
      isLoaded: false,
      loadPriority: 0
    };
    
    currentLevel.subLevels.push(newSubLevel);
    this.editorState.markModified();
    this.eventBus.emitLevelDataChanged(currentLevel);
  }

  /**
   * Add new spawner
   */
  addSpawner() {
    // Implementation for adding spawner
    console.log('[LevelEditorPanel] Add spawner not yet implemented');
  }

  /**
   * Add new path
   */
  addPath() {
    // Implementation for adding path
    console.log('[LevelEditorPanel] Add path not yet implemented');
  }

  /**
   * Delete selected items
   */
  deleteSelected() {
    // Delegate to sublevel editor
    if (this.subLevelEditor) {
      this.subLevelEditor.deleteSelected();
    }
  }

  /**
   * Duplicate selected items
   */
  duplicateSelected() {
    // Implementation for duplicating items
    console.log('[LevelEditorPanel] Duplicate not yet implemented');
  }

  /**
   * Set level data
   * @param {Object} levelData - Level data
   */
  setLevelData(levelData) {
    if (this.editorState) {
      this.editorState.setCurrentLevel(levelData);
    }
  }

  /**
   * Set selection
   * @param {Array} selection - Selected items
   */
  setSelection(selection) {
    if (this.editorState) {
      this.editorState.setSelection(selection);
    }
  }

  /**
   * Handle message from parent
   * @param {string} message - Message type
   * @param {any} data - Message data
   */
  handleMessage(message, data) {
    switch (message) {
      case 'level-data-changed':
        this.setLevelData(data);
        break;
      case 'level-saved':
        this.onLevelSaved(data);
        break;
      case 'selection-changed':
        this.setSelection(data);
        break;
      default:
        console.log('[LevelEditorPanel] Unhandled message:', message, data);
    }
  }

  /**
   * Handle level saved
   * @param {Object} data - Save data
   */
  onLevelSaved(data) {
    if (this.editorState) {
      this.editorState.markSaved();
    }
    console.log('[LevelEditorPanel] Level saved:', data.path);
  }

  /**
   * Panel show event
   */
  onShow() {
    // Handle panel show
    if (this.subLevelEditor) {
      this.subLevelEditor.resizeCanvas();
    }
  }

  /**
   * Panel hide event
   */
  onHide() {
    // Handle panel hide
  }

  /**
   * Destroy the panel
   */
  destroy() {
    if (this.levelHierarchy) {
      this.levelHierarchy.destroy();
    }
    if (this.propertyInspector) {
      this.propertyInspector.destroy();
    }
    if (this.subLevelEditor) {
      this.subLevelEditor.destroy();
    }
    if (this.toolbarActions) {
      this.toolbarActions.destroy();
    }
    if (this.editorState) {
      this.editorState.destroy();
    }

    // Destroy component-specific editors
    // if (this.spawnerEditor) {
    //   this.spawnerEditor.destroy();
    // }
    // if (this.pathEditor) {
    //   this.pathEditor.destroy();
    // }
    // if (this.mapLayerEditor) {
    //   this.mapLayerEditor.destroy();
    // }

    // Destroy integration manager
    // if (this.integrationManager) {
    //   this.integrationManager.destroy();
    // }

    this.isInitialized = false;
  }

  /**
   * Create component-specific editors
   */
  createComponentEditors() {
    // Temporarily disabled
    return;
    // Create containers for component editors
    let editorContainer = document.getElementById('component-editors');
    if (!editorContainer) {
      // Create the container if it doesn't exist
      editorContainer = document.createElement('div');
      editorContainer.id = 'component-editors';
      editorContainer.className = 'component-editors-container';
      editorContainer.style.display = 'none'; // Initially hidden
      document.body.appendChild(editorContainer);
    }

    // Create spawner editor
    const spawnerContainer = document.createElement('div');
    spawnerContainer.id = 'spawner-editor-container';
    spawnerContainer.className = 'component-editor-container';
    editorContainer.appendChild(spawnerContainer);
    this.spawnerEditor = new SpawnerEditor(spawnerContainer, this.eventBus, this.editorState);

    // Create path editor
    const pathContainer = document.createElement('div');
    pathContainer.id = 'path-editor-container';
    pathContainer.className = 'component-editor-container';
    editorContainer.appendChild(pathContainer);
    this.pathEditor = new PathEditor(pathContainer, this.eventBus, this.editorState);

    // Create map layer editor
    const mapContainer = document.createElement('div');
    mapContainer.id = 'map-editor-container';
    mapContainer.className = 'component-editor-container';
    editorContainer.appendChild(mapContainer);
    this.mapLayerEditor = new MapLayerEditor(mapContainer, this.eventBus, this.editorState);

    // Setup editor switching
    this.setupEditorSwitching();
  }

  /**
   * Setup editor switching based on selection
   */
  setupEditorSwitching() {
    this.eventBus.onSelectionChanged((selection) => {
      this.switchToAppropriateEditor(selection);
    });
  }

  /**
   * Switch to appropriate editor based on selection
   * @param {Array} selection - Selected items
   */
  switchToAppropriateEditor(selection) {
    // Hide all component editors first
    this.hideAllComponentEditors();

    if (selection.length === 1) {
      const selectedItem = selection[0];

      switch (selectedItem.type) {
        case 'spawner':
          this.showComponentEditor('spawner');
          break;
        case 'path':
          this.showComponentEditor('path');
          break;
        case 'map':
          this.showComponentEditor('map');
          break;
        default:
          // Show default property inspector
          this.showComponentEditor('default');
          break;
      }
    } else {
      // Multiple selection or no selection - show default
      this.showComponentEditor('default');
    }
  }

  /**
   * Hide all component editors
   */
  hideAllComponentEditors() {
    const editorContainer = document.getElementById('component-editors');
    if (editorContainer) {
      editorContainer.style.display = 'none';
    }

    // Hide individual editors
    if (this.spawnerEditor) {
      this.spawnerEditor.container.style.display = 'none';
    }
    if (this.pathEditor) {
      this.pathEditor.container.style.display = 'none';
    }
    if (this.mapLayerEditor) {
      this.mapLayerEditor.container.style.display = 'none';
    }
  }

  /**
   * Show specific component editor
   * @param {string} editorType - Type of editor to show
   */
  showComponentEditor(editorType) {
    const editorContainer = document.getElementById('component-editors');
    const inspectorContainer = document.getElementById('property-inspector');

    switch (editorType) {
      case 'spawner':
        if (editorContainer && this.spawnerEditor) {
          editorContainer.style.display = 'block';
          this.spawnerEditor.container.style.display = 'block';
          inspectorContainer.style.display = 'none';
        }
        break;

      case 'path':
        if (editorContainer && this.pathEditor) {
          editorContainer.style.display = 'block';
          this.pathEditor.container.style.display = 'block';
          inspectorContainer.style.display = 'none';
        }
        break;

      case 'map':
        if (editorContainer && this.mapLayerEditor) {
          editorContainer.style.display = 'block';
          this.mapLayerEditor.container.style.display = 'block';
          inspectorContainer.style.display = 'none';
        }
        break;

      default:
        // Show default property inspector
        if (editorContainer) {
          editorContainer.style.display = 'none';
        }
        if (inspectorContainer) {
          inspectorContainer.style.display = 'block';
        }
        break;
    }
  }

  /**
   * Get integration status
   * @returns {Object} Integration status
   */
  getIntegrationStatus() {
    // Temporarily disabled
    return { isConnected: false, error: 'Integration manager not initialized' };
  }

  /**
   * Connect to LevelEditor component
   * @param {Object} levelEditorComponent - LevelEditor component instance
   */
  connectToLevelEditor(levelEditorComponent) {
    // Temporarily disabled
    console.log('[LevelEditorPanel] Integration temporarily disabled');
  }

  /**
   * Enable scene integration
   */
  enableSceneIntegration() {
    // Temporarily disabled
    console.log('[LevelEditorPanel] Scene integration temporarily disabled');
  }

  /**
   * Disable scene integration
   */
  disableSceneIntegration() {
    // Temporarily disabled
    console.log('[LevelEditorPanel] Scene integration temporarily disabled');
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = LevelEditorPanel;
} else {
  window.LevelEditorPanel = LevelEditorPanel;
}
