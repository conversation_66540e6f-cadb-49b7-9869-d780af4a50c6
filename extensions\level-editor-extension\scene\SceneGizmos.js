/**
 * Scene Gizmos System
 * Provides visual gizmos and manipulation tools in the Cocos Creator scene view
 */

const { Vec3, Color, Node, Graphics, Camera } = require('cc');

class SceneGizmos {
  constructor() {
    this.gizmoNodes = new Map();
    this.selectedItems = [];
    this.isEnabled = true;
    this.gizmoLayer = null;
    this.camera = null;
    
    // Gizmo settings
    this.settings = {
      showBounds: true,
      showSpawners: true,
      showPaths: true,
      showConnections: true,
      boundsColor: new Color(0, 122, 204, 128), // #007acc with alpha
      spawnerColor: new Color(255, 107, 53, 255), // #ff6b35
      pathColor: new Color(0, 255, 136, 255), // #00ff88
      connectionColor: new Color(255, 149, 0, 128), // #ff9500 with alpha
      gizmoSize: 1.0,
      alwaysOnTop: true
    };
    
    this.initialize();
  }

  /**
   * Initialize the gizmo system
   */
  initialize() {
    this.createGizmoLayer();
    this.setupEventHandlers();
  }

  /**
   * Create gizmo layer
   */
  createGizmoLayer() {
    // Create a dedicated layer for gizmos
    this.gizmoLayer = new Node('LevelEditorGizmos');
    this.gizmoLayer.layer = cc.Layers.Enum.UI_2D; // Use UI layer for always-on-top rendering
    
    // Add to scene
    const scene = cc.director.getScene();
    if (scene) {
      scene.addChild(this.gizmoLayer);
    }
  }

  /**
   * Setup event handlers
   */
  setupEventHandlers() {
    // Listen for scene changes
    cc.director.on(cc.Director.EVENT_AFTER_SCENE_LAUNCH, this.onSceneChanged, this);
    
    // Listen for camera changes
    cc.director.on(cc.Director.EVENT_BEFORE_DRAW, this.updateGizmos, this);
  }

  /**
   * Handle scene changed
   */
  onSceneChanged() {
    this.clearAllGizmos();
    this.createGizmoLayer();
  }

  /**
   * Set level data
   * @param {Object} levelData - Level data
   */
  setLevelData(levelData) {
    this.levelData = levelData;
    this.updateAllGizmos();
  }

  /**
   * Set selected items
   * @param {Array} selectedItems - Selected items
   */
  setSelectedItems(selectedItems) {
    this.selectedItems = selectedItems;
    this.updateSelectionGizmos();
  }

  /**
   * Enable/disable gizmos
   * @param {boolean} enabled - Whether gizmos are enabled
   */
  setEnabled(enabled) {
    this.isEnabled = enabled;
    if (this.gizmoLayer) {
      this.gizmoLayer.active = enabled;
    }
  }

  /**
   * Update gizmo settings
   * @param {Object} settings - New settings
   */
  updateSettings(settings) {
    Object.assign(this.settings, settings);
    this.updateAllGizmos();
  }

  /**
   * Update all gizmos
   */
  updateAllGizmos() {
    if (!this.isEnabled || !this.levelData) return;
    
    this.clearAllGizmos();
    
    // Create gizmos for each sublevel
    this.levelData.subLevels.forEach(subLevel => {
      this.createSubLevelGizmos(subLevel);
    });
  }

  /**
   * Create gizmos for a sublevel
   * @param {Object} subLevel - SubLevel data
   */
  createSubLevelGizmos(subLevel) {
    const subLevelNode = new Node(`SubLevel_${subLevel.id}`);
    this.gizmoLayer.addChild(subLevelNode);
    this.gizmoNodes.set(subLevel.id, subLevelNode);
    
    // Create bounds gizmo
    if (this.settings.showBounds) {
      this.createBoundsGizmo(subLevelNode, subLevel);
    }
    
    // Create spawner gizmos
    if (this.settings.showSpawners) {
      subLevel.spawners.forEach(spawner => {
        this.createSpawnerGizmo(subLevelNode, spawner);
      });
    }
    
    // Create path gizmos
    if (this.settings.showPaths) {
      subLevel.paths.forEach(path => {
        this.createPathGizmo(subLevelNode, path);
      });
    }
    
    // Create connection gizmos
    if (this.settings.showConnections) {
      subLevel.connections.forEach(connection => {
        this.createConnectionGizmo(subLevelNode, connection);
      });
    }
    
    // Create entry/exit point gizmos
    if (subLevel.entryPoint) {
      this.createEntryPointGizmo(subLevelNode, subLevel.entryPoint);
    }
    
    if (subLevel.exitPoint) {
      this.createExitPointGizmo(subLevelNode, subLevel.exitPoint);
    }
  }

  /**
   * Create bounds gizmo
   * @param {Node} parent - Parent node
   * @param {Object} subLevel - SubLevel data
   */
  createBoundsGizmo(parent, subLevel) {
    const boundsNode = new Node('Bounds');
    parent.addChild(boundsNode);
    
    const graphics = boundsNode.addComponent(Graphics);
    const bounds = subLevel.bounds;
    
    // Set color and style
    graphics.strokeColor = this.settings.boundsColor;
    graphics.fillColor = new Color(this.settings.boundsColor.r, this.settings.boundsColor.g, this.settings.boundsColor.b, 32);
    graphics.lineWidth = 2;
    
    // Draw bounds rectangle
    graphics.rect(bounds.x, bounds.y, bounds.width, bounds.height);
    graphics.fill();
    graphics.stroke();
    
    // Add corner handles for resizing
    this.createBoundsHandles(boundsNode, bounds);
    
    // Store reference
    boundsNode.userData = { type: 'bounds', data: subLevel };
  }

  /**
   * Create bounds handles
   * @param {Node} parent - Parent node
   * @param {Object} bounds - Bounds data
   */
  createBoundsHandles(parent, bounds) {
    const handleSize = 8 * this.settings.gizmoSize;
    const positions = [
      { x: bounds.x, y: bounds.y, name: 'top-left' },
      { x: bounds.x + bounds.width, y: bounds.y, name: 'top-right' },
      { x: bounds.x, y: bounds.y + bounds.height, name: 'bottom-left' },
      { x: bounds.x + bounds.width, y: bounds.y + bounds.height, name: 'bottom-right' }
    ];
    
    positions.forEach(pos => {
      const handleNode = new Node(`Handle_${pos.name}`);
      parent.addChild(handleNode);
      
      handleNode.setPosition(pos.x, pos.y, 0);
      
      const graphics = handleNode.addComponent(Graphics);
      graphics.fillColor = Color.WHITE;
      graphics.strokeColor = Color.BLACK;
      graphics.lineWidth = 1;
      
      graphics.rect(-handleSize/2, -handleSize/2, handleSize, handleSize);
      graphics.fill();
      graphics.stroke();
      
      // Store handle data
      handleNode.userData = { 
        type: 'bounds-handle', 
        corner: pos.name,
        bounds: bounds
      };
    });
  }

  /**
   * Create spawner gizmo
   * @param {Node} parent - Parent node
   * @param {Object} spawner - Spawner data
   */
  createSpawnerGizmo(parent, spawner) {
    const spawnerNode = new Node(`Spawner_${spawner.id}`);
    parent.addChild(spawnerNode);
    
    spawnerNode.setPosition(spawner.position.x, spawner.position.y, spawner.position.z);
    
    const graphics = spawnerNode.addComponent(Graphics);
    const radius = 12 * this.settings.gizmoSize;
    
    // Set color based on active state
    const color = spawner.isActive ? this.settings.spawnerColor : Color.GRAY;
    graphics.fillColor = color;
    graphics.strokeColor = Color.WHITE;
    graphics.lineWidth = 2;
    
    // Draw spawner circle
    graphics.circle(0, 0, radius);
    graphics.fill();
    graphics.stroke();
    
    // Draw spawner icon (simple cross)
    graphics.strokeColor = Color.WHITE;
    graphics.lineWidth = 2;
    graphics.moveTo(-radius/2, 0);
    graphics.lineTo(radius/2, 0);
    graphics.moveTo(0, -radius/2);
    graphics.lineTo(0, radius/2);
    graphics.stroke();
    
    // Add spawn area visualization
    if (spawner.spawnArea) {
      this.createSpawnAreaGizmo(spawnerNode, spawner.spawnArea);
    }
    
    // Store reference
    spawnerNode.userData = { type: 'spawner', data: spawner };
  }

  /**
   * Create spawn area gizmo
   * @param {Node} parent - Parent node
   * @param {Object} spawnArea - Spawn area data
   */
  createSpawnAreaGizmo(parent, spawnArea) {
    const areaNode = new Node('SpawnArea');
    parent.addChild(areaNode);
    
    const graphics = areaNode.addComponent(Graphics);
    graphics.strokeColor = new Color(this.settings.spawnerColor.r, this.settings.spawnerColor.g, this.settings.spawnerColor.b, 128);
    graphics.lineWidth = 1;
    
    const size = spawnArea.size || 50;
    
    switch (spawnArea.type) {
      case 'circle':
        graphics.circle(0, 0, size / 2);
        graphics.stroke();
        break;
      case 'rectangle':
        graphics.rect(-size/2, -size/2, size, size);
        graphics.stroke();
        break;
      case 'line':
        graphics.moveTo(-size/2, 0);
        graphics.lineTo(size/2, 0);
        graphics.stroke();
        break;
      default: // point
        // No additional visualization for point
        break;
    }
  }

  /**
   * Create path gizmo
   * @param {Node} parent - Parent node
   * @param {Object} path - Path data
   */
  createPathGizmo(parent, path) {
    const pathNode = new Node(`Path_${path.id}`);
    parent.addChild(pathNode);
    
    const graphics = pathNode.addComponent(Graphics);
    graphics.strokeColor = path.editorData?.color ? Color.fromHEX(new Color(), path.editorData.color) : this.settings.pathColor;
    graphics.lineWidth = 3;
    
    if (path.points && path.points.length > 1) {
      // Draw path curve
      this.drawPathCurve(graphics, path.points, path.isLoop);
      
      // Create point gizmos
      path.points.forEach((point, index) => {
        this.createPathPointGizmo(pathNode, point, index);
      });
    }
    
    // Store reference
    pathNode.userData = { type: 'path', data: path };
  }

  /**
   * Draw path curve
   * @param {Graphics} graphics - Graphics component
   * @param {Array} points - Path points
   * @param {boolean} isLoop - Whether path is a loop
   */
  drawPathCurve(graphics, points, isLoop) {
    if (points.length < 2) return;
    
    // Move to first point
    const firstPoint = points[0];
    graphics.moveTo(firstPoint.position.x, firstPoint.position.y);
    
    // Draw bezier curves between points
    for (let i = 1; i < points.length; i++) {
      const prevPoint = points[i - 1];
      const currPoint = points[i];
      
      const cp1x = prevPoint.position.x + prevPoint.controlPoint2.x;
      const cp1y = prevPoint.position.y + prevPoint.controlPoint2.y;
      const cp2x = currPoint.position.x + currPoint.controlPoint1.x;
      const cp2y = currPoint.position.y + currPoint.controlPoint1.y;
      
      graphics.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, currPoint.position.x, currPoint.position.y);
    }
    
    // Close path if loop
    if (isLoop && points.length > 2) {
      const lastPoint = points[points.length - 1];
      const firstPoint = points[0];
      
      const cp1x = lastPoint.position.x + lastPoint.controlPoint2.x;
      const cp1y = lastPoint.position.y + lastPoint.controlPoint2.y;
      const cp2x = firstPoint.position.x + firstPoint.controlPoint1.x;
      const cp2y = firstPoint.position.y + firstPoint.controlPoint1.y;
      
      graphics.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, firstPoint.position.x, firstPoint.position.y);
    }
    
    graphics.stroke();
  }

  /**
   * Create path point gizmo
   * @param {Node} parent - Parent node
   * @param {Object} point - Path point data
   * @param {number} index - Point index
   */
  createPathPointGizmo(parent, point, index) {
    const pointNode = new Node(`Point_${index}`);
    parent.addChild(pointNode);
    
    pointNode.setPosition(point.position.x, point.position.y, point.position.z);
    
    const graphics = pointNode.addComponent(Graphics);
    const radius = 6 * this.settings.gizmoSize;
    
    graphics.fillColor = Color.WHITE;
    graphics.strokeColor = Color.BLACK;
    graphics.lineWidth = 2;
    
    // Draw point
    graphics.circle(0, 0, radius);
    graphics.fill();
    graphics.stroke();
    
    // Draw point number
    // Note: In a full implementation, you'd use a Label component for text
    
    // Store reference
    pointNode.userData = { 
      type: 'path-point', 
      data: point, 
      index: index 
    };
  }

  /**
   * Create connection gizmo
   * @param {Node} parent - Parent node
   * @param {Object} connection - Connection data
   */
  createConnectionGizmo(parent, connection) {
    const connectionNode = new Node(`Connection_${connection.id || 'connection'}`);
    parent.addChild(connectionNode);
    
    const graphics = connectionNode.addComponent(Graphics);
    const triggerArea = connection.triggerArea;
    
    graphics.strokeColor = this.settings.connectionColor;
    graphics.fillColor = new Color(this.settings.connectionColor.r, this.settings.connectionColor.g, this.settings.connectionColor.b, 64);
    graphics.lineWidth = 2;
    
    // Draw trigger area
    graphics.rect(triggerArea.x, triggerArea.y, triggerArea.width, triggerArea.height);
    graphics.fill();
    graphics.stroke();
    
    // Store reference
    connectionNode.userData = { type: 'connection', data: connection };
  }

  /**
   * Create entry point gizmo
   * @param {Node} parent - Parent node
   * @param {Object} entryPoint - Entry point data
   */
  createEntryPointGizmo(parent, entryPoint) {
    const entryNode = new Node('EntryPoint');
    parent.addChild(entryNode);
    
    entryNode.setPosition(entryPoint.position.x, entryPoint.position.y, entryPoint.position.z);
    
    const graphics = entryNode.addComponent(Graphics);
    const radius = 10 * this.settings.gizmoSize;
    
    graphics.fillColor = Color.GREEN;
    graphics.strokeColor = Color.WHITE;
    graphics.lineWidth = 2;
    
    // Draw entry point
    graphics.circle(0, 0, radius);
    graphics.fill();
    graphics.stroke();
    
    // Draw arrow pointing in
    graphics.strokeColor = Color.WHITE;
    graphics.lineWidth = 2;
    graphics.moveTo(-radius/2, 0);
    graphics.lineTo(radius/2, 0);
    graphics.moveTo(radius/4, -radius/4);
    graphics.lineTo(radius/2, 0);
    graphics.lineTo(radius/4, radius/4);
    graphics.stroke();
    
    // Store reference
    entryNode.userData = { type: 'entry-point', data: entryPoint };
  }

  /**
   * Create exit point gizmo
   * @param {Node} parent - Parent node
   * @param {Object} exitPoint - Exit point data
   */
  createExitPointGizmo(parent, exitPoint) {
    const exitNode = new Node('ExitPoint');
    parent.addChild(exitNode);
    
    exitNode.setPosition(exitPoint.position.x, exitPoint.position.y, exitPoint.position.z);
    
    const graphics = exitNode.addComponent(Graphics);
    const radius = 10 * this.settings.gizmoSize;
    
    graphics.fillColor = Color.RED;
    graphics.strokeColor = Color.WHITE;
    graphics.lineWidth = 2;
    
    // Draw exit point
    graphics.circle(0, 0, radius);
    graphics.fill();
    graphics.stroke();
    
    // Draw arrow pointing out
    graphics.strokeColor = Color.WHITE;
    graphics.lineWidth = 2;
    graphics.moveTo(-radius/2, 0);
    graphics.lineTo(radius/2, 0);
    graphics.moveTo(-radius/4, -radius/4);
    graphics.lineTo(-radius/2, 0);
    graphics.lineTo(-radius/4, radius/4);
    graphics.stroke();
    
    // Store reference
    exitNode.userData = { type: 'exit-point', data: exitPoint };
  }

  /**
   * Update selection gizmos
   */
  updateSelectionGizmos() {
    // Clear previous selection highlights
    this.clearSelectionHighlights();
    
    // Add selection highlights
    this.selectedItems.forEach(item => {
      this.addSelectionHighlight(item);
    });
  }

  /**
   * Clear selection highlights
   */
  clearSelectionHighlights() {
    // Remove existing selection highlights
    this.gizmoLayer.children.forEach(child => {
      const selectionNode = child.getChildByName('Selection');
      if (selectionNode) {
        selectionNode.removeFromParent();
      }
    });
  }

  /**
   * Add selection highlight
   * @param {Object} item - Selected item
   */
  addSelectionHighlight(item) {
    const gizmoNode = this.findGizmoNode(item);
    if (!gizmoNode) return;
    
    const selectionNode = new Node('Selection');
    gizmoNode.addChild(selectionNode);
    
    const graphics = selectionNode.addComponent(Graphics);
    graphics.strokeColor = Color.YELLOW;
    graphics.lineWidth = 3;
    
    // Create selection outline based on item type
    switch (item.type) {
      case 'spawner':
        graphics.circle(0, 0, 15 * this.settings.gizmoSize);
        graphics.stroke();
        break;
      case 'path-point':
        graphics.circle(0, 0, 8 * this.settings.gizmoSize);
        graphics.stroke();
        break;
      case 'bounds':
        const bounds = item.data.bounds;
        graphics.rect(bounds.x - 2, bounds.y - 2, bounds.width + 4, bounds.height + 4);
        graphics.stroke();
        break;
    }
  }

  /**
   * Find gizmo node for item
   * @param {Object} item - Item to find
   * @returns {Node|null} Gizmo node or null
   */
  findGizmoNode(item) {
    // Implementation would search through gizmo nodes to find matching item
    // This is a simplified version
    for (const [id, node] of this.gizmoNodes) {
      const found = this.searchNodeForItem(node, item);
      if (found) return found;
    }
    return null;
  }

  /**
   * Search node for item
   * @param {Node} node - Node to search
   * @param {Object} item - Item to find
   * @returns {Node|null} Found node or null
   */
  searchNodeForItem(node, item) {
    if (node.userData && node.userData.data === item.data) {
      return node;
    }
    
    for (const child of node.children) {
      const found = this.searchNodeForItem(child, item);
      if (found) return found;
    }
    
    return null;
  }

  /**
   * Update gizmos (called each frame)
   */
  updateGizmos() {
    if (!this.isEnabled || !this.gizmoLayer) return;
    
    // Update gizmo visibility based on camera distance
    this.updateGizmoScale();
  }

  /**
   * Update gizmo scale based on camera distance
   */
  updateGizmoScale() {
    // Get main camera
    const camera = cc.Camera.main;
    if (!camera) return;
    
    // Calculate scale factor based on camera zoom
    const zoom = camera.zoomRatio || 1;
    const scaleFactor = Math.max(0.5, Math.min(2.0, 1 / zoom));
    
    // Apply scale to all gizmo nodes
    this.gizmoLayer.children.forEach(child => {
      child.setScale(scaleFactor, scaleFactor, 1);
    });
  }

  /**
   * Clear all gizmos
   */
  clearAllGizmos() {
    if (this.gizmoLayer) {
      this.gizmoLayer.removeAllChildren();
    }
    this.gizmoNodes.clear();
  }

  /**
   * Destroy the gizmo system
   */
  destroy() {
    // Remove event listeners
    cc.director.off(cc.Director.EVENT_AFTER_SCENE_LAUNCH, this.onSceneChanged, this);
    cc.director.off(cc.Director.EVENT_BEFORE_DRAW, this.updateGizmos, this);
    
    // Clear gizmos
    this.clearAllGizmos();
    
    // Remove gizmo layer
    if (this.gizmoLayer) {
      this.gizmoLayer.removeFromParent();
      this.gizmoLayer = null;
    }
  }
}

module.exports = SceneGizmos;
