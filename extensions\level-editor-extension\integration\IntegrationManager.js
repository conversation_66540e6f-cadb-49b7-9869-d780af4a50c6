/**
 * Integration Manager
 * Coordinates all integration components and manages the connection
 * between the extension and the existing LevelEditor system
 */

const DataBridge = require('./DataBridge');

class IntegrationManager {
  constructor(eventBus, editorState) {
    this.eventBus = eventBus;
    this.editorState = editorState;
    
    // Integration components
    this.dataBridge = null;
    this.sceneIntegration = null;
    
    // Integration state
    this.isInitialized = false;
    this.isConnected = false;
    this.connectionAttempts = 0;
    this.maxConnectionAttempts = 5;
    
    // References to external systems
    this.levelEditorComponent = null;
    this.sceneGizmos = null;
    
    // Integration settings
    this.settings = {
      autoConnect: true,
      connectionRetryInterval: 2000, // 2 seconds
      enableSceneIntegration: true,
      enableDataSync: true,
      validateOnConnect: true,
      backupOnConnect: true
    };
    
    this.initialize();
  }

  /**
   * Initialize the integration manager
   */
  initialize() {
    if (this.isInitialized) return;
    
    try {
      // Create integration components
      this.createIntegrationComponents();
      
      // Setup event handlers
      this.setupEventHandlers();
      
      // Start auto-connection if enabled
      if (this.settings.autoConnect) {
        this.startAutoConnection();
      }
      
      this.isInitialized = true;
      console.log('[IntegrationManager] Initialized successfully');
      
    } catch (error) {
      console.error('[IntegrationManager] Failed to initialize:', error);
    }
  }

  /**
   * Create integration components
   */
  createIntegrationComponents() {
    // Create data bridge
    this.dataBridge = new DataBridge(this.eventBus, this.editorState);
    
    // Scene integration will be created when needed
    this.sceneIntegration = null;
  }

  /**
   * Setup event handlers
   */
  setupEventHandlers() {
    // Listen for connection requests
    this.eventBus.on('connect-to-leveleditor', this.connectToLevelEditor, this);
    this.eventBus.on('disconnect-from-leveleditor', this.disconnectFromLevelEditor, this);
    
    // Listen for scene integration requests
    this.eventBus.on('enable-scene-integration', this.enableSceneIntegration, this);
    this.eventBus.on('disable-scene-integration', this.disableSceneIntegration, this);
    
    // Listen for settings changes
    this.eventBus.on('integration-settings-changed', this.onSettingsChanged, this);
    
    // Listen for external system events
    if (typeof cc !== 'undefined') {
      cc.systemEvent.on('level-editor-component-ready', this.onLevelEditorComponentReady, this);
      cc.systemEvent.on('level-editor-component-destroyed', this.onLevelEditorComponentDestroyed, this);
    }
  }

  /**
   * Start auto-connection process
   */
  startAutoConnection() {
    this.connectionAttempts = 0;
    this.attemptConnection();
  }

  /**
   * Attempt to connect to the LevelEditor component
   */
  attemptConnection() {
    if (this.isConnected || this.connectionAttempts >= this.maxConnectionAttempts) {
      return;
    }
    
    this.connectionAttempts++;
    console.log(`[IntegrationManager] Connection attempt ${this.connectionAttempts}/${this.maxConnectionAttempts}`);
    
    // Try to find the LevelEditor component in the scene
    const levelEditorComponent = this.findLevelEditorComponent();
    
    if (levelEditorComponent) {
      this.connectToLevelEditor(levelEditorComponent);
    } else {
      // Retry after interval
      setTimeout(() => {
        this.attemptConnection();
      }, this.settings.connectionRetryInterval);
    }
  }

  /**
   * Find the LevelEditor component in the current scene
   * @returns {Object|null} LevelEditor component or null
   */
  findLevelEditorComponent() {
    if (typeof cc === 'undefined') return null;
    
    try {
      const scene = cc.director.getScene();
      if (!scene) return null;
      
      // Search for LevelEditor component
      const levelEditorNode = scene.getChildByName('LevelEditor');
      if (levelEditorNode) {
        const levelEditorComponent = levelEditorNode.getComponent('LevelEditor');
        if (levelEditorComponent) {
          return levelEditorComponent;
        }
      }
      
      // Alternative search: look for any node with LevelEditor component
      const allNodes = scene.children;
      for (const node of allNodes) {
        const component = node.getComponent('LevelEditor');
        if (component) {
          return component;
        }
      }
      
      return null;
    } catch (error) {
      console.error('[IntegrationManager] Error finding LevelEditor component:', error);
      return null;
    }
  }

  /**
   * Connect to the LevelEditor component
   * @param {Object} levelEditorComponent - LevelEditor component instance
   */
  connectToLevelEditor(levelEditorComponent) {
    if (this.isConnected) {
      console.warn('[IntegrationManager] Already connected to LevelEditor');
      return;
    }
    
    try {
      this.levelEditorComponent = levelEditorComponent;
      
      // Validate component if enabled
      if (this.settings.validateOnConnect) {
        const validation = this.validateLevelEditorComponent(levelEditorComponent);
        if (!validation.isValid) {
          throw new Error(`LevelEditor component validation failed: ${validation.errors.join(', ')}`);
        }
      }
      
      // Create backup if enabled
      if (this.settings.backupOnConnect) {
        this.createBackup();
      }
      
      // Connect data bridge
      if (this.settings.enableDataSync && this.dataBridge) {
        this.dataBridge.connectToLevelEditor(levelEditorComponent);
      }
      
      // Enable scene integration if requested
      if (this.settings.enableSceneIntegration) {
        this.enableSceneIntegration();
      }
      
      this.isConnected = true;
      this.connectionAttempts = 0;
      
      // Emit connection event
      this.eventBus.emit('leveleditor-connected', {
        component: levelEditorComponent,
        timestamp: Date.now()
      });
      
      console.log('[IntegrationManager] Successfully connected to LevelEditor component');
      
    } catch (error) {
      console.error('[IntegrationManager] Failed to connect to LevelEditor:', error);
      this.levelEditorComponent = null;
    }
  }

  /**
   * Disconnect from the LevelEditor component
   */
  disconnectFromLevelEditor() {
    if (!this.isConnected) {
      console.warn('[IntegrationManager] Not connected to LevelEditor');
      return;
    }
    
    try {
      // Disable scene integration
      this.disableSceneIntegration();
      
      // Disconnect data bridge
      if (this.dataBridge) {
        this.dataBridge.disconnectFromLevelEditor();
      }
      
      // Clear references
      this.levelEditorComponent = null;
      this.isConnected = false;
      
      // Emit disconnection event
      this.eventBus.emit('leveleditor-disconnected', {
        timestamp: Date.now()
      });
      
      console.log('[IntegrationManager] Disconnected from LevelEditor component');
      
    } catch (error) {
      console.error('[IntegrationManager] Error during disconnection:', error);
    }
  }

  /**
   * Enable scene integration
   */
  enableSceneIntegration() {
    if (!this.isConnected) {
      console.warn('[IntegrationManager] Cannot enable scene integration: not connected to LevelEditor');
      return;
    }
    
    try {
      // Import scene integration if not already loaded
      if (!this.sceneIntegration) {
        // In a real implementation, you'd dynamically import the scene integration
        // For now, we'll create a placeholder
        this.sceneIntegration = {
          enable: () => console.log('[IntegrationManager] Scene integration enabled'),
          disable: () => console.log('[IntegrationManager] Scene integration disabled'),
          setLevelData: (data) => console.log('[IntegrationManager] Scene integration level data set'),
          setSelectedItems: (items) => console.log('[IntegrationManager] Scene integration selection set')
        };
      }
      
      // Enable scene integration with current level data
      const currentLevel = this.editorState.getCurrentLevel();
      if (currentLevel) {
        this.sceneIntegration.enable(currentLevel);
      }
      
      // Sync current selection
      const currentSelection = this.editorState.getSelection();
      if (currentSelection.length > 0) {
        this.sceneIntegration.setSelectedItems(currentSelection);
      }
      
      console.log('[IntegrationManager] Scene integration enabled');
      
    } catch (error) {
      console.error('[IntegrationManager] Failed to enable scene integration:', error);
    }
  }

  /**
   * Disable scene integration
   */
  disableSceneIntegration() {
    if (this.sceneIntegration) {
      try {
        this.sceneIntegration.disable();
        console.log('[IntegrationManager] Scene integration disabled');
      } catch (error) {
        console.error('[IntegrationManager] Error disabling scene integration:', error);
      }
    }
  }

  /**
   * Validate LevelEditor component
   * @param {Object} component - LevelEditor component to validate
   * @returns {Object} Validation result
   */
  validateLevelEditorComponent(component) {
    const errors = [];
    const warnings = [];
    
    try {
      // Check required methods
      const requiredMethods = ['loadLevel', 'saveLevel', 'getCurrentLevel', 'createNewLevel'];
      requiredMethods.forEach(method => {
        if (typeof component[method] !== 'function') {
          errors.push(`Missing required method: ${method}`);
        }
      });
      
      // Check required properties
      const requiredProperties = ['currentLevel', 'selectedSubLevel', 'currentEditMode'];
      requiredProperties.forEach(prop => {
        if (!(prop in component)) {
          warnings.push(`Missing property: ${prop}`);
        }
      });
      
      // Check component state
      if (!component.node) {
        errors.push('Component is not attached to a node');
      }
      
      if (!component.enabled) {
        warnings.push('Component is disabled');
      }
      
    } catch (error) {
      errors.push(`Validation error: ${error.message}`);
    }
    
    return {
      isValid: errors.length === 0,
      errors: errors,
      warnings: warnings
    };
  }

  /**
   * Create backup of current state
   */
  createBackup() {
    try {
      const currentLevel = this.editorState.getCurrentLevel();
      if (currentLevel) {
        const backup = {
          timestamp: Date.now(),
          levelData: JSON.parse(JSON.stringify(currentLevel)),
          editorSettings: this.editorState.getSettings(),
          selection: this.editorState.getSelection()
        };
        
        // Store backup (in a real implementation, this would be saved to file)
        localStorage.setItem('level-editor-backup', JSON.stringify(backup));
        console.log('[IntegrationManager] Backup created');
      }
    } catch (error) {
      console.error('[IntegrationManager] Failed to create backup:', error);
    }
  }

  /**
   * Restore from backup
   */
  restoreFromBackup() {
    try {
      const backupData = localStorage.getItem('level-editor-backup');
      if (backupData) {
        const backup = JSON.parse(backupData);
        
        // Restore level data
        if (backup.levelData) {
          this.editorState.setCurrentLevel(backup.levelData);
        }
        
        // Restore settings
        if (backup.editorSettings) {
          this.editorState.updateSettings(backup.editorSettings);
        }
        
        // Restore selection
        if (backup.selection) {
          this.editorState.setSelection(backup.selection);
        }
        
        console.log('[IntegrationManager] Restored from backup');
        return true;
      }
    } catch (error) {
      console.error('[IntegrationManager] Failed to restore from backup:', error);
    }
    
    return false;
  }

  /**
   * Get integration status
   * @returns {Object} Integration status
   */
  getIntegrationStatus() {
    return {
      isInitialized: this.isInitialized,
      isConnected: this.isConnected,
      connectionAttempts: this.connectionAttempts,
      hasLevelEditorComponent: !!this.levelEditorComponent,
      hasSceneIntegration: !!this.sceneIntegration,
      dataBridgeStatus: this.dataBridge ? this.dataBridge.getSyncStatus() : null,
      settings: this.settings
    };
  }

  /**
   * Update integration settings
   * @param {Object} settings - New settings
   */
  updateSettings(settings) {
    Object.assign(this.settings, settings);
    
    // Update component settings
    if (this.dataBridge) {
      this.dataBridge.updateSettings(settings);
    }
    
    // Emit settings changed event
    this.eventBus.emit('integration-settings-updated', this.settings);
  }

  /**
   * Force reconnection
   */
  forceReconnect() {
    if (this.isConnected) {
      this.disconnectFromLevelEditor();
    }
    
    this.connectionAttempts = 0;
    this.attemptConnection();
  }

  /**
   * Export integration data
   * @returns {Object} Integration data
   */
  exportIntegrationData() {
    return {
      status: this.getIntegrationStatus(),
      levelData: this.editorState.getCurrentLevel(),
      settings: this.settings,
      timestamp: Date.now()
    };
  }

  /**
   * Import integration data
   * @param {Object} data - Integration data to import
   */
  importIntegrationData(data) {
    try {
      if (data.levelData) {
        this.editorState.setCurrentLevel(data.levelData);
      }
      
      if (data.settings) {
        this.updateSettings(data.settings);
      }
      
      console.log('[IntegrationManager] Integration data imported');
    } catch (error) {
      console.error('[IntegrationManager] Failed to import integration data:', error);
    }
  }

  // Event handlers
  onLevelEditorComponentReady(event) {
    if (this.settings.autoConnect && !this.isConnected) {
      this.connectToLevelEditor(event.component);
    }
  }

  onLevelEditorComponentDestroyed(event) {
    if (this.isConnected) {
      this.disconnectFromLevelEditor();
    }
  }

  onSettingsChanged(settings) {
    this.updateSettings(settings);
  }

  /**
   * Destroy the integration manager
   */
  destroy() {
    // Disconnect from systems
    this.disconnectFromLevelEditor();
    this.disableSceneIntegration();
    
    // Destroy components
    if (this.dataBridge) {
      this.dataBridge.destroy();
      this.dataBridge = null;
    }
    
    // Remove event listeners
    this.eventBus.off('connect-to-leveleditor', this.connectToLevelEditor, this);
    this.eventBus.off('disconnect-from-leveleditor', this.disconnectFromLevelEditor, this);
    this.eventBus.off('enable-scene-integration', this.enableSceneIntegration, this);
    this.eventBus.off('disable-scene-integration', this.disableSceneIntegration, this);
    this.eventBus.off('integration-settings-changed', this.onSettingsChanged, this);
    
    if (typeof cc !== 'undefined') {
      cc.systemEvent.off('level-editor-component-ready', this.onLevelEditorComponentReady, this);
      cc.systemEvent.off('level-editor-component-destroyed', this.onLevelEditorComponentDestroyed, this);
    }
    
    // Reset state
    this.isInitialized = false;
    this.isConnected = false;
    this.levelEditorComponent = null;
    this.sceneIntegration = null;
    
    console.log('[IntegrationManager] Destroyed');
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = IntegrationManager;
} else {
  window.IntegrationManager = IntegrationManager;
}
