/**
 * Level Editor Bridge (TypeScript)
 * Bridge component that can be used from the existing LevelEditor.ts
 * to communicate with the level editor extension
 */

import { _decorator, Component, Node } from 'cc';
import { ILevelData, SubLevel, Spawner, Path, MapLayer } from '../../../assets/scripts/Game/level/core/LevelData';
import { EditMode, SelectionInfo } from '../../../assets/scripts/Game/level/core/Types';

const { ccclass, property } = _decorator;

/**
 * Extension communication interface
 */
interface IExtensionBridge {
  isConnected(): boolean;
  sendMessage(message: string, data?: any): void;
  onMessage(callback: (message: string, data: any) => void): void;
  destroy(): void;
}

/**
 * Extension event data
 */
interface ExtensionEventData {
  type: string;
  data: any;
  timestamp: number;
}

/**
 * Bridge settings
 */
interface BridgeSettings {
  autoConnect: boolean;
  enableBidirectionalSync: boolean;
  syncInterval: number;
  enableSceneGizmos: boolean;
  enableManipulation: boolean;
}

@ccclass('LevelEditorBridge')
export class LevelEditorBridge extends Component {
  
  @property({ tooltip: 'Enable automatic connection to extension' })
  public autoConnect: boolean = true;
  
  @property({ tooltip: 'Enable bidirectional data synchronization' })
  public enableBidirectionalSync: boolean = true;
  
  @property({ tooltip: 'Sync interval in milliseconds' })
  public syncInterval: number = 1000;
  
  @property({ tooltip: 'Enable scene gizmos' })
  public enableSceneGizmos: boolean = true;
  
  @property({ tooltip: 'Enable scene manipulation' })
  public enableManipulation: boolean = true;
  
  // Private properties
  private extensionBridge: IExtensionBridge | null = null;
  private isConnected: boolean = false;
  private messageHandlers: Map<string, Function[]> = new Map();
  private syncTimer: number | null = null;
  
  // Callbacks for LevelEditor component
  public onExtensionConnected: ((bridge: LevelEditorBridge) => void) | null = null;
  public onExtensionDisconnected: (() => void) | null = null;
  public onLevelDataReceived: ((levelData: ILevelData) => void) | null = null;
  public onSelectionReceived: ((selection: SelectionInfo) => void) | null = null;
  public onEditModeReceived: ((editMode: EditMode) => void) | null = null;
  
  /**
   * Component lifecycle - start
   */
  start() {
    this.initialize();
  }
  
  /**
   * Component lifecycle - on destroy
   */
  onDestroy() {
    this.destroy();
  }
  
  /**
   * Initialize the bridge
   */
  private initialize(): void {
    try {
      this.setupMessageHandlers();
      
      if (this.autoConnect) {
        this.connectToExtension();
      }
      
      // Emit ready event for extension to detect
      this.emitSystemEvent('level-editor-component-ready', { component: this });
      
      console.log('[LevelEditorBridge] Initialized');
    } catch (error) {
      console.error('[LevelEditorBridge] Failed to initialize:', error);
    }
  }
  
  /**
   * Setup message handlers
   */
  private setupMessageHandlers(): void {
    // Register handlers for different message types
    this.registerMessageHandler('level-data-changed', this.onLevelDataChanged.bind(this));
    this.registerMessageHandler('selection-changed', this.onSelectionChanged.bind(this));
    this.registerMessageHandler('edit-mode-changed', this.onEditModeChanged.bind(this));
    this.registerMessageHandler('property-changed', this.onPropertyChanged.bind(this));
    this.registerMessageHandler('manipulation-start', this.onManipulationStart.bind(this));
    this.registerMessageHandler('manipulation-update', this.onManipulationUpdate.bind(this));
    this.registerMessageHandler('manipulation-end', this.onManipulationEnd.bind(this));
  }
  
  /**
   * Connect to the extension
   */
  public connectToExtension(): void {
    if (this.isConnected) {
      console.warn('[LevelEditorBridge] Already connected to extension');
      return;
    }
    
    try {
      // Create extension bridge
      this.extensionBridge = this.createExtensionBridge();
      
      if (this.extensionBridge && this.extensionBridge.isConnected()) {
        this.isConnected = true;
        
        // Setup message listener
        this.extensionBridge.onMessage(this.handleExtensionMessage.bind(this));
        
        // Start sync timer if enabled
        if (this.enableBidirectionalSync && this.syncInterval > 0) {
          this.startSyncTimer();
        }
        
        // Notify connection
        if (this.onExtensionConnected) {
          this.onExtensionConnected(this);
        }
        
        console.log('[LevelEditorBridge] Connected to extension');
      } else {
        console.warn('[LevelEditorBridge] Failed to connect to extension');
      }
    } catch (error) {
      console.error('[LevelEditorBridge] Error connecting to extension:', error);
    }
  }
  
  /**
   * Disconnect from the extension
   */
  public disconnectFromExtension(): void {
    if (!this.isConnected) {
      return;
    }
    
    try {
      // Stop sync timer
      this.stopSyncTimer();
      
      // Destroy extension bridge
      if (this.extensionBridge) {
        this.extensionBridge.destroy();
        this.extensionBridge = null;
      }
      
      this.isConnected = false;
      
      // Notify disconnection
      if (this.onExtensionDisconnected) {
        this.onExtensionDisconnected();
      }
      
      console.log('[LevelEditorBridge] Disconnected from extension');
    } catch (error) {
      console.error('[LevelEditorBridge] Error disconnecting from extension:', error);
    }
  }
  
  /**
   * Create extension bridge
   */
  private createExtensionBridge(): IExtensionBridge | null {
    // In a real implementation, this would create a proper bridge
    // For now, we'll create a mock bridge that uses system events
    return {
      isConnected: () => true,
      sendMessage: (message: string, data?: any) => {
        this.emitSystemEvent('level-editor-to-extension', { message, data });
      },
      onMessage: (callback: (message: string, data: any) => void) => {
        // Listen for extension messages
        cc.systemEvent.on('extension-to-level-editor', (event: any) => {
          callback(event.message, event.data);
        });
      },
      destroy: () => {
        cc.systemEvent.off('extension-to-level-editor');
      }
    };
  }
  
  /**
   * Send message to extension
   */
  public sendToExtension(message: string, data?: any): void {
    if (!this.isConnected || !this.extensionBridge) {
      console.warn('[LevelEditorBridge] Cannot send message: not connected to extension');
      return;
    }
    
    try {
      this.extensionBridge.sendMessage(message, data);
    } catch (error) {
      console.error('[LevelEditorBridge] Error sending message to extension:', error);
    }
  }
  
  /**
   * Handle message from extension
   */
  private handleExtensionMessage(message: string, data: any): void {
    const handlers = this.messageHandlers.get(message);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`[LevelEditorBridge] Error handling message '${message}':`, error);
        }
      });
    }
  }
  
  /**
   * Register message handler
   */
  public registerMessageHandler(message: string, handler: Function): void {
    if (!this.messageHandlers.has(message)) {
      this.messageHandlers.set(message, []);
    }
    this.messageHandlers.get(message)!.push(handler);
  }
  
  /**
   * Unregister message handler
   */
  public unregisterMessageHandler(message: string, handler: Function): void {
    const handlers = this.messageHandlers.get(message);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index !== -1) {
        handlers.splice(index, 1);
      }
    }
  }
  
  /**
   * Start sync timer
   */
  private startSyncTimer(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }
    
    this.syncTimer = setInterval(() => {
      this.performSync();
    }, this.syncInterval) as any;
  }
  
  /**
   * Stop sync timer
   */
  private stopSyncTimer(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }
  }
  
  /**
   * Perform synchronization
   */
  private performSync(): void {
    // This would sync data between LevelEditor and extension
    // Implementation depends on specific sync requirements
  }
  
  /**
   * Emit system event
   */
  private emitSystemEvent(eventName: string, data: any): void {
    if (typeof cc !== 'undefined' && cc.systemEvent) {
      cc.systemEvent.emit(eventName, data);
    }
  }
  
  // Message handlers
  private onLevelDataChanged(data: any): void {
    if (this.onLevelDataReceived && data.levelData) {
      this.onLevelDataReceived(data.levelData);
    }
  }
  
  private onSelectionChanged(data: any): void {
    if (this.onSelectionReceived && data.selection) {
      this.onSelectionReceived(data.selection);
    }
  }
  
  private onEditModeChanged(data: any): void {
    if (this.onEditModeReceived && data.editMode) {
      this.onEditModeReceived(data.editMode);
    }
  }
  
  private onPropertyChanged(data: any): void {
    // Handle property changes from extension
    console.log('[LevelEditorBridge] Property changed:', data);
  }
  
  private onManipulationStart(data: any): void {
    // Handle manipulation start from extension
    console.log('[LevelEditorBridge] Manipulation started:', data);
  }
  
  private onManipulationUpdate(data: any): void {
    // Handle manipulation update from extension
    console.log('[LevelEditorBridge] Manipulation updated:', data);
  }
  
  private onManipulationEnd(data: any): void {
    // Handle manipulation end from extension
    console.log('[LevelEditorBridge] Manipulation ended:', data);
  }
  
  // Public API for LevelEditor component
  
  /**
   * Send level data to extension
   */
  public sendLevelData(levelData: ILevelData): void {
    this.sendToExtension('level-data-changed', { levelData });
  }
  
  /**
   * Send selection to extension
   */
  public sendSelection(selection: SelectionInfo): void {
    this.sendToExtension('selection-changed', { selection });
  }
  
  /**
   * Send edit mode to extension
   */
  public sendEditMode(editMode: EditMode): void {
    this.sendToExtension('edit-mode-changed', { editMode });
  }
  
  /**
   * Send property change to extension
   */
  public sendPropertyChange(propertyPath: string, value: any, target: any): void {
    this.sendToExtension('property-changed', { propertyPath, value, target });
  }
  
  /**
   * Request extension to focus on item
   */
  public focusOnItem(itemType: string, itemId: string): void {
    this.sendToExtension('focus-on-item', { itemType, itemId });
  }
  
  /**
   * Get bridge settings
   */
  public getSettings(): BridgeSettings {
    return {
      autoConnect: this.autoConnect,
      enableBidirectionalSync: this.enableBidirectionalSync,
      syncInterval: this.syncInterval,
      enableSceneGizmos: this.enableSceneGizmos,
      enableManipulation: this.enableManipulation
    };
  }
  
  /**
   * Update bridge settings
   */
  public updateSettings(settings: Partial<BridgeSettings>): void {
    if (settings.autoConnect !== undefined) {
      this.autoConnect = settings.autoConnect;
    }
    if (settings.enableBidirectionalSync !== undefined) {
      this.enableBidirectionalSync = settings.enableBidirectionalSync;
    }
    if (settings.syncInterval !== undefined) {
      this.syncInterval = settings.syncInterval;
      if (this.isConnected) {
        this.startSyncTimer();
      }
    }
    if (settings.enableSceneGizmos !== undefined) {
      this.enableSceneGizmos = settings.enableSceneGizmos;
    }
    if (settings.enableManipulation !== undefined) {
      this.enableManipulation = settings.enableManipulation;
    }
    
    // Send settings to extension
    this.sendToExtension('settings-changed', this.getSettings());
  }
  
  /**
   * Get connection status
   */
  public getConnectionStatus(): { isConnected: boolean; hasExtension: boolean } {
    return {
      isConnected: this.isConnected,
      hasExtension: !!this.extensionBridge
    };
  }
  
  /**
   * Destroy the bridge
   */
  private destroy(): void {
    // Emit destroyed event
    this.emitSystemEvent('level-editor-component-destroyed', { component: this });
    
    // Disconnect from extension
    this.disconnectFromExtension();
    
    // Clear message handlers
    this.messageHandlers.clear();
    
    // Clear callbacks
    this.onExtensionConnected = null;
    this.onExtensionDisconnected = null;
    this.onLevelDataReceived = null;
    this.onSelectionReceived = null;
    this.onEditModeReceived = null;
    
    console.log('[LevelEditorBridge] Destroyed');
  }
}
