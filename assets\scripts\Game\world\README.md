# World System

The World System is a comprehensive gameplay driver architecture for managing game systems, state, and lifecycle in Cocos Creator projects.

## Overview

The World System provides:
- **Modular System Architecture**: Register and manage custom game systems
- **Lifecycle Management**: Proper initialization, update, and cleanup of systems
- **World State Management**: Handle different world states (running, paused, stopped, etc.)
- **Configuration-Driven Setup**: Initialize worlds with comprehensive configuration data
- **Component Integration**: Seamless integration with Cocos Creator's component system

## Architecture

### Core Components

1. **System** - Base class for all game systems
2. **SystemContainer** - Manages registration and updating of multiple systems
3. **World** - Main gameplay driver containing a SystemContainer
4. **WorldInitializeData** - Configuration data for world initialization
5. **Bootstrap** - Cocos Creator component that creates and drives the world

### System Hierarchy

```
Bootstrap (cc.Component)
└── World
    └── SystemContainer
        ├── BulletSystem
        ├── LevelSystem
        ├── PlayerSystem
        └── [Custom Systems...]
```

## Quick Start

### 1. Using Bootstrap Component (Recommended)

```typescript
import { Bootstrap, GameMode, DifficultyLevel } from "./Game/world";

// Add Bootstrap component to a node in your scene
const bootstrap = node.addComponent(Bootstrap);
bootstrap.levelId = "level_001";
bootstrap.gameMode = GameMode.STORY;
bootstrap.difficulty = DifficultyLevel.NORMAL;
bootstrap.autoStart = true;
```

### 2. Manual World Creation

```typescript
import { 
    World, 
    WorldInitializeData, 
    BulletSystem, 
    LevelSystem, 
    PlayerSystem 
} from "./Game/world";

// Create world
const world = new World();

// Register systems
world.registerSystem(new BulletSystem());
world.registerSystem(new LevelSystem());
world.registerSystem(new PlayerSystem());

// Initialize
const initData = new WorldInitializeData();
initData.levelId = "my_level";
initData.modeId = GameMode.STORY;

await world.initialize(initData);
```

## Creating Custom Systems

```typescript
import { System } from "./Game/world";

export class MyCustomSystem extends System {
    getSystemName(): string {
        return "MyCustomSystem";
    }
    
    protected onInit(): void {
        console.log("Custom system initialized");
        // Initialize your system here
    }
    
    protected onUnInit(): void {
        console.log("Custom system cleaned up");
        // Cleanup your system here
    }
    
    protected onUpdate(deltaTime: number): void {
        // Update logic called every frame
    }
    
    protected onLateUpdate(deltaTime: number): void {
        // Late update logic called after all updates
    }
    
    // Add your custom methods here
    public doSomething(): void {
        // Custom functionality
    }
}

// Register to world
world.registerSystem(new MyCustomSystem());

// Access later
const mySystem = world.getSystem<MyCustomSystem>("MyCustomSystem");
mySystem?.doSomething();
```

## Included Example Systems

### BulletSystem
Manages bullets in the game world with features like:
- Bullet creation and destruction
- Movement and lifetime management
- Object pooling for performance
- Owner tracking

```typescript
const bulletSystem = world.getSystem<BulletSystem>("BulletSystem");
const bulletId = bulletSystem?.createBullet(
    position, 
    direction, 
    { speed: 500, damage: 10, lifetime: 3.0, bulletType: "player", size: 5 },
    "player_id"
);
```

### LevelSystem
Manages level state, objectives, and events:
- Level loading and progression
- Objective tracking
- Checkpoint system
- Event system for level events

```typescript
const levelSystem = world.getSystem<LevelSystem>("LevelSystem");
levelSystem?.addEventListener(LevelEventType.LEVEL_COMPLETE, (event) => {
    console.log("Level completed!", event.data);
});
```

### PlayerSystem
Manages player state, movement, and combat:
- Multiple player support
- Health and stats management
- Input processing
- State management (idle, moving, attacking, etc.)

```typescript
const playerSystem = world.getSystem<PlayerSystem>("PlayerSystem");
playerSystem?.createPlayer("player1", startPosition, { maxHealth: 100 });
playerSystem?.damagePlayer("player1", 25);
```

## Configuration

### WorldInitializeData Properties

```typescript
const initData = new WorldInitializeData();

// Basic settings
initData.modeId = GameMode.STORY;
initData.levelId = "level_001";
initData.difficulty = DifficultyLevel.NORMAL;
initData.randomSeed = 12345;

// Physics configuration
initData.physicsConfig = {
    gravity: -9.8,
    timeScale: 1.0,
    maxVelocity: 1000,
    enableCollision: true
};

// Rendering configuration
initData.renderConfig = {
    enableParticles: true,
    maxParticles: 1000,
    enablePostProcessing: true,
    renderScale: 1.0
};

// Debug settings
initData.debugFlags = {
    enableDebugDraw: true,
    showCollisionBounds: true,
    showPerformanceStats: true,
    logSystemUpdates: false
};
```

## World States

The World system manages several states:
- `UNINITIALIZED` - World not yet initialized
- `INITIALIZING` - World is being initialized
- `RUNNING` - World is actively updating
- `PAUSED` - World is paused
- `STOPPING` - World is being stopped
- `STOPPED` - World has stopped
- `ERROR` - World encountered an error

## Event Handling

```typescript
// World state changes
world.setOnStateChanged((oldState, newState) => {
    console.log(`World state: ${oldState} -> ${newState}`);
});

// World errors
world.setOnError((error) => {
    console.error("World error:", error);
});
```

## Best Practices

1. **Use Bootstrap Component**: For most use cases, use the Bootstrap component for easy setup
2. **System Independence**: Keep systems independent of each other when possible
3. **Resource Cleanup**: Always implement proper cleanup in `onUnInit()`
4. **Error Handling**: Handle errors gracefully in system update methods
5. **Performance**: Use object pooling for frequently created/destroyed objects
6. **State Management**: Use the world state system to control when systems should update

## Example Usage

See `examples/WorldExample.ts` for a complete working example that demonstrates:
- World setup and initialization
- System registration and usage
- Input handling
- Level management
- Player and bullet systems

## Integration with Existing Code

The World system is designed to work alongside existing game architecture:
- It follows the same patterns as the existing `IMgr` system
- Systems can interact with existing managers and components
- The Bootstrap component integrates seamlessly with Cocos Creator scenes

## Performance Considerations

- Systems only update when the world is in the `RUNNING` state
- Object pooling is used in example systems (BulletSystem)
- Late update is separated from regular update for optimization
- Systems can be enabled/disabled individually for performance tuning
