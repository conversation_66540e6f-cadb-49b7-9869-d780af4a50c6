{"version": "1.0.0", "format": "json", "timestamp": "2025-01-26T10:00:00.000Z", "data": {"metadata": {"name": "Basic Test Level", "version": "1.0.0", "duration": 180, "difficulty": 1, "description": "A basic test level with simple spawners and paths", "author": "Test Generator", "createdAt": "2025-01-26T10:00:00.000Z", "modifiedAt": "2025-01-26T10:00:00.000Z"}, "subLevels": [{"id": "sublevel_intro", "name": "Introduction Area", "bounds": {"x": 0, "y": 0, "width": 1920, "height": 2000}, "maps": [{"id": "bg_far_1", "prefabPath": "prefabs/backgrounds/clouds_far", "layerType": "BG_Far", "depth": -800, "scrollSpeed": {"x": 0, "y": 0.2}, "repeatMode": "vertical", "offset": {"x": 0, "y": 0}, "bounds": {"x": 0, "y": 0, "width": 1920, "height": 2000}, "isVisible": true}, {"id": "bg_close_1", "prefabPath": "prefabs/backgrounds/mountains", "layerType": "BG_Close", "depth": -400, "scrollSpeed": {"x": 0, "y": 0.5}, "repeatMode": "vertical", "offset": {"x": 0, "y": 0}, "bounds": {"x": 0, "y": 0, "width": 1920, "height": 2000}, "isVisible": true}], "spawners": [{"id": "spawner_basic_enemies", "name": "Basic Enemy Spawner", "position": {"x": 960, "y": 1800, "z": 0}, "prefabPath": "prefabs/enemies/basic_fighter", "spawnPattern": {"type": "sequential", "entities": [{"prefabPath": "prefabs/enemies/basic_fighter", "weight": 1.0}], "count": 5, "interval": 2.0, "delay": 1.0}, "pathId": "path_straight_down", "isActive": true, "waves": [{"id": "wave_1", "name": "First Wave", "startTime": 5.0, "endTime": 15.0, "spawnerConfigs": [{"spawnerId": "spawner_basic_enemies", "spawnTime": 5.0, "interval": 2.0}], "isActive": true}]}], "paths": [{"id": "path_straight_down", "name": "Straight Down Path", "points": [{"position": {"x": 960, "y": 1800, "z": 0}, "controlPoint1": {"x": 960, "y": 1700, "z": 0}, "controlPoint2": {"x": 960, "y": 1600, "z": 0}, "speed": 200, "rotation": 0}, {"position": {"x": 960, "y": -200, "z": 0}, "controlPoint1": {"x": 960, "y": -100, "z": 0}, "controlPoint2": {"x": 960, "y": 0, "z": 0}, "speed": 200, "rotation": 0}], "isLoop": false, "totalLength": 2000}], "events": {"nodes": {}, "connections": [], "triggers": []}, "entryPoint": {"position": {"x": 960, "y": 100, "z": 0}, "entryType": "smooth", "showCardSelection": false, "entryDelay": 1.0}, "exitPoint": {"position": {"x": 960, "y": 1900, "z": 0}, "exitType": "smooth", "showCardSelection": false, "exitDelay": 1.0}, "connections": [{"targetSubLevelId": "sublevel_main", "connectionType": "seamless", "triggerArea": {"x": 860, "y": 1850, "width": 200, "height": 100}}]}], "globalEvents": {"nodes": {}, "connections": [], "triggers": []}, "cameraSettings": {"viewportSize": {"width": 1920, "height": 1080}, "scrollSpeed": 100, "smoothing": 5.0, "bounds": {"x": 0, "y": 0, "width": 1920, "height": 2000}}}}