{"version": "1.0.0", "format": "json", "timestamp": "2025-01-26T10:00:00.000Z", "data": {"metadata": {"name": "Boss Battle Test Level", "version": "1.0.0", "duration": 240, "difficulty": 5, "description": "A boss battle level with multiple phases and complex spawning patterns", "author": "Test Generator", "createdAt": "2025-01-26T10:00:00.000Z", "modifiedAt": "2025-01-26T10:00:00.000Z"}, "subLevels": [{"id": "sublevel_boss_arena", "name": "Boss Arena", "bounds": {"x": 0, "y": 0, "width": 1920, "height": 1080}, "maps": [{"id": "bg_boss_arena", "prefabPath": "prefabs/backgrounds/boss_arena", "layerType": "BG_Close", "depth": -400, "scrollSpeed": {"x": 0, "y": 0}, "repeatMode": "none", "offset": {"x": 0, "y": 0}, "bounds": {"x": 0, "y": 0, "width": 1920, "height": 1080}, "isVisible": true}, {"id": "fg_effects", "prefabPath": "prefabs/effects/arena_particles", "layerType": "FG_VeryClose", "depth": 200, "scrollSpeed": {"x": 0, "y": 0}, "repeatMode": "none", "offset": {"x": 0, "y": 0}, "bounds": {"x": 0, "y": 0, "width": 1920, "height": 1080}, "isVisible": true}], "spawners": [{"id": "spawner_boss", "name": "Boss Spawner", "position": {"x": 960, "y": 900, "z": 0}, "prefabPath": "prefabs/bosses/mega_destroyer", "spawnPattern": {"type": "static", "entities": [{"prefabPath": "prefabs/bosses/mega_destroyer", "weight": 1.0}], "count": 1, "interval": 0, "delay": 3.0}, "pathId": "path_boss_movement", "isActive": true, "waves": [{"id": "wave_boss_spawn", "name": "Boss Spawn", "startTime": 5.0, "endTime": 8.0, "spawnerConfigs": [{"spawnerId": "spawner_boss", "spawnTime": 5.0}], "isActive": true}]}, {"id": "spawner_minions_phase1", "name": "Phase 1 Minions", "position": {"x": 200, "y": 800, "z": 0}, "prefabPath": "prefabs/enemies/drone", "spawnPattern": {"type": "random", "entities": [{"prefabPath": "prefabs/enemies/drone", "weight": 0.7}, {"prefabPath": "prefabs/enemies/interceptor", "weight": 0.3}], "count": 8, "interval": 1.5, "delay": 15.0}, "pathId": "path_minion_swarm", "isActive": true, "waves": [{"id": "wave_phase1_minions", "name": "Phase 1 Minions", "startTime": 20.0, "endTime": 40.0, "spawnerConfigs": [{"spawnerId": "spawner_minions_phase1", "spawnTime": 20.0, "interval": 1.5}], "isActive": true}]}, {"id": "spawner_heavy_phase2", "name": "Phase 2 Heavy Units", "position": {"x": 1720, "y": 800, "z": 0}, "prefabPath": "prefabs/enemies/heavy_bomber", "spawnPattern": {"type": "sequential", "entities": [{"prefabPath": "prefabs/enemies/heavy_bomber", "weight": 1.0}], "count": 4, "interval": 3.0, "delay": 60.0}, "pathId": "path_heavy_assault", "isActive": true, "waves": [{"id": "wave_phase2_heavy", "name": "Phase 2 Heavy Assault", "startTime": 80.0, "endTime": 120.0, "spawnerConfigs": [{"spawnerId": "spawner_heavy_phase2", "spawnTime": 80.0, "interval": 3.0}], "isActive": true}]}], "paths": [{"id": "path_boss_movement", "name": "Boss Movement Pattern", "points": [{"position": {"x": 960, "y": 900, "z": 0}, "controlPoint1": {"x": 960, "y": 850, "z": 0}, "controlPoint2": {"x": 1100, "y": 800, "z": 0}, "speed": 80, "rotation": 0}, {"position": {"x": 1400, "y": 700, "z": 0}, "controlPoint1": {"x": 1300, "y": 750, "z": 0}, "controlPoint2": {"x": 1400, "y": 600, "z": 0}, "speed": 60, "rotation": -10}, {"position": {"x": 1200, "y": 500, "z": 0}, "controlPoint1": {"x": 1300, "y": 550, "z": 0}, "controlPoint2": {"x": 1000, "y": 500, "z": 0}, "speed": 70, "rotation": 0}, {"position": {"x": 520, "y": 500, "z": 0}, "controlPoint1": {"x": 720, "y": 500, "z": 0}, "controlPoint2": {"x": 520, "y": 600, "z": 0}, "speed": 70, "rotation": 10}, {"position": {"x": 720, "y": 700, "z": 0}, "controlPoint1": {"x": 620, "y": 650, "z": 0}, "controlPoint2": {"x": 860, "y": 750, "z": 0}, "speed": 60, "rotation": 0}], "isLoop": true, "totalLength": 1800}, {"id": "path_minion_swarm", "name": "Minion Swarm Path", "points": [{"position": {"x": 200, "y": 800, "z": 0}, "controlPoint1": {"x": 300, "y": 750, "z": 0}, "controlPoint2": {"x": 500, "y": 600, "z": 0}, "speed": 120, "rotation": 0}, {"position": {"x": 800, "y": 400, "z": 0}, "controlPoint1": {"x": 700, "y": 500, "z": 0}, "controlPoint2": {"x": 900, "y": 300, "z": 0}, "speed": 140, "rotation": -20}, {"position": {"x": 1200, "y": 200, "z": 0}, "controlPoint1": {"x": 1100, "y": 250, "z": 0}, "controlPoint2": {"x": 1300, "y": 150, "z": 0}, "speed": 160, "rotation": 0}], "isLoop": false, "totalLength": 1200}, {"id": "path_heavy_assault", "name": "Heavy Assault Path", "points": [{"position": {"x": 1720, "y": 800, "z": 0}, "controlPoint1": {"x": 1620, "y": 750, "z": 0}, "controlPoint2": {"x": 1400, "y": 650, "z": 0}, "speed": 100, "rotation": 0}, {"position": {"x": 960, "y": 400, "z": 0}, "controlPoint1": {"x": 1100, "y": 500, "z": 0}, "controlPoint2": {"x": 820, "y": 300, "z": 0}, "speed": 80, "rotation": 15}, {"position": {"x": 200, "y": 200, "z": 0}, "controlPoint1": {"x": 400, "y": 250, "z": 0}, "controlPoint2": {"x": 100, "y": 150, "z": 0}, "speed": 120, "rotation": 0}], "isLoop": false, "totalLength": 1600}], "events": {"nodes": {}, "connections": [], "triggers": [{"id": "trigger_boss_phase_2", "type": "condition", "condition": {"bossHealthPercent": 50}, "targetNodeId": "node_phase_2_start", "isActive": true}, {"id": "trigger_boss_defeated", "type": "condition", "condition": {"bossHealthPercent": 0}, "targetNodeId": "node_victory", "isActive": true}]}, "entryPoint": {"position": {"x": 960, "y": 100, "z": 0}, "entryType": "fade", "showCardSelection": true, "entryAnimation": "dramatic_entrance", "entryDelay": 3.0}, "connections": []}], "globalEvents": {"nodes": {}, "connections": [], "triggers": [{"id": "trigger_boss_music", "type": "time", "condition": {"timeReached": 2}, "targetNodeId": "node_boss_music_start", "isActive": true}]}, "cameraSettings": {"viewportSize": {"width": 1920, "height": 1080}, "scrollSpeed": 0, "followTarget": "boss", "smoothing": 2.0, "bounds": {"x": 0, "y": 0, "width": 1920, "height": 1080}}}}